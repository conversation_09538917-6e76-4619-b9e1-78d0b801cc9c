{"version": 3, "file": "index-CWFnNnBI.js", "sources": ["../../src/pages/UserManagement/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Tag,\n  Modal,\n  Form,\n  Input,\n  Select,\n  Switch,\n  message,\n  Popconfirm,\n  Row,\n  Col,\n  Statistic,\n} from 'antd';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  UserOutlined,\n  TeamOutlined,\n  CrownOutlined,\n  EyeOutlined,\n} from '@ant-design/icons';\nimport { UserInfo, UserRole, CreateUserRequest, UpdateUserRequest } from '@/types';\nimport { authApi } from '@/api';\nimport { useAuthStore } from '@/store/auth';\n\nconst { Option } = Select;\n\nconst UserManagement: React.FC = () => {\n  const [users, setUsers] = useState<UserInfo[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingUser, setEditingUser] = useState<UserInfo | null>(null);\n  const [form] = Form.useForm();\n  const [pagination, setPagination] = useState({\n    current: 1,\n    pageSize: 20,\n    total: 0,\n  });\n\n  const { user: currentUser } = useAuthStore();\n\n  const fetchUsers = async (page = 1, size = 20) => {\n    setLoading(true);\n    try {\n      const response = await authApi.getUsers(page, size);\n      if (response.data.success && response.data.data) {\n        setUsers(response.data.data);\n        setPagination({\n          current: page,\n          pageSize: size,\n          total: response.data.data.length,\n        });\n      }\n    } catch (error) {\n      message.error('获取用户列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchUsers();\n  }, []);\n\n  const handleCreateUser = () => {\n    setEditingUser(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEditUser = (user: UserInfo) => {\n    setEditingUser(user);\n    form.setFieldsValue({\n      username: user.username,\n      email: user.email,\n      role: user.role,\n      is_active: user.is_active,\n    });\n    setModalVisible(true);\n  };\n\n  const handleDeleteUser = async (userId: number) => {\n    try {\n      await authApi.deleteUser(userId);\n      message.success('删除用户成功');\n      fetchUsers(pagination.current, pagination.pageSize);\n    } catch (error) {\n      message.error('删除用户失败');\n    }\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      \n      if (editingUser) {\n        // 更新用户\n        const updateData: UpdateUserRequest = {\n          username: values.username,\n          email: values.email,\n          role: values.role,\n          is_active: values.is_active,\n        };\n        await authApi.updateUser(editingUser.id, updateData);\n        message.success('更新用户成功');\n      } else {\n        // 创建用户\n        const createData: CreateUserRequest = {\n          username: values.username,\n          email: values.email,\n          password: values.password,\n          role: values.role,\n        };\n        await authApi.register(createData);\n        message.success('创建用户成功');\n      }\n      \n      setModalVisible(false);\n      fetchUsers(pagination.current, pagination.pageSize);\n    } catch (error: any) {\n      message.error(error.response?.data?.error || '操作失败');\n    }\n  };\n\n  const getRoleTag = (role: UserRole) => {\n    const roleConfig = {\n      [UserRole.Admin]: { color: 'red', icon: <CrownOutlined /> },\n      [UserRole.User]: { color: 'blue', icon: <UserOutlined /> },\n      [UserRole.Viewer]: { color: 'green', icon: <EyeOutlined /> },\n    };\n    \n    const config = roleConfig[role];\n    return (\n      <Tag color={config.color} icon={config.icon}>\n        {role === UserRole.Admin ? '管理员' : role === UserRole.User ? '用户' : '观察者'}\n      </Tag>\n    );\n  };\n\n  const columns = [\n    {\n      title: 'ID',\n      dataIndex: 'id',\n      key: 'id',\n      width: 80,\n    },\n    {\n      title: '用户名',\n      dataIndex: 'username',\n      key: 'username',\n    },\n    {\n      title: '邮箱',\n      dataIndex: 'email',\n      key: 'email',\n    },\n    {\n      title: '角色',\n      dataIndex: 'role',\n      key: 'role',\n      render: (role: UserRole) => getRoleTag(role),\n    },\n    {\n      title: '状态',\n      dataIndex: 'is_active',\n      key: 'is_active',\n      render: (active: boolean) => (\n        <Tag color={active ? 'green' : 'red'}>\n          {active ? '活跃' : '禁用'}\n        </Tag>\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'created_at',\n      key: 'created_at',\n      render: (date: string) => new Date(date).toLocaleString(),\n    },\n    {\n      title: '最后登录',\n      dataIndex: 'last_login',\n      key: 'last_login',\n      render: (date: string) => date ? new Date(date).toLocaleString() : '从未登录',\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record: UserInfo) => (\n        <Space>\n          <Button\n            type=\"link\"\n            icon={<EditOutlined />}\n            onClick={() => handleEditUser(record)}\n          >\n            编辑\n          </Button>\n          {record.id !== currentUser?.id && (\n            <Popconfirm\n              title=\"确定要删除这个用户吗？\"\n              onConfirm={() => handleDeleteUser(record.id)}\n              okText=\"确定\"\n              cancelText=\"取消\"\n            >\n              <Button\n                type=\"link\"\n                danger\n                icon={<DeleteOutlined />}\n              >\n                删除\n              </Button>\n            </Popconfirm>\n          )}\n        </Space>\n      ),\n    },\n  ];\n\n  // 统计数据\n  const adminCount = users.filter(u => u.role === UserRole.Admin).length;\n  const userCount = users.filter(u => u.role === UserRole.User).length;\n  const viewerCount = users.filter(u => u.role === UserRole.Viewer).length;\n  const activeCount = users.filter(u => u.is_active).length;\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <Row gutter={16} style={{ marginBottom: '24px' }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总用户数\"\n              value={pagination.total}\n              prefix={<TeamOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"管理员\"\n              value={adminCount}\n              prefix={<CrownOutlined />}\n              valueStyle={{ color: '#cf1322' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"普通用户\"\n              value={userCount}\n              prefix={<UserOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"活跃用户\"\n              value={activeCount}\n              prefix={<UserOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card\n        title=\"用户管理\"\n        extra={\n          <Button\n            type=\"primary\"\n            icon={<PlusOutlined />}\n            onClick={handleCreateUser}\n          >\n            新建用户\n          </Button>\n        }\n      >\n        <Table\n          dataSource={users}\n          columns={columns}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            ...pagination,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,\n            onChange: (page, size) => {\n              fetchUsers(page, size);\n            },\n          }}\n        />\n      </Card>\n\n      <Modal\n        title={editingUser ? '编辑用户' : '新建用户'}\n        open={modalVisible}\n        onOk={handleModalOk}\n        onCancel={() => setModalVisible(false)}\n        destroyOnClose\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          preserve={false}\n        >\n          <Form.Item\n            label=\"用户名\"\n            name=\"username\"\n            rules={[\n              { required: true, message: '请输入用户名' },\n              { min: 3, max: 50, message: '用户名长度为3-50个字符' },\n            ]}\n          >\n            <Input placeholder=\"请输入用户名\" />\n          </Form.Item>\n\n          <Form.Item\n            label=\"邮箱\"\n            name=\"email\"\n            rules={[\n              { required: true, message: '请输入邮箱' },\n              { type: 'email', message: '请输入有效的邮箱地址' },\n            ]}\n          >\n            <Input placeholder=\"请输入邮箱\" />\n          </Form.Item>\n\n          {!editingUser && (\n            <Form.Item\n              label=\"密码\"\n              name=\"password\"\n              rules={[\n                { required: true, message: '请输入密码' },\n                { min: 6, max: 100, message: '密码长度为6-100个字符' },\n              ]}\n            >\n              <Input.Password placeholder=\"请输入密码\" />\n            </Form.Item>\n          )}\n\n          <Form.Item\n            label=\"角色\"\n            name=\"role\"\n            rules={[{ required: true, message: '请选择角色' }]}\n          >\n            <Select placeholder=\"请选择角色\">\n              <Option value={UserRole.Viewer}>观察者</Option>\n              <Option value={UserRole.User}>用户</Option>\n              <Option value={UserRole.Admin}>管理员</Option>\n            </Select>\n          </Form.Item>\n\n          {editingUser && (\n            <Form.Item\n              label=\"状态\"\n              name=\"is_active\"\n              valuePropName=\"checked\"\n            >\n              <Switch\n                checkedChildren=\"活跃\"\n                unCheckedChildren=\"禁用\"\n              />\n            </Form.Item>\n          )}\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default UserManagement;"], "names": ["Option", "Select", "UserManagement", "users", "setUsers", "useState", "loading", "setLoading", "modalVisible", "setModalVisible", "editingUser", "setEditingUser", "form", "Form", "pagination", "setPagination", "currentUser", "useAuthStore", "fetchUsers", "page", "size", "response", "authApi", "message", "useEffect", "handleCreateUser", "handleEditUser", "user", "handleDeleteUser", "userId", "handleModalOk", "values", "updateData", "createData", "error", "_b", "_a", "getRoleTag", "role", "config", "UserRole", "jsx", "CrownOutlined", "UserOutlined", "EyeOutlined", "Tag", "columns", "active", "date", "_", "record", "Space", "<PERSON><PERSON>", "EditOutlined", "Popconfirm", "DeleteOutlined", "adminCount", "u", "userCount", "activeCount", "jsxs", "Row", "Col", "Card", "Statistic", "TeamOutlined", "PlusOutlined", "Table", "total", "range", "Modal", "Input", "Switch"], "mappings": "2TA+BA,KAAM,CAAE,OAAAA,GAAWC,EAEbC,GAA2B,IAAM,CACrC,KAAM,CAACC,EAAOC,CAAQ,EAAIC,EAAAA,SAAqB,CAAA,CAAE,EAC3C,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAK,EACtC,CAACG,EAAcC,CAAe,EAAIJ,EAAAA,SAAS,EAAK,EAChD,CAACK,EAAaC,CAAc,EAAIN,EAAAA,SAA0B,IAAI,EAC9D,CAACO,CAAI,EAAIC,EAAK,QAAA,EACd,CAACC,EAAYC,CAAa,EAAIV,WAAS,CAC3C,QAAS,EACT,SAAU,GACV,MAAO,CAAA,CACR,EAEK,CAAE,KAAMW,CAAA,EAAgBC,EAAA,EAExBC,EAAa,MAAOC,EAAO,EAAGC,EAAO,KAAO,CAChDb,EAAW,EAAI,EACf,GAAI,CACF,MAAMc,EAAW,MAAMC,EAAQ,SAASH,EAAMC,CAAI,EAC9CC,EAAS,KAAK,SAAWA,EAAS,KAAK,OACzCjB,EAASiB,EAAS,KAAK,IAAI,EAC3BN,EAAc,CACZ,QAASI,EACT,SAAUC,EACV,MAAOC,EAAS,KAAK,KAAK,MAAA,CAC3B,EAEL,MAAgB,CACdE,EAAQ,MAAM,UAAU,CAC1B,QAAA,CACEhB,EAAW,EAAK,CAClB,CACF,EAEAiB,EAAAA,UAAU,IAAM,CACdN,EAAA,CACF,EAAG,CAAA,CAAE,EAEL,MAAMO,EAAmB,IAAM,CAC7Bd,EAAe,IAAI,EACnBC,EAAK,YAAA,EACLH,EAAgB,EAAI,CACtB,EAEMiB,EAAkBC,GAAmB,CACzChB,EAAegB,CAAI,EACnBf,EAAK,eAAe,CAClB,SAAUe,EAAK,SACf,MAAOA,EAAK,MACZ,KAAMA,EAAK,KACX,UAAWA,EAAK,SAAA,CACjB,EACDlB,EAAgB,EAAI,CACtB,EAEMmB,EAAmB,MAAOC,GAAmB,CACjD,GAAI,CACF,MAAMP,EAAQ,WAAWO,CAAM,EAC/BN,EAAQ,QAAQ,QAAQ,EACxBL,EAAWJ,EAAW,QAASA,EAAW,QAAQ,CACpD,MAAgB,CACdS,EAAQ,MAAM,QAAQ,CACxB,CACF,EAEMO,EAAgB,SAAY,SAChC,GAAI,CACF,MAAMC,EAAS,MAAMnB,EAAK,eAAA,EAE1B,GAAIF,EAAa,CAEf,MAAMsB,EAAgC,CACpC,SAAUD,EAAO,SACjB,MAAOA,EAAO,MACd,KAAMA,EAAO,KACb,UAAWA,EAAO,SAAA,EAEpB,MAAMT,EAAQ,WAAWZ,EAAY,GAAIsB,CAAU,EACnDT,EAAQ,QAAQ,QAAQ,CAC1B,KAAO,CAEL,MAAMU,EAAgC,CACpC,SAAUF,EAAO,SACjB,MAAOA,EAAO,MACd,SAAUA,EAAO,SACjB,KAAMA,EAAO,IAAA,EAEf,MAAMT,EAAQ,SAASW,CAAU,EACjCV,EAAQ,QAAQ,QAAQ,CAC1B,CAEAd,EAAgB,EAAK,EACrBS,EAAWJ,EAAW,QAASA,EAAW,QAAQ,CACpD,OAASoB,EAAY,CACnBX,EAAQ,QAAMY,GAAAC,EAAAF,EAAM,WAAN,YAAAE,EAAgB,OAAhB,YAAAD,EAAsB,QAAS,MAAM,CACrD,CACF,EAEME,EAAcC,GAAmB,CAOrC,MAAMC,EANa,CACjB,CAACC,EAAS,KAAK,EAAG,CAAE,MAAO,MAAO,KAAMC,EAAAA,IAACC,EAAA,CAAA,CAAc,CAAA,EACvD,CAACF,EAAS,IAAI,EAAG,CAAE,MAAO,OAAQ,KAAMC,EAAAA,IAACE,EAAA,CAAA,CAAa,CAAA,EACtD,CAACH,EAAS,MAAM,EAAG,CAAE,MAAO,QAAS,KAAMC,EAAAA,IAACG,EAAA,EAAY,CAAA,CAAG,EAGnCN,CAAI,EAC9B,aACGO,EAAA,CAAI,MAAON,EAAO,MAAO,KAAMA,EAAO,KACpC,SAAAD,IAASE,EAAS,MAAQ,MAAQF,IAASE,EAAS,KAAO,KAAO,MACrE,CAEJ,EAEMM,EAAU,CACd,CACE,MAAO,KACP,UAAW,KACX,IAAK,KACL,MAAO,EAAA,EAET,CACE,MAAO,MACP,UAAW,WACX,IAAK,UAAA,EAEP,CACE,MAAO,KACP,UAAW,QACX,IAAK,OAAA,EAEP,CACE,MAAO,KACP,UAAW,OACX,IAAK,OACL,OAASR,GAAmBD,EAAWC,CAAI,CAAA,EAE7C,CACE,MAAO,KACP,UAAW,YACX,IAAK,YACL,OAASS,GACPN,MAACI,EAAA,CAAI,MAAOE,EAAS,QAAU,MAC5B,SAAAA,EAAS,KAAO,IAAA,CACnB,CAAA,EAGJ,CACE,MAAO,OACP,UAAW,aACX,IAAK,aACL,OAASC,GAAiB,IAAI,KAAKA,CAAI,EAAE,eAAA,CAAe,EAE1D,CACE,MAAO,OACP,UAAW,aACX,IAAK,aACL,OAASA,GAAiBA,EAAO,IAAI,KAAKA,CAAI,EAAE,iBAAmB,MAAA,EAErE,CACE,MAAO,KACP,IAAK,SACL,OAAQ,CAACC,EAAGC,WACTC,EAAA,CACC,SAAA,CAAAV,EAAAA,IAACW,EAAA,CACC,KAAK,OACL,WAAOC,EAAA,EAAa,EACpB,QAAS,IAAM3B,EAAewB,CAAM,EACrC,SAAA,IAAA,CAAA,EAGAA,EAAO,MAAOlC,GAAA,YAAAA,EAAa,KAC1ByB,EAAAA,IAACa,EAAA,CACC,MAAM,cACN,UAAW,IAAM1B,EAAiBsB,EAAO,EAAE,EAC3C,OAAO,KACP,WAAW,KAEX,SAAAT,EAAAA,IAACW,EAAA,CACC,KAAK,OACL,OAAM,GACN,WAAOG,EAAA,EAAe,EACvB,SAAA,IAAA,CAAA,CAED,CAAA,CACF,CAAA,CAEJ,CAAA,CAEJ,EAIIC,EAAarD,EAAM,OAAOsD,GAAKA,EAAE,OAASjB,EAAS,KAAK,EAAE,OAC1DkB,EAAYvD,EAAM,OAAOsD,GAAKA,EAAE,OAASjB,EAAS,IAAI,EAAE,OAC1CrC,EAAM,OAAOsD,GAAKA,EAAE,OAASjB,EAAS,MAAM,EAAE,OAClE,MAAMmB,EAAcxD,EAAM,OAAOsD,GAAKA,EAAE,SAAS,EAAE,OAEnD,cACG,MAAA,CAAI,MAAO,CAAE,QAAS,QACrB,SAAA,CAAAG,OAACC,GAAI,OAAQ,GAAI,MAAO,CAAE,aAAc,QACtC,SAAA,CAAApB,MAACqB,EAAA,CAAI,KAAM,EACT,SAAArB,MAACsB,EAAA,CACC,SAAAtB,EAAAA,IAACuB,EAAA,CACC,MAAM,OACN,MAAOlD,EAAW,MAClB,aAASmD,EAAA,CAAA,CAAa,CAAA,CAAA,EAE1B,CAAA,CACF,EACAxB,MAACqB,EAAA,CAAI,KAAM,EACT,eAACC,EAAA,CACC,SAAAtB,EAAAA,IAACuB,EAAA,CACC,MAAM,MACN,MAAOR,EACP,aAASd,EAAA,EAAc,EACvB,WAAY,CAAE,MAAO,SAAA,CAAU,CAAA,EAEnC,CAAA,CACF,EACAD,MAACqB,EAAA,CAAI,KAAM,EACT,eAACC,EAAA,CACC,SAAAtB,EAAAA,IAACuB,EAAA,CACC,MAAM,OACN,MAAON,EACP,aAASf,EAAA,EAAa,EACtB,WAAY,CAAE,MAAO,SAAA,CAAU,CAAA,EAEnC,CAAA,CACF,EACAF,MAACqB,EAAA,CAAI,KAAM,EACT,eAACC,EAAA,CACC,SAAAtB,EAAAA,IAACuB,EAAA,CACC,MAAM,OACN,MAAOL,EACP,aAAShB,EAAA,EAAa,EACtB,WAAY,CAAE,MAAO,SAAA,CAAU,CAAA,EAEnC,CAAA,CACF,CAAA,EACF,EAEAF,EAAAA,IAACsB,EAAA,CACC,MAAM,OACN,MACEtB,EAAAA,IAACW,EAAA,CACC,KAAK,UACL,WAAOc,EAAA,EAAa,EACpB,QAASzC,EACV,SAAA,MAAA,CAAA,EAKH,SAAAgB,EAAAA,IAAC0B,EAAA,CACC,WAAYhE,EACZ,QAAA2C,EACA,OAAO,KACP,QAAAxC,EACA,WAAY,CACV,GAAGQ,EACH,gBAAiB,GACjB,gBAAiB,GACjB,UAAW,CAACsD,EAAOC,IACjB,KAAKA,EAAM,CAAC,CAAC,IAAIA,EAAM,CAAC,CAAC,QAAQD,CAAK,KACxC,SAAU,CAACjD,EAAMC,IAAS,CACxBF,EAAWC,EAAMC,CAAI,CACvB,CAAA,CACF,CAAA,CACF,CAAA,EAGFqB,EAAAA,IAAC6B,EAAA,CACC,MAAO5D,EAAc,OAAS,OAC9B,KAAMF,EACN,KAAMsB,EACN,SAAU,IAAMrB,EAAgB,EAAK,EACrC,eAAc,GAEd,SAAAmD,EAAAA,KAAC/C,EAAA,CACC,KAAAD,EACA,OAAO,WACP,SAAU,GAEV,SAAA,CAAA6B,EAAAA,IAAC5B,EAAK,KAAL,CACC,MAAM,MACN,KAAK,WACL,MAAO,CACL,CAAE,SAAU,GAAM,QAAS,QAAA,EAC3B,CAAE,IAAK,EAAG,IAAK,GAAI,QAAS,eAAA,CAAgB,EAG9C,SAAA4B,EAAAA,IAAC8B,EAAA,CAAM,YAAY,QAAA,CAAS,CAAA,CAAA,EAG9B9B,EAAAA,IAAC5B,EAAK,KAAL,CACC,MAAM,KACN,KAAK,QACL,MAAO,CACL,CAAE,SAAU,GAAM,QAAS,OAAA,EAC3B,CAAE,KAAM,QAAS,QAAS,YAAA,CAAa,EAGzC,SAAA4B,EAAAA,IAAC8B,EAAA,CAAM,YAAY,OAAA,CAAQ,CAAA,CAAA,EAG5B,CAAC7D,GACA+B,EAAAA,IAAC5B,EAAK,KAAL,CACC,MAAM,KACN,KAAK,WACL,MAAO,CACL,CAAE,SAAU,GAAM,QAAS,OAAA,EAC3B,CAAE,IAAK,EAAG,IAAK,IAAK,QAAS,eAAA,CAAgB,EAG/C,SAAA4B,EAAAA,IAAC8B,EAAM,SAAN,CAAe,YAAY,OAAA,CAAQ,CAAA,CAAA,EAIxC9B,EAAAA,IAAC5B,EAAK,KAAL,CACC,MAAM,KACN,KAAK,OACL,MAAO,CAAC,CAAE,SAAU,GAAM,QAAS,QAAS,EAE5C,SAAA+C,EAAAA,KAAC3D,EAAA,CAAO,YAAY,QAClB,SAAA,CAAAwC,EAAAA,IAACzC,EAAA,CAAO,MAAOwC,EAAS,OAAQ,SAAA,MAAG,EACnCC,EAAAA,IAACzC,EAAA,CAAO,MAAOwC,EAAS,KAAM,SAAA,KAAE,EAChCC,EAAAA,IAACzC,EAAA,CAAO,MAAOwC,EAAS,MAAO,SAAA,KAAA,CAAG,CAAA,CAAA,CACpC,CAAA,CAAA,EAGD9B,GACC+B,EAAAA,IAAC5B,EAAK,KAAL,CACC,MAAM,KACN,KAAK,YACL,cAAc,UAEd,SAAA4B,EAAAA,IAAC+B,EAAA,CACC,gBAAgB,KAChB,kBAAkB,IAAA,CAAA,CACpB,CAAA,CACF,CAAA,CAAA,CAEJ,CAAA,CACF,EACF,CAEJ"}