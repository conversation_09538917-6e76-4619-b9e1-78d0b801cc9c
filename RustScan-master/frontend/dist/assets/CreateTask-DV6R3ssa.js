import{u as N,S as l,j as e,U as o,d as P,e as C,t as A}from"./index-z9zCfJ11.js";import{r as y}from"./vendor-o6zXO7vr.js";import{u as D}from"./useMutation-i6kbUsZB.js";import{Y as F,F as t,b as d,S as i,B as u,a6 as R,T as B,R as p,C as a,I as k,a1 as _,a7 as U,a8 as c,a9 as m,aa as W,a2 as M}from"./ui-Smnqh0BA.js";import"./charts-CfM58meh.js";const{Title:$,Text:r}=B,{Option:n}=_,{TextArea:H}=k,J=()=>{const j=N(),{message:g}=F.useApp(),[I]=t.useForm(),[z,f]=y.useState(l.Standard),[h,v]=y.useState(!1),b=D({mutationFn:A.createTask,onSuccess:s=>{var x;g.success("任务创建成功"),j(`/tasks/${(x=s.data.data)==null?void 0:x.id}`)},onError:s=>{g.error("创建任务失败: "+s.message)}}),w=s=>{const x=h?{scan_mode:s.scan_mode,target_type:s.target_type,enable_port_scan:s.enable_port_scan,enable_service_detection:s.enable_service_detection,enable_dns_resolution:s.enable_dns_resolution,enable_subdomain_enum:s.enable_subdomain_enum,enable_web_crawling:s.enable_web_crawling,enable_vulnerability_scan:s.enable_vulnerability_scan,max_parallel_tasks:s.max_parallel_tasks,timeout_per_step:s.timeout_per_step,max_retries:s.max_retries,continue_on_error:s.continue_on_error,nmap_config:{scan_type:s.nmap_scan_type,timing:s.nmap_timing,threads:s.nmap_threads,host_timeout:s.nmap_host_timeout,enable_service_detection:s.nmap_enable_service_detection,enable_os_detection:s.nmap_enable_os_detection,stealth_mode:s.nmap_stealth_mode,aggressive_scan:s.nmap_aggressive_scan},httpx_config:{threads:s.httpx_threads,timeout:s.httpx_timeout,retries:s.httpx_retries,user_agent_type:s.httpx_user_agent_type,follow_redirects:s.httpx_follow_redirects,tech_detect:s.httpx_tech_detect,screenshot:s.httpx_screenshot},nuclei_config:{threads:s.nuclei_threads,rate_limit:s.nuclei_rate_limit,timeout:s.nuclei_timeout,severity:s.nuclei_severity||["medium","high","critical"],templates:s.nuclei_templates||["cves/","vulnerabilities/"],user_agent_type:s.nuclei_user_agent_type,update_templates:s.nuclei_update_templates,passive_scan:s.nuclei_passive_scan}}:void 0,S={name:`${s.task_type}扫描 - ${s.target.trim()}`,target:s.target.trim(),task_type:s.task_type,config:x?JSON.stringify(x):void 0,total_steps:7};b.mutate(S)},T={[l.Quick]:"快速扫描常见端口和高危漏洞，适合快速评估",[l.Standard]:"标准扫描，平衡速度和覆盖率",[l.Deep]:"深度扫描所有端口和漏洞，时间较长但最全面",[l.WebFocused]:"专注于Web应用扫描，包含爬虫和Web漏洞检测",[l.Custom]:"自定义扫描配置，可精确控制扫描参数"};return e.jsx("div",{style:{padding:24},children:e.jsxs(d,{children:[e.jsx("div",{style:{marginBottom:24},children:e.jsxs(i,{children:[e.jsx(u,{icon:e.jsx(R,{}),onClick:()=>j("/tasks"),children:"返回"}),e.jsxs("div",{children:[e.jsx($,{level:3,style:{margin:0},children:"创建扫描任务"}),e.jsx(r,{type:"secondary",children:"配置扫描目标和参数，开始安全扫描"})]})]})}),e.jsxs(t,{form:I,layout:"vertical",onFinish:w,initialValues:{task_type:l.Standard,scan_mode:C.Standard,target_type:P.Mixed,enable_port_scan:!0,enable_service_detection:!0,enable_dns_resolution:!0,enable_subdomain_enum:!0,enable_web_crawling:!0,enable_vulnerability_scan:!0,max_parallel_tasks:3,timeout_per_step:1800,max_retries:2,continue_on_error:!0,nmap_scan_type:"SYN",nmap_timing:4,nmap_threads:50,nmap_host_timeout:300,nmap_enable_service_detection:!0,nmap_enable_os_detection:!1,nmap_stealth_mode:!1,nmap_aggressive_scan:!1,httpx_threads:50,httpx_timeout:10,httpx_retries:2,httpx_user_agent_type:o.Desktop,httpx_follow_redirects:!0,httpx_tech_detect:!0,httpx_screenshot:!1,nuclei_threads:25,nuclei_rate_limit:150,nuclei_timeout:10,nuclei_user_agent_type:o.Desktop,nuclei_update_templates:!1,nuclei_passive_scan:!1},children:[e.jsxs(p,{gutter:24,children:[e.jsxs(a,{xs:24,lg:12,children:[e.jsxs(d,{title:"基础配置",size:"small",style:{marginBottom:16},children:[e.jsx(t.Item,{label:"扫描目标",name:"target",rules:[{required:!0,message:"请输入扫描目标"},{pattern:/^(https?:\/\/|[\w.-]+|[\d.]+|[\da-fA-F:]+).*$/,message:"请输入有效的URL、域名或IP地址"}],children:e.jsx(k,{placeholder:"例如: example.com, ***********, https://example.com",size:"large"})}),e.jsx(t.Item,{label:"扫描类型",name:"task_type",rules:[{required:!0,message:"请选择扫描类型"}],children:e.jsxs(_,{size:"large",onChange:s=>f(s),children:[e.jsx(n,{value:l.Quick,children:"快速扫描"}),e.jsx(n,{value:l.Standard,children:"标准扫描"}),e.jsx(n,{value:l.Deep,children:"深度扫描"}),e.jsx(n,{value:l.WebFocused,children:"Web专项扫描"}),e.jsx(n,{value:l.Custom,children:"自定义扫描"})]})}),e.jsx(U,{message:T[z],type:"info",showIcon:!0,style:{marginBottom:16}})]}),e.jsx(d,{title:"配置选项",size:"small",children:e.jsx(t.Item,{children:e.jsxs(i,{children:[e.jsx(c,{checked:h,onChange:v}),e.jsx(r,{children:"显示高级配置"})]})})})]}),e.jsxs(a,{xs:24,lg:12,children:[h&&e.jsx(d,{title:"扫描模块",size:"small",style:{marginBottom:16},children:e.jsxs(p,{gutter:16,children:[e.jsx(a,{span:12,children:e.jsx(t.Item,{name:"enable_port_scan",valuePropName:"checked",children:e.jsxs(i,{children:[e.jsx(c,{size:"small"}),e.jsx(r,{children:"端口扫描"})]})})}),e.jsx(a,{span:12,children:e.jsx(t.Item,{name:"enable_service_detection",valuePropName:"checked",children:e.jsxs(i,{children:[e.jsx(c,{size:"small"}),e.jsx(r,{children:"服务检测"})]})})}),e.jsx(a,{span:12,children:e.jsx(t.Item,{name:"enable_dns_resolution",valuePropName:"checked",children:e.jsxs(i,{children:[e.jsx(c,{size:"small"}),e.jsx(r,{children:"DNS解析"})]})})}),e.jsx(a,{span:12,children:e.jsx(t.Item,{name:"enable_subdomain_enum",valuePropName:"checked",children:e.jsxs(i,{children:[e.jsx(c,{size:"small"}),e.jsx(r,{children:"子域名枚举"})]})})}),e.jsx(a,{span:12,children:e.jsx(t.Item,{name:"enable_web_crawling",valuePropName:"checked",children:e.jsxs(i,{children:[e.jsx(c,{size:"small"}),e.jsx(r,{children:"Web爬虫"})]})})}),e.jsx(a,{span:12,children:e.jsx(t.Item,{name:"enable_vulnerability_scan",valuePropName:"checked",children:e.jsxs(i,{children:[e.jsx(c,{size:"small"}),e.jsx(r,{children:"漏洞扫描"})]})})})]})}),h&&e.jsx(d,{title:"全局参数",size:"small",children:e.jsxs(p,{gutter:16,children:[e.jsx(a,{span:12,children:e.jsx(t.Item,{label:"并行任务数",name:"max_parallel_tasks",children:e.jsx(m,{min:1,max:10,style:{width:"100%"}})})}),e.jsx(a,{span:12,children:e.jsx(t.Item,{label:"步骤超时(秒)",name:"timeout_per_step",children:e.jsx(m,{min:300,max:7200,style:{width:"100%"}})})}),e.jsx(a,{span:12,children:e.jsx(t.Item,{label:"最大重试次数",name:"max_retries",children:e.jsx(m,{min:0,max:5,style:{width:"100%"}})})}),e.jsx(a,{span:12,children:e.jsx(t.Item,{name:"continue_on_error",valuePropName:"checked",children:e.jsxs(i,{children:[e.jsx(c,{size:"small"}),e.jsx(r,{children:"出错时继续"})]})})})]})})]})]}),h&&e.jsxs(e.Fragment,{children:[e.jsx(W,{children:"工具配置"}),e.jsxs(p,{gutter:24,children:[e.jsx(a,{xs:24,lg:8,children:e.jsxs(d,{title:"Nmap配置",size:"small",children:[e.jsx(t.Item,{label:"扫描类型",name:"nmap_scan_type",children:e.jsxs(_,{size:"small",children:[e.jsx(n,{value:"SYN",children:"SYN扫描"}),e.jsx(n,{value:"TCP",children:"TCP连接扫描"}),e.jsx(n,{value:"UDP",children:"UDP扫描"})]})}),e.jsx(t.Item,{label:"时序模板",name:"nmap_timing",children:e.jsxs(_,{size:"small",children:[e.jsx(n,{value:3,children:"正常 (T3)"}),e.jsx(n,{value:4,children:"快速 (T4)"}),e.jsx(n,{value:5,children:"急速 (T5)"})]})}),e.jsx(t.Item,{label:"线程数",name:"nmap_threads",children:e.jsx(m,{min:1,max:100,size:"small",style:{width:"100%"}})}),e.jsx(t.Item,{name:"nmap_stealth_mode",valuePropName:"checked",children:e.jsxs(i,{children:[e.jsx(c,{size:"small"}),e.jsx(r,{children:"隐蔽模式"})]})})]})}),e.jsx(a,{xs:24,lg:8,children:e.jsxs(d,{title:"HTTPx配置",size:"small",children:[e.jsx(t.Item,{label:"线程数",name:"httpx_threads",children:e.jsx(m,{min:1,max:200,size:"small",style:{width:"100%"}})}),e.jsx(t.Item,{label:"超时时间",name:"httpx_timeout",children:e.jsx(m,{min:5,max:60,size:"small",style:{width:"100%"}})}),e.jsx(t.Item,{label:"User-Agent类型",name:"httpx_user_agent_type",children:e.jsxs(_,{size:"small",children:[e.jsx(n,{value:o.Desktop,children:"桌面浏览器"}),e.jsx(n,{value:o.Mobile,children:"移动浏览器"}),e.jsx(n,{value:o.Random,children:"随机"})]})}),e.jsx(t.Item,{name:"httpx_tech_detect",valuePropName:"checked",children:e.jsxs(i,{children:[e.jsx(c,{size:"small"}),e.jsx(r,{children:"技术检测"})]})})]})}),e.jsx(a,{xs:24,lg:8,children:e.jsxs(d,{title:"Nuclei配置",size:"small",children:[e.jsx(t.Item,{label:"线程数",name:"nuclei_threads",children:e.jsx(m,{min:1,max:100,size:"small",style:{width:"100%"}})}),e.jsx(t.Item,{label:"速率限制",name:"nuclei_rate_limit",children:e.jsx(m,{min:50,max:500,size:"small",style:{width:"100%"}})}),e.jsx(t.Item,{label:"严重性",name:"nuclei_severity",children:e.jsxs(_,{mode:"multiple",size:"small",placeholder:"选择严重性级别",children:[e.jsx(n,{value:"info",children:"信息"}),e.jsx(n,{value:"low",children:"低危"}),e.jsx(n,{value:"medium",children:"中危"}),e.jsx(n,{value:"high",children:"高危"}),e.jsx(n,{value:"critical",children:"严重"})]})}),e.jsx(t.Item,{name:"nuclei_update_templates",valuePropName:"checked",children:e.jsxs(i,{children:[e.jsx(c,{size:"small"}),e.jsx(r,{children:"更新模板"})]})})]})})]})]}),e.jsx("div",{style:{marginTop:32,textAlign:"right"},children:e.jsxs(i,{children:[e.jsx(u,{onClick:()=>j("/tasks"),children:"取消"}),e.jsx(u,{type:"primary",htmlType:"submit",icon:e.jsx(M,{}),loading:b.isPending,size:"large",children:"开始扫描"})]})})]})]})})};export{J as default};
//# sourceMappingURL=CreateTask-DV6R3ssa.js.map
