const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/TaskList-Bttm5b5J.js","assets/vendor-o6zXO7vr.js","assets/useMutation-i6kbUsZB.js","assets/ui-Smnqh0BA.js","assets/charts-CfM58meh.js","assets/CreateTask-DV6R3ssa.js","assets/TaskDetail-Bfmfpbt2.js","assets/index-Byjsv-ns.js","assets/index-huypI1iw.js","assets/index-CWFnNnBI.js","assets/index-C1hbtBsp.js","assets/NotFound-CqlA3poe.js"])))=>i.map(i=>d[i]);
var hs=e=>{throw TypeError(e)};var hr=(e,t,r)=>t.has(e)||hs("Cannot "+r);var p=(e,t,r)=>(hr(e,t,"read from private field"),r?r.call(e):t.get(e)),O=(e,t,r)=>t.has(e)?hs("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),C=(e,t,r,s)=>(hr(e,t,"write to private field"),s?s.call(e,r):t.set(e,r),r),A=(e,t,r)=>(hr(e,t,"access private method"),r);var Dt=(e,t,r,s)=>({set _(n){C(e,t,n,r)},get _(){return p(e,t,s)}});import{r as w,c as Fi,b as Di,g as vn,e as Li,R as re}from"./vendor-o6zXO7vr.js";import{d as Ni,T as Zr,F as pt,R as $t,C as ce,a as Ui,b as le,I as ps,c as _r,e as Mi,B as De,s as Bt,f as Fe,S as pr,g as Ae,h as bn,i as xn,j as mr,k as Lt,l as Ye,m as Sn,n as $i,o as ms,p as Bi,P as zi,q as qi,t as ys,u as gs,v as Hi,w as Wi,x as Qi,y as Or,L as Pr,z as Ki,A as Ji,D as Vi,E as Yi,M as Gi,G as Xi,H as Zi,J as eo,K as to,N as ro,O as so,Q as no,U as vs,V as io,W as oo,X as bs,Y as wn,Z as ao,_ as Nt}from"./ui-Smnqh0BA.js";import{R as xs,L as lo,C as co,X as uo,Y as fo,T as Ss,a as ho,b as yr,P as po,c as mo,d as yo}from"./charts-CfM58meh.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))s(n);new MutationObserver(n=>{for(const i of n)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&s(o)}).observe(document,{childList:!0,subtree:!0});function r(n){const i={};return n.integrity&&(i.integrity=n.integrity),n.referrerPolicy&&(i.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?i.credentials="include":n.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function s(n){if(n.ep)return;n.ep=!0;const i=r(n);fetch(n.href,i)}})();var Rn={exports:{}},Vt={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var go=w,vo=Symbol.for("react.element"),bo=Symbol.for("react.fragment"),xo=Object.prototype.hasOwnProperty,So=go.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,wo={key:!0,ref:!0,__self:!0,__source:!0};function En(e,t,r){var s,n={},i=null,o=null;r!==void 0&&(i=""+r),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(o=t.ref);for(s in t)xo.call(t,s)&&!wo.hasOwnProperty(s)&&(n[s]=t[s]);if(e&&e.defaultProps)for(s in t=e.defaultProps,t)n[s]===void 0&&(n[s]=t[s]);return{$$typeof:vo,type:e,key:i,ref:o,props:n,_owner:So.current}}Vt.Fragment=bo;Vt.jsx=En;Vt.jsxs=En;Rn.exports=Vt;var l=Rn.exports,Tr={},ws=Fi;Tr.createRoot=ws.createRoot,Tr.hydrateRoot=ws.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function gt(){return gt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e},gt.apply(this,arguments)}var Oe;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Oe||(Oe={}));const Rs="popstate";function Ro(e){e===void 0&&(e={});function t(s,n){let{pathname:i,search:o,hash:a}=s.location;return kr("",{pathname:i,search:o,hash:a},n.state&&n.state.usr||null,n.state&&n.state.key||"default")}function r(s,n){return typeof n=="string"?n:jn(n)}return Co(t,r,null,e)}function $(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Cn(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Eo(){return Math.random().toString(36).substr(2,8)}function Es(e,t){return{usr:e.state,key:e.key,idx:t}}function kr(e,t,r,s){return r===void 0&&(r=null),gt({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?ut(t):t,{state:r,key:t&&t.key||s||Eo()})}function jn(e){let{pathname:t="/",search:r="",hash:s=""}=e;return r&&r!=="?"&&(t+=r.charAt(0)==="?"?r:"?"+r),s&&s!=="#"&&(t+=s.charAt(0)==="#"?s:"#"+s),t}function ut(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let s=e.indexOf("?");s>=0&&(t.search=e.substr(s),e=e.substr(0,s)),e&&(t.pathname=e)}return t}function Co(e,t,r,s){s===void 0&&(s={});let{window:n=document.defaultView,v5Compat:i=!1}=s,o=n.history,a=Oe.Pop,u=null,d=c();d==null&&(d=0,o.replaceState(gt({},o.state,{idx:d}),""));function c(){return(o.state||{idx:null}).idx}function f(){a=Oe.Pop;let h=c(),S=h==null?null:h-d;d=h,u&&u({action:a,location:m.location,delta:S})}function y(h,S){a=Oe.Push;let b=kr(m.location,h,S);d=c()+1;let R=Es(b,d),_=m.createHref(b);try{o.pushState(R,"",_)}catch(j){if(j instanceof DOMException&&j.name==="DataCloneError")throw j;n.location.assign(_)}i&&u&&u({action:a,location:m.location,delta:1})}function x(h,S){a=Oe.Replace;let b=kr(m.location,h,S);d=c();let R=Es(b,d),_=m.createHref(b);o.replaceState(R,"",_),i&&u&&u({action:a,location:m.location,delta:0})}function v(h){let S=n.location.origin!=="null"?n.location.origin:n.location.href,b=typeof h=="string"?h:jn(h);return b=b.replace(/ $/,"%20"),$(S,"No window.location.(origin|href) available to create URL for href: "+b),new URL(b,S)}let m={get action(){return a},get location(){return e(n,o)},listen(h){if(u)throw new Error("A history only accepts one active listener");return n.addEventListener(Rs,f),u=h,()=>{n.removeEventListener(Rs,f),u=null}},createHref(h){return t(n,h)},createURL:v,encodeLocation(h){let S=v(h);return{pathname:S.pathname,search:S.search,hash:S.hash}},push:y,replace:x,go(h){return o.go(h)}};return m}var Cs;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Cs||(Cs={}));function jo(e,t,r){return r===void 0&&(r="/"),_o(e,t,r)}function _o(e,t,r,s){let n=typeof t=="string"?ut(t):t,i=Pn(n.pathname||"/",r);if(i==null)return null;let o=_n(e);Oo(o);let a=null;for(let u=0;a==null&&u<o.length;++u){let d=$o(i);a=No(o[u],d)}return a}function _n(e,t,r,s){t===void 0&&(t=[]),r===void 0&&(r=[]),s===void 0&&(s="");let n=(i,o,a)=>{let u={relativePath:a===void 0?i.path||"":a,caseSensitive:i.caseSensitive===!0,childrenIndex:o,route:i};u.relativePath.startsWith("/")&&($(u.relativePath.startsWith(s),'Absolute route path "'+u.relativePath+'" nested under path '+('"'+s+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),u.relativePath=u.relativePath.slice(s.length));let d=Qe([s,u.relativePath]),c=r.concat(u);i.children&&i.children.length>0&&($(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+d+'".')),_n(i.children,t,c,d)),!(i.path==null&&!i.index)&&t.push({path:d,score:Do(d,i.index),routesMeta:c})};return e.forEach((i,o)=>{var a;if(i.path===""||!((a=i.path)!=null&&a.includes("?")))n(i,o);else for(let u of On(i.path))n(i,o,u)}),t}function On(e){let t=e.split("/");if(t.length===0)return[];let[r,...s]=t,n=r.endsWith("?"),i=r.replace(/\?$/,"");if(s.length===0)return n?[i,""]:[i];let o=On(s.join("/")),a=[];return a.push(...o.map(u=>u===""?i:[i,u].join("/"))),n&&a.push(...o),a.map(u=>e.startsWith("/")&&u===""?"/":u)}function Oo(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:Lo(t.routesMeta.map(s=>s.childrenIndex),r.routesMeta.map(s=>s.childrenIndex)))}const Po=/^:[\w-]+$/,To=3,ko=2,Ao=1,Io=10,Fo=-2,js=e=>e==="*";function Do(e,t){let r=e.split("/"),s=r.length;return r.some(js)&&(s+=Fo),t&&(s+=ko),r.filter(n=>!js(n)).reduce((n,i)=>n+(Po.test(i)?To:i===""?Ao:Io),s)}function Lo(e,t){return e.length===t.length&&e.slice(0,-1).every((s,n)=>s===t[n])?e[e.length-1]-t[t.length-1]:0}function No(e,t,r){let{routesMeta:s}=e,n={},i="/",o=[];for(let a=0;a<s.length;++a){let u=s[a],d=a===s.length-1,c=i==="/"?t:t.slice(i.length)||"/",f=Uo({path:u.relativePath,caseSensitive:u.caseSensitive,end:d},c),y=u.route;if(!f)return null;Object.assign(n,f.params),o.push({params:n,pathname:Qe([i,f.pathname]),pathnameBase:Ho(Qe([i,f.pathnameBase])),route:y}),f.pathnameBase!=="/"&&(i=Qe([i,f.pathnameBase]))}return o}function Uo(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,s]=Mo(e.path,e.caseSensitive,e.end),n=t.match(r);if(!n)return null;let i=n[0],o=i.replace(/(.)\/+$/,"$1"),a=n.slice(1);return{params:s.reduce((d,c,f)=>{let{paramName:y,isOptional:x}=c;if(y==="*"){let m=a[f]||"";o=i.slice(0,i.length-m.length).replace(/(.)\/+$/,"$1")}const v=a[f];return x&&!v?d[y]=void 0:d[y]=(v||"").replace(/%2F/g,"/"),d},{}),pathname:i,pathnameBase:o,pattern:e}}function Mo(e,t,r){t===void 0&&(t=!1),r===void 0&&(r=!0),Cn(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let s=[],n="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,a,u)=>(s.push({paramName:a,isOptional:u!=null}),u?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(s.push({paramName:"*"}),n+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?n+="\\/*$":e!==""&&e!=="/"&&(n+="(?:(?=\\/|$))"),[new RegExp(n,t?void 0:"i"),s]}function $o(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Cn(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function Pn(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,s=e.charAt(r);return s&&s!=="/"?null:e.slice(r)||"/"}function Bo(e,t){t===void 0&&(t="/");let{pathname:r,search:s="",hash:n=""}=typeof e=="string"?ut(e):e;return{pathname:r?r.startsWith("/")?r:zo(r,t):t,search:Wo(s),hash:Qo(n)}}function zo(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(n=>{n===".."?r.length>1&&r.pop():n!=="."&&r.push(n)}),r.length>1?r.join("/"):"/"}function gr(e,t,r,s){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(s)+"].  Please separate it out to the ")+("`to."+r+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function qo(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function Tn(e,t){let r=qo(e);return t?r.map((s,n)=>n===r.length-1?s.pathname:s.pathnameBase):r.map(s=>s.pathnameBase)}function kn(e,t,r,s){s===void 0&&(s=!1);let n;typeof e=="string"?n=ut(e):(n=gt({},e),$(!n.pathname||!n.pathname.includes("?"),gr("?","pathname","search",n)),$(!n.pathname||!n.pathname.includes("#"),gr("#","pathname","hash",n)),$(!n.search||!n.search.includes("#"),gr("#","search","hash",n)));let i=e===""||n.pathname==="",o=i?"/":n.pathname,a;if(o==null)a=r;else{let f=t.length-1;if(!s&&o.startsWith("..")){let y=o.split("/");for(;y[0]==="..";)y.shift(),f-=1;n.pathname=y.join("/")}a=f>=0?t[f]:"/"}let u=Bo(n,a),d=o&&o!=="/"&&o.endsWith("/"),c=(i||o===".")&&r.endsWith("/");return!u.pathname.endsWith("/")&&(d||c)&&(u.pathname+="/"),u}const Qe=e=>e.join("/").replace(/\/\/+/g,"/"),Ho=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Wo=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Qo=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Ko(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const An=["post","put","patch","delete"];new Set(An);const Jo=["get",...An];new Set(Jo);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function vt(){return vt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e},vt.apply(this,arguments)}const es=w.createContext(null),Vo=w.createContext(null),_t=w.createContext(null),Yt=w.createContext(null),Te=w.createContext({outlet:null,matches:[],isDataRoute:!1}),In=w.createContext(null);function Ot(){return w.useContext(Yt)!=null}function Pt(){return Ot()||$(!1),w.useContext(Yt).location}function Fn(e){w.useContext(_t).static||w.useLayoutEffect(e)}function ts(){let{isDataRoute:e}=w.useContext(Te);return e?la():Yo()}function Yo(){Ot()||$(!1);let e=w.useContext(es),{basename:t,future:r,navigator:s}=w.useContext(_t),{matches:n}=w.useContext(Te),{pathname:i}=Pt(),o=JSON.stringify(Tn(n,r.v7_relativeSplatPath)),a=w.useRef(!1);return Fn(()=>{a.current=!0}),w.useCallback(function(d,c){if(c===void 0&&(c={}),!a.current)return;if(typeof d=="number"){s.go(d);return}let f=kn(d,JSON.parse(o),i,c.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:Qe([t,f.pathname])),(c.replace?s.replace:s.push)(f,c.state,c)},[t,s,o,i,e])}function nd(){let{matches:e}=w.useContext(Te),t=e[e.length-1];return t?t.params:{}}function Go(e,t){return Xo(e,t)}function Xo(e,t,r,s){Ot()||$(!1);let{navigator:n}=w.useContext(_t),{matches:i}=w.useContext(Te),o=i[i.length-1],a=o?o.params:{};o&&o.pathname;let u=o?o.pathnameBase:"/";o&&o.route;let d=Pt(),c;if(t){var f;let h=typeof t=="string"?ut(t):t;u==="/"||(f=h.pathname)!=null&&f.startsWith(u)||$(!1),c=h}else c=d;let y=c.pathname||"/",x=y;if(u!=="/"){let h=u.replace(/^\//,"").split("/");x="/"+y.replace(/^\//,"").split("/").slice(h.length).join("/")}let v=jo(e,{pathname:x}),m=sa(v&&v.map(h=>Object.assign({},h,{params:Object.assign({},a,h.params),pathname:Qe([u,n.encodeLocation?n.encodeLocation(h.pathname).pathname:h.pathname]),pathnameBase:h.pathnameBase==="/"?u:Qe([u,n.encodeLocation?n.encodeLocation(h.pathnameBase).pathname:h.pathnameBase])})),i,r,s);return t&&m?w.createElement(Yt.Provider,{value:{location:vt({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:Oe.Pop}},m):m}function Zo(){let e=aa(),t=Ko(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,n={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return w.createElement(w.Fragment,null,w.createElement("h2",null,"Unexpected Application Error!"),w.createElement("h3",{style:{fontStyle:"italic"}},t),r?w.createElement("pre",{style:n},r):null,null)}const ea=w.createElement(Zo,null);class ta extends w.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,r){return r.location!==t.location||r.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:r.error,location:r.location,revalidation:t.revalidation||r.revalidation}}componentDidCatch(t,r){console.error("React Router caught the following error during render",t,r)}render(){return this.state.error!==void 0?w.createElement(Te.Provider,{value:this.props.routeContext},w.createElement(In.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function ra(e){let{routeContext:t,match:r,children:s}=e,n=w.useContext(es);return n&&n.static&&n.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(n.staticContext._deepestRenderedBoundaryId=r.route.id),w.createElement(Te.Provider,{value:t},s)}function sa(e,t,r,s){var n;if(t===void 0&&(t=[]),r===void 0&&(r=null),s===void 0&&(s=null),e==null){var i;if(!r)return null;if(r.errors)e=r.matches;else if((i=s)!=null&&i.v7_partialHydration&&t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let o=e,a=(n=r)==null?void 0:n.errors;if(a!=null){let c=o.findIndex(f=>f.route.id&&(a==null?void 0:a[f.route.id])!==void 0);c>=0||$(!1),o=o.slice(0,Math.min(o.length,c+1))}let u=!1,d=-1;if(r&&s&&s.v7_partialHydration)for(let c=0;c<o.length;c++){let f=o[c];if((f.route.HydrateFallback||f.route.hydrateFallbackElement)&&(d=c),f.route.id){let{loaderData:y,errors:x}=r,v=f.route.loader&&y[f.route.id]===void 0&&(!x||x[f.route.id]===void 0);if(f.route.lazy||v){u=!0,d>=0?o=o.slice(0,d+1):o=[o[0]];break}}}return o.reduceRight((c,f,y)=>{let x,v=!1,m=null,h=null;r&&(x=a&&f.route.id?a[f.route.id]:void 0,m=f.route.errorElement||ea,u&&(d<0&&y===0?(ca("route-fallback"),v=!0,h=null):d===y&&(v=!0,h=f.route.hydrateFallbackElement||null)));let S=t.concat(o.slice(0,y+1)),b=()=>{let R;return x?R=m:v?R=h:f.route.Component?R=w.createElement(f.route.Component,null):f.route.element?R=f.route.element:R=c,w.createElement(ra,{match:f,routeContext:{outlet:c,matches:S,isDataRoute:r!=null},children:R})};return r&&(f.route.ErrorBoundary||f.route.errorElement||y===0)?w.createElement(ta,{location:r.location,revalidation:r.revalidation,component:m,error:x,children:b(),routeContext:{outlet:null,matches:S,isDataRoute:!0}}):b()},null)}var Dn=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Dn||{}),Ln=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Ln||{});function na(e){let t=w.useContext(es);return t||$(!1),t}function ia(e){let t=w.useContext(Vo);return t||$(!1),t}function oa(e){let t=w.useContext(Te);return t||$(!1),t}function Nn(e){let t=oa(),r=t.matches[t.matches.length-1];return r.route.id||$(!1),r.route.id}function aa(){var e;let t=w.useContext(In),r=ia(),s=Nn();return t!==void 0?t:(e=r.errors)==null?void 0:e[s]}function la(){let{router:e}=na(Dn.UseNavigateStable),t=Nn(Ln.UseNavigateStable),r=w.useRef(!1);return Fn(()=>{r.current=!0}),w.useCallback(function(n,i){i===void 0&&(i={}),r.current&&(typeof n=="number"?e.navigate(n):e.navigate(n,vt({fromRouteId:t},i)))},[e,t])}const _s={};function ca(e,t,r){_s[e]||(_s[e]=!0)}function ua(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function vr(e){let{to:t,replace:r,state:s,relative:n}=e;Ot()||$(!1);let{future:i,static:o}=w.useContext(_t),{matches:a}=w.useContext(Te),{pathname:u}=Pt(),d=ts(),c=kn(t,Tn(a,i.v7_relativeSplatPath),u,n==="path"),f=JSON.stringify(c);return w.useEffect(()=>d(JSON.parse(f),{replace:r,state:s,relative:n}),[d,f,n,r,s]),null}function J(e){$(!1)}function da(e){let{basename:t="/",children:r=null,location:s,navigationType:n=Oe.Pop,navigator:i,static:o=!1,future:a}=e;Ot()&&$(!1);let u=t.replace(/^\/*/,"/"),d=w.useMemo(()=>({basename:u,navigator:i,static:o,future:vt({v7_relativeSplatPath:!1},a)}),[u,a,i,o]);typeof s=="string"&&(s=ut(s));let{pathname:c="/",search:f="",hash:y="",state:x=null,key:v="default"}=s,m=w.useMemo(()=>{let h=Pn(c,u);return h==null?null:{location:{pathname:h,search:f,hash:y,state:x,key:v},navigationType:n}},[u,c,f,y,x,v,n]);return m==null?null:w.createElement(_t.Provider,{value:d},w.createElement(Yt.Provider,{children:r,value:m}))}function Os(e){let{children:t,location:r}=e;return Go(Ar(t),r)}new Promise(()=>{});function Ar(e,t){t===void 0&&(t=[]);let r=[];return w.Children.forEach(e,(s,n)=>{if(!w.isValidElement(s))return;let i=[...t,n];if(s.type===w.Fragment){r.push.apply(r,Ar(s.props.children,i));return}s.type!==J&&$(!1),!s.props.index||!s.props.children||$(!1);let o={id:s.props.id||i.join("-"),caseSensitive:s.props.caseSensitive,element:s.props.element,Component:s.props.Component,index:s.props.index,path:s.props.path,loader:s.props.loader,action:s.props.action,errorElement:s.props.errorElement,ErrorBoundary:s.props.ErrorBoundary,hasErrorBoundary:s.props.ErrorBoundary!=null||s.props.errorElement!=null,shouldRevalidate:s.props.shouldRevalidate,handle:s.props.handle,lazy:s.props.lazy};s.props.children&&(o.children=Ar(s.props.children,i)),r.push(o)}),r}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const fa="6";try{window.__reactRouterVersion=fa}catch{}const ha="startTransition",Ps=Di[ha];function pa(e){let{basename:t,children:r,future:s,window:n}=e,i=w.useRef();i.current==null&&(i.current=Ro({window:n,v5Compat:!0}));let o=i.current,[a,u]=w.useState({action:o.action,location:o.location}),{v7_startTransition:d}=s||{},c=w.useCallback(f=>{d&&Ps?Ps(()=>u(f)):u(f)},[u,d]);return w.useLayoutEffect(()=>o.listen(c),[o,c]),w.useEffect(()=>ua(s),[s]),w.createElement(da,{basename:t,children:r,location:a.location,navigationType:a.action,navigator:o,future:s})}var Ts;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Ts||(Ts={}));var ks;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(ks||(ks={}));var Tt=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Je=typeof window>"u"||"Deno"in globalThis;function Y(){}function ma(e,t){return typeof e=="function"?e(t):e}function Ir(e){return typeof e=="number"&&e>=0&&e!==1/0}function Un(e,t){return Math.max(e+(t||0)-Date.now(),0)}function Pe(e,t){return typeof e=="function"?e(t):e}function te(e,t){return typeof e=="function"?e(t):e}function As(e,t){const{type:r="all",exact:s,fetchStatus:n,predicate:i,queryKey:o,stale:a}=e;if(o){if(s){if(t.queryHash!==rs(o,t.options))return!1}else if(!xt(t.queryKey,o))return!1}if(r!=="all"){const u=t.isActive();if(r==="active"&&!u||r==="inactive"&&u)return!1}return!(typeof a=="boolean"&&t.isStale()!==a||n&&n!==t.state.fetchStatus||i&&!i(t))}function Is(e,t){const{exact:r,status:s,predicate:n,mutationKey:i}=e;if(i){if(!t.options.mutationKey)return!1;if(r){if(bt(t.options.mutationKey)!==bt(i))return!1}else if(!xt(t.options.mutationKey,i))return!1}return!(s&&t.state.status!==s||n&&!n(t))}function rs(e,t){return((t==null?void 0:t.queryKeyHashFn)||bt)(e)}function bt(e){return JSON.stringify(e,(t,r)=>Dr(r)?Object.keys(r).sort().reduce((s,n)=>(s[n]=r[n],s),{}):r)}function xt(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?Object.keys(t).every(r=>xt(e[r],t[r])):!1}function Mn(e,t){if(e===t)return e;const r=Fs(e)&&Fs(t);if(r||Dr(e)&&Dr(t)){const s=r?e:Object.keys(e),n=s.length,i=r?t:Object.keys(t),o=i.length,a=r?[]:{},u=new Set(s);let d=0;for(let c=0;c<o;c++){const f=r?c:i[c];(!r&&u.has(f)||r)&&e[f]===void 0&&t[f]===void 0?(a[f]=void 0,d++):(a[f]=Mn(e[f],t[f]),a[f]===e[f]&&e[f]!==void 0&&d++)}return n===o&&d===n?e:a}return t}function Fr(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(e[r]!==t[r])return!1;return!0}function Fs(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function Dr(e){if(!Ds(e))return!1;const t=e.constructor;if(t===void 0)return!0;const r=t.prototype;return!(!Ds(r)||!r.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function Ds(e){return Object.prototype.toString.call(e)==="[object Object]"}function ya(e){return new Promise(t=>{setTimeout(t,e)})}function Lr(e,t,r){return typeof r.structuralSharing=="function"?r.structuralSharing(e,t):r.structuralSharing!==!1?Mn(e,t):t}function ga(e,t,r=0){const s=[...e,t];return r&&s.length>r?s.slice(1):s}function va(e,t,r=0){const s=[t,...e];return r&&s.length>r?s.slice(0,-1):s}var ss=Symbol();function $n(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===ss?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}function ba(e,t){return typeof e=="function"?e(...t):!!e}var Ne,xe,Ge,cn,xa=(cn=class extends Tt{constructor(){super();O(this,Ne);O(this,xe);O(this,Ge);C(this,Ge,t=>{if(!Je&&window.addEventListener){const r=()=>t();return window.addEventListener("visibilitychange",r,!1),()=>{window.removeEventListener("visibilitychange",r)}}})}onSubscribe(){p(this,xe)||this.setEventListener(p(this,Ge))}onUnsubscribe(){var t;this.hasListeners()||((t=p(this,xe))==null||t.call(this),C(this,xe,void 0))}setEventListener(t){var r;C(this,Ge,t),(r=p(this,xe))==null||r.call(this),C(this,xe,t(s=>{typeof s=="boolean"?this.setFocused(s):this.onFocus()}))}setFocused(t){p(this,Ne)!==t&&(C(this,Ne,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(r=>{r(t)})}isFocused(){var t;return typeof p(this,Ne)=="boolean"?p(this,Ne):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},Ne=new WeakMap,xe=new WeakMap,Ge=new WeakMap,cn),ns=new xa,Xe,Se,Ze,un,Sa=(un=class extends Tt{constructor(){super();O(this,Xe,!0);O(this,Se);O(this,Ze);C(this,Ze,t=>{if(!Je&&window.addEventListener){const r=()=>t(!0),s=()=>t(!1);return window.addEventListener("online",r,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",r),window.removeEventListener("offline",s)}}})}onSubscribe(){p(this,Se)||this.setEventListener(p(this,Ze))}onUnsubscribe(){var t;this.hasListeners()||((t=p(this,Se))==null||t.call(this),C(this,Se,void 0))}setEventListener(t){var r;C(this,Ze,t),(r=p(this,Se))==null||r.call(this),C(this,Se,t(this.setOnline.bind(this)))}setOnline(t){p(this,Xe)!==t&&(C(this,Xe,t),this.listeners.forEach(s=>{s(t)}))}isOnline(){return p(this,Xe)}},Xe=new WeakMap,Se=new WeakMap,Ze=new WeakMap,un),Qt=new Sa;function Nr(){let e,t;const r=new Promise((n,i)=>{e=n,t=i});r.status="pending",r.catch(()=>{});function s(n){Object.assign(r,n),delete r.resolve,delete r.reject}return r.resolve=n=>{s({status:"fulfilled",value:n}),e(n)},r.reject=n=>{s({status:"rejected",reason:n}),t(n)},r}function wa(e){return Math.min(1e3*2**e,3e4)}function Bn(e){return(e??"online")==="online"?Qt.isOnline():!0}var zn=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function br(e){return e instanceof zn}function qn(e){let t=!1,r=0,s=!1,n;const i=Nr(),o=m=>{var h;s||(y(new zn(m)),(h=e.abort)==null||h.call(e))},a=()=>{t=!0},u=()=>{t=!1},d=()=>ns.isFocused()&&(e.networkMode==="always"||Qt.isOnline())&&e.canRun(),c=()=>Bn(e.networkMode)&&e.canRun(),f=m=>{var h;s||(s=!0,(h=e.onSuccess)==null||h.call(e,m),n==null||n(),i.resolve(m))},y=m=>{var h;s||(s=!0,(h=e.onError)==null||h.call(e,m),n==null||n(),i.reject(m))},x=()=>new Promise(m=>{var h;n=S=>{(s||d())&&m(S)},(h=e.onPause)==null||h.call(e)}).then(()=>{var m;n=void 0,s||(m=e.onContinue)==null||m.call(e)}),v=()=>{if(s)return;let m;const h=r===0?e.initialPromise:void 0;try{m=h??e.fn()}catch(S){m=Promise.reject(S)}Promise.resolve(m).then(f).catch(S=>{var P;if(s)return;const b=e.retry??(Je?0:3),R=e.retryDelay??wa,_=typeof R=="function"?R(r,S):R,j=b===!0||typeof b=="number"&&r<b||typeof b=="function"&&b(r,S);if(t||!j){y(S);return}r++,(P=e.onFail)==null||P.call(e,r,S),ya(_).then(()=>d()?void 0:x()).then(()=>{t?y(S):v()})})};return{promise:i,cancel:o,continue:()=>(n==null||n(),i),cancelRetry:a,continueRetry:u,canStart:c,start:()=>(c()?v():x().then(v),i)}}var Ra=e=>setTimeout(e,0);function Ea(){let e=[],t=0,r=a=>{a()},s=a=>{a()},n=Ra;const i=a=>{t?e.push(a):n(()=>{r(a)})},o=()=>{const a=e;e=[],a.length&&n(()=>{s(()=>{a.forEach(u=>{r(u)})})})};return{batch:a=>{let u;t++;try{u=a()}finally{t--,t||o()}return u},batchCalls:a=>(...u)=>{i(()=>{a(...u)})},schedule:i,setNotifyFunction:a=>{r=a},setBatchNotifyFunction:a=>{s=a},setScheduler:a=>{n=a}}}var B=Ea(),Ue,dn,Hn=(dn=class{constructor(){O(this,Ue)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),Ir(this.gcTime)&&C(this,Ue,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(Je?1/0:5*60*1e3))}clearGcTimeout(){p(this,Ue)&&(clearTimeout(p(this,Ue)),C(this,Ue,void 0))}},Ue=new WeakMap,dn),et,Me,ee,$e,z,Rt,Be,ne,me,fn,Ca=(fn=class extends Hn{constructor(t){super();O(this,ne);O(this,et);O(this,Me);O(this,ee);O(this,$e);O(this,z);O(this,Rt);O(this,Be);C(this,Be,!1),C(this,Rt,t.defaultOptions),this.setOptions(t.options),this.observers=[],C(this,$e,t.client),C(this,ee,p(this,$e).getQueryCache()),this.queryKey=t.queryKey,this.queryHash=t.queryHash,C(this,et,ja(this.options)),this.state=t.state??p(this,et),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=p(this,z))==null?void 0:t.promise}setOptions(t){this.options={...p(this,Rt),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&p(this,ee).remove(this)}setData(t,r){const s=Lr(this.state.data,t,this.options);return A(this,ne,me).call(this,{data:s,type:"success",dataUpdatedAt:r==null?void 0:r.updatedAt,manual:r==null?void 0:r.manual}),s}setState(t,r){A(this,ne,me).call(this,{type:"setState",state:t,setStateOptions:r})}cancel(t){var s,n;const r=(s=p(this,z))==null?void 0:s.promise;return(n=p(this,z))==null||n.cancel(t),r?r.then(Y).catch(Y):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(p(this,et))}isActive(){return this.observers.some(t=>te(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===ss||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(t=>Pe(t.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(t=0){return this.state.data===void 0?!0:t==="static"?!1:this.state.isInvalidated?!0:!Un(this.state.dataUpdatedAt,t)}onFocus(){var r;const t=this.observers.find(s=>s.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(r=p(this,z))==null||r.continue()}onOnline(){var r;const t=this.observers.find(s=>s.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(r=p(this,z))==null||r.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),p(this,ee).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(r=>r!==t),this.observers.length||(p(this,z)&&(p(this,Be)?p(this,z).cancel({revert:!0}):p(this,z).cancelRetry()),this.scheduleGc()),p(this,ee).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||A(this,ne,me).call(this,{type:"invalidate"})}fetch(t,r){var d,c,f;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(r!=null&&r.cancelRefetch))this.cancel({silent:!0});else if(p(this,z))return p(this,z).continueRetry(),p(this,z).promise}if(t&&this.setOptions(t),!this.options.queryFn){const y=this.observers.find(x=>x.options.queryFn);y&&this.setOptions(y.options)}const s=new AbortController,n=y=>{Object.defineProperty(y,"signal",{enumerable:!0,get:()=>(C(this,Be,!0),s.signal)})},i=()=>{const y=$n(this.options,r),v=(()=>{const m={client:p(this,$e),queryKey:this.queryKey,meta:this.meta};return n(m),m})();return C(this,Be,!1),this.options.persister?this.options.persister(y,v,this):y(v)},a=(()=>{const y={fetchOptions:r,options:this.options,queryKey:this.queryKey,client:p(this,$e),state:this.state,fetchFn:i};return n(y),y})();(d=this.options.behavior)==null||d.onFetch(a,this),C(this,Me,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((c=a.fetchOptions)==null?void 0:c.meta))&&A(this,ne,me).call(this,{type:"fetch",meta:(f=a.fetchOptions)==null?void 0:f.meta});const u=y=>{var x,v,m,h;br(y)&&y.silent||A(this,ne,me).call(this,{type:"error",error:y}),br(y)||((v=(x=p(this,ee).config).onError)==null||v.call(x,y,this),(h=(m=p(this,ee).config).onSettled)==null||h.call(m,this.state.data,y,this)),this.scheduleGc()};return C(this,z,qn({initialPromise:r==null?void 0:r.initialPromise,fn:a.fetchFn,abort:s.abort.bind(s),onSuccess:y=>{var x,v,m,h;if(y===void 0){u(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(y)}catch(S){u(S);return}(v=(x=p(this,ee).config).onSuccess)==null||v.call(x,y,this),(h=(m=p(this,ee).config).onSettled)==null||h.call(m,y,this.state.error,this),this.scheduleGc()},onError:u,onFail:(y,x)=>{A(this,ne,me).call(this,{type:"failed",failureCount:y,error:x})},onPause:()=>{A(this,ne,me).call(this,{type:"pause"})},onContinue:()=>{A(this,ne,me).call(this,{type:"continue"})},retry:a.options.retry,retryDelay:a.options.retryDelay,networkMode:a.options.networkMode,canRun:()=>!0})),p(this,z).start()}},et=new WeakMap,Me=new WeakMap,ee=new WeakMap,$e=new WeakMap,z=new WeakMap,Rt=new WeakMap,Be=new WeakMap,ne=new WeakSet,me=function(t){const r=s=>{switch(t.type){case"failed":return{...s,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...s,fetchStatus:"paused"};case"continue":return{...s,fetchStatus:"fetching"};case"fetch":return{...s,...Wn(s.data,this.options),fetchMeta:t.meta??null};case"success":return C(this,Me,void 0),{...s,data:t.data,dataUpdateCount:s.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const n=t.error;return br(n)&&n.revert&&p(this,Me)?{...p(this,Me),fetchStatus:"idle"}:{...s,error:n,errorUpdateCount:s.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:s.fetchFailureCount+1,fetchFailureReason:n,fetchStatus:"idle",status:"error"};case"invalidate":return{...s,isInvalidated:!0};case"setState":return{...s,...t.state}}};this.state=r(this.state),B.batch(()=>{this.observers.forEach(s=>{s.onQueryUpdate()}),p(this,ee).notify({query:this,type:"updated",action:t})})},fn);function Wn(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Bn(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function ja(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,r=t!==void 0,s=r?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?s??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}var de,hn,_a=(hn=class extends Tt{constructor(t={}){super();O(this,de);this.config=t,C(this,de,new Map)}build(t,r,s){const n=r.queryKey,i=r.queryHash??rs(n,r);let o=this.get(i);return o||(o=new Ca({client:t,queryKey:n,queryHash:i,options:t.defaultQueryOptions(r),state:s,defaultOptions:t.getQueryDefaults(n)}),this.add(o)),o}add(t){p(this,de).has(t.queryHash)||(p(this,de).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const r=p(this,de).get(t.queryHash);r&&(t.destroy(),r===t&&p(this,de).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){B.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return p(this,de).get(t)}getAll(){return[...p(this,de).values()]}find(t){const r={exact:!0,...t};return this.getAll().find(s=>As(r,s))}findAll(t={}){const r=this.getAll();return Object.keys(t).length>0?r.filter(s=>As(t,s)):r}notify(t){B.batch(()=>{this.listeners.forEach(r=>{r(t)})})}onFocus(){B.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){B.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},de=new WeakMap,hn),fe,H,ze,he,be,pn,Oa=(pn=class extends Hn{constructor(t){super();O(this,he);O(this,fe);O(this,H);O(this,ze);this.mutationId=t.mutationId,C(this,H,t.mutationCache),C(this,fe,[]),this.state=t.state||Pa(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){p(this,fe).includes(t)||(p(this,fe).push(t),this.clearGcTimeout(),p(this,H).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){C(this,fe,p(this,fe).filter(r=>r!==t)),this.scheduleGc(),p(this,H).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){p(this,fe).length||(this.state.status==="pending"?this.scheduleGc():p(this,H).remove(this))}continue(){var t;return((t=p(this,ze))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var i,o,a,u,d,c,f,y,x,v,m,h,S,b,R,_,j,P,U,L;const r=()=>{A(this,he,be).call(this,{type:"continue"})};C(this,ze,qn({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(E,F)=>{A(this,he,be).call(this,{type:"failed",failureCount:E,error:F})},onPause:()=>{A(this,he,be).call(this,{type:"pause"})},onContinue:r,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>p(this,H).canRun(this)}));const s=this.state.status==="pending",n=!p(this,ze).canStart();try{if(s)r();else{A(this,he,be).call(this,{type:"pending",variables:t,isPaused:n}),await((o=(i=p(this,H).config).onMutate)==null?void 0:o.call(i,t,this));const F=await((u=(a=this.options).onMutate)==null?void 0:u.call(a,t));F!==this.state.context&&A(this,he,be).call(this,{type:"pending",context:F,variables:t,isPaused:n})}const E=await p(this,ze).start();return await((c=(d=p(this,H).config).onSuccess)==null?void 0:c.call(d,E,t,this.state.context,this)),await((y=(f=this.options).onSuccess)==null?void 0:y.call(f,E,t,this.state.context)),await((v=(x=p(this,H).config).onSettled)==null?void 0:v.call(x,E,null,this.state.variables,this.state.context,this)),await((h=(m=this.options).onSettled)==null?void 0:h.call(m,E,null,t,this.state.context)),A(this,he,be).call(this,{type:"success",data:E}),E}catch(E){try{throw await((b=(S=p(this,H).config).onError)==null?void 0:b.call(S,E,t,this.state.context,this)),await((_=(R=this.options).onError)==null?void 0:_.call(R,E,t,this.state.context)),await((P=(j=p(this,H).config).onSettled)==null?void 0:P.call(j,void 0,E,this.state.variables,this.state.context,this)),await((L=(U=this.options).onSettled)==null?void 0:L.call(U,void 0,E,t,this.state.context)),E}finally{A(this,he,be).call(this,{type:"error",error:E})}}finally{p(this,H).runNext(this)}}},fe=new WeakMap,H=new WeakMap,ze=new WeakMap,he=new WeakSet,be=function(t){const r=s=>{switch(t.type){case"failed":return{...s,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...s,isPaused:!0};case"continue":return{...s,isPaused:!1};case"pending":return{...s,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...s,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...s,data:void 0,error:t.error,failureCount:s.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=r(this.state),B.batch(()=>{p(this,fe).forEach(s=>{s.onMutationUpdate(t)}),p(this,H).notify({mutation:this,type:"updated",action:t})})},pn);function Pa(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var ge,ie,Et,mn,Ta=(mn=class extends Tt{constructor(t={}){super();O(this,ge);O(this,ie);O(this,Et);this.config=t,C(this,ge,new Set),C(this,ie,new Map),C(this,Et,0)}build(t,r,s){const n=new Oa({mutationCache:this,mutationId:++Dt(this,Et)._,options:t.defaultMutationOptions(r),state:s});return this.add(n),n}add(t){p(this,ge).add(t);const r=Ut(t);if(typeof r=="string"){const s=p(this,ie).get(r);s?s.push(t):p(this,ie).set(r,[t])}this.notify({type:"added",mutation:t})}remove(t){if(p(this,ge).delete(t)){const r=Ut(t);if(typeof r=="string"){const s=p(this,ie).get(r);if(s)if(s.length>1){const n=s.indexOf(t);n!==-1&&s.splice(n,1)}else s[0]===t&&p(this,ie).delete(r)}}this.notify({type:"removed",mutation:t})}canRun(t){const r=Ut(t);if(typeof r=="string"){const s=p(this,ie).get(r),n=s==null?void 0:s.find(i=>i.state.status==="pending");return!n||n===t}else return!0}runNext(t){var s;const r=Ut(t);if(typeof r=="string"){const n=(s=p(this,ie).get(r))==null?void 0:s.find(i=>i!==t&&i.state.isPaused);return(n==null?void 0:n.continue())??Promise.resolve()}else return Promise.resolve()}clear(){B.batch(()=>{p(this,ge).forEach(t=>{this.notify({type:"removed",mutation:t})}),p(this,ge).clear(),p(this,ie).clear()})}getAll(){return Array.from(p(this,ge))}find(t){const r={exact:!0,...t};return this.getAll().find(s=>Is(r,s))}findAll(t={}){return this.getAll().filter(r=>Is(t,r))}notify(t){B.batch(()=>{this.listeners.forEach(r=>{r(t)})})}resumePausedMutations(){const t=this.getAll().filter(r=>r.state.isPaused);return B.batch(()=>Promise.all(t.map(r=>r.continue().catch(Y))))}},ge=new WeakMap,ie=new WeakMap,Et=new WeakMap,mn);function Ut(e){var t;return(t=e.options.scope)==null?void 0:t.id}function Ls(e){return{onFetch:(t,r)=>{var c,f,y,x,v;const s=t.options,n=(y=(f=(c=t.fetchOptions)==null?void 0:c.meta)==null?void 0:f.fetchMore)==null?void 0:y.direction,i=((x=t.state.data)==null?void 0:x.pages)||[],o=((v=t.state.data)==null?void 0:v.pageParams)||[];let a={pages:[],pageParams:[]},u=0;const d=async()=>{let m=!1;const h=R=>{Object.defineProperty(R,"signal",{enumerable:!0,get:()=>(t.signal.aborted?m=!0:t.signal.addEventListener("abort",()=>{m=!0}),t.signal)})},S=$n(t.options,t.fetchOptions),b=async(R,_,j)=>{if(m)return Promise.reject();if(_==null&&R.pages.length)return Promise.resolve(R);const U=(()=>{const se={client:t.client,queryKey:t.queryKey,pageParam:_,direction:j?"backward":"forward",meta:t.options.meta};return h(se),se})(),L=await S(U),{maxPages:E}=t.options,F=j?va:ga;return{pages:F(R.pages,L,E),pageParams:F(R.pageParams,_,E)}};if(n&&i.length){const R=n==="backward",_=R?ka:Ns,j={pages:i,pageParams:o},P=_(s,j);a=await b(j,P,R)}else{const R=e??i.length;do{const _=u===0?o[0]??s.initialPageParam:Ns(s,a);if(u>0&&_==null)break;a=await b(a,_),u++}while(u<R)}return a};t.options.persister?t.fetchFn=()=>{var m,h;return(h=(m=t.options).persister)==null?void 0:h.call(m,d,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r)}:t.fetchFn=d}}}function Ns(e,{pages:t,pageParams:r}){const s=t.length-1;return t.length>0?e.getNextPageParam(t[s],t,r[s],r):void 0}function ka(e,{pages:t,pageParams:r}){var s;return t.length>0?(s=e.getPreviousPageParam)==null?void 0:s.call(e,t[0],t,r[0],r):void 0}var N,we,Re,tt,rt,Ee,st,nt,yn,Aa=(yn=class{constructor(e={}){O(this,N);O(this,we);O(this,Re);O(this,tt);O(this,rt);O(this,Ee);O(this,st);O(this,nt);C(this,N,e.queryCache||new _a),C(this,we,e.mutationCache||new Ta),C(this,Re,e.defaultOptions||{}),C(this,tt,new Map),C(this,rt,new Map),C(this,Ee,0)}mount(){Dt(this,Ee)._++,p(this,Ee)===1&&(C(this,st,ns.subscribe(async e=>{e&&(await this.resumePausedMutations(),p(this,N).onFocus())})),C(this,nt,Qt.subscribe(async e=>{e&&(await this.resumePausedMutations(),p(this,N).onOnline())})))}unmount(){var e,t;Dt(this,Ee)._--,p(this,Ee)===0&&((e=p(this,st))==null||e.call(this),C(this,st,void 0),(t=p(this,nt))==null||t.call(this),C(this,nt,void 0))}isFetching(e){return p(this,N).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return p(this,we).findAll({...e,status:"pending"}).length}getQueryData(e){var r;const t=this.defaultQueryOptions({queryKey:e});return(r=p(this,N).get(t.queryHash))==null?void 0:r.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),r=p(this,N).build(this,t),s=r.state.data;return s===void 0?this.fetchQuery(e):(e.revalidateIfStale&&r.isStaleByTime(Pe(t.staleTime,r))&&this.prefetchQuery(t),Promise.resolve(s))}getQueriesData(e){return p(this,N).findAll(e).map(({queryKey:t,state:r})=>{const s=r.data;return[t,s]})}setQueryData(e,t,r){const s=this.defaultQueryOptions({queryKey:e}),n=p(this,N).get(s.queryHash),i=n==null?void 0:n.state.data,o=ma(t,i);if(o!==void 0)return p(this,N).build(this,s).setData(o,{...r,manual:!0})}setQueriesData(e,t,r){return B.batch(()=>p(this,N).findAll(e).map(({queryKey:s})=>[s,this.setQueryData(s,t,r)]))}getQueryState(e){var r;const t=this.defaultQueryOptions({queryKey:e});return(r=p(this,N).get(t.queryHash))==null?void 0:r.state}removeQueries(e){const t=p(this,N);B.batch(()=>{t.findAll(e).forEach(r=>{t.remove(r)})})}resetQueries(e,t){const r=p(this,N);return B.batch(()=>(r.findAll(e).forEach(s=>{s.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){const r={revert:!0,...t},s=B.batch(()=>p(this,N).findAll(e).map(n=>n.cancel(r)));return Promise.all(s).then(Y).catch(Y)}invalidateQueries(e,t={}){return B.batch(()=>(p(this,N).findAll(e).forEach(r=>{r.invalidate()}),(e==null?void 0:e.refetchType)==="none"?Promise.resolve():this.refetchQueries({...e,type:(e==null?void 0:e.refetchType)??(e==null?void 0:e.type)??"active"},t)))}refetchQueries(e,t={}){const r={...t,cancelRefetch:t.cancelRefetch??!0},s=B.batch(()=>p(this,N).findAll(e).filter(n=>!n.isDisabled()&&!n.isStatic()).map(n=>{let i=n.fetch(void 0,r);return r.throwOnError||(i=i.catch(Y)),n.state.fetchStatus==="paused"?Promise.resolve():i}));return Promise.all(s).then(Y)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const r=p(this,N).build(this,t);return r.isStaleByTime(Pe(t.staleTime,r))?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(Y).catch(Y)}fetchInfiniteQuery(e){return e.behavior=Ls(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(Y).catch(Y)}ensureInfiniteQueryData(e){return e.behavior=Ls(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return Qt.isOnline()?p(this,we).resumePausedMutations():Promise.resolve()}getQueryCache(){return p(this,N)}getMutationCache(){return p(this,we)}getDefaultOptions(){return p(this,Re)}setDefaultOptions(e){C(this,Re,e)}setQueryDefaults(e,t){p(this,tt).set(bt(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...p(this,tt).values()],r={};return t.forEach(s=>{xt(e,s.queryKey)&&Object.assign(r,s.defaultOptions)}),r}setMutationDefaults(e,t){p(this,rt).set(bt(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...p(this,rt).values()],r={};return t.forEach(s=>{xt(e,s.mutationKey)&&Object.assign(r,s.defaultOptions)}),r}defaultQueryOptions(e){if(e._defaulted)return e;const t={...p(this,Re).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=rs(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===ss&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...p(this,Re).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){p(this,N).clear(),p(this,we).clear()}},N=new WeakMap,we=new WeakMap,Re=new WeakMap,tt=new WeakMap,rt=new WeakMap,Ee=new WeakMap,st=new WeakMap,nt=new WeakMap,yn),V,I,Ct,W,qe,it,Ce,je,jt,ot,at,He,We,_e,lt,D,yt,Ur,Mr,$r,Br,zr,qr,Hr,Qn,gn,Ia=(gn=class extends Tt{constructor(t,r){super();O(this,D);O(this,V);O(this,I);O(this,Ct);O(this,W);O(this,qe);O(this,it);O(this,Ce);O(this,je);O(this,jt);O(this,ot);O(this,at);O(this,He);O(this,We);O(this,_e);O(this,lt,new Set);this.options=r,C(this,V,t),C(this,je,null),C(this,Ce,Nr()),this.options.experimental_prefetchInRender||p(this,Ce).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(r)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(p(this,I).addObserver(this),Us(p(this,I),this.options)?A(this,D,yt).call(this):this.updateResult(),A(this,D,Br).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return Wr(p(this,I),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return Wr(p(this,I),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,A(this,D,zr).call(this),A(this,D,qr).call(this),p(this,I).removeObserver(this)}setOptions(t){const r=this.options,s=p(this,I);if(this.options=p(this,V).defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof te(this.options.enabled,p(this,I))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");A(this,D,Hr).call(this),p(this,I).setOptions(this.options),r._defaulted&&!Fr(this.options,r)&&p(this,V).getQueryCache().notify({type:"observerOptionsUpdated",query:p(this,I),observer:this});const n=this.hasListeners();n&&Ms(p(this,I),s,this.options,r)&&A(this,D,yt).call(this),this.updateResult(),n&&(p(this,I)!==s||te(this.options.enabled,p(this,I))!==te(r.enabled,p(this,I))||Pe(this.options.staleTime,p(this,I))!==Pe(r.staleTime,p(this,I)))&&A(this,D,Ur).call(this);const i=A(this,D,Mr).call(this);n&&(p(this,I)!==s||te(this.options.enabled,p(this,I))!==te(r.enabled,p(this,I))||i!==p(this,_e))&&A(this,D,$r).call(this,i)}getOptimisticResult(t){const r=p(this,V).getQueryCache().build(p(this,V),t),s=this.createResult(r,t);return Da(this,s)&&(C(this,W,s),C(this,it,this.options),C(this,qe,p(this,I).state)),s}getCurrentResult(){return p(this,W)}trackResult(t,r){return new Proxy(t,{get:(s,n)=>(this.trackProp(n),r==null||r(n),Reflect.get(s,n))})}trackProp(t){p(this,lt).add(t)}getCurrentQuery(){return p(this,I)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const r=p(this,V).defaultQueryOptions(t),s=p(this,V).getQueryCache().build(p(this,V),r);return s.fetch().then(()=>this.createResult(s,r))}fetch(t){return A(this,D,yt).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),p(this,W)))}createResult(t,r){var E;const s=p(this,I),n=this.options,i=p(this,W),o=p(this,qe),a=p(this,it),d=t!==s?t.state:p(this,Ct),{state:c}=t;let f={...c},y=!1,x;if(r._optimisticResults){const F=this.hasListeners(),se=!F&&Us(t,r),pe=F&&Ms(t,s,r,n);(se||pe)&&(f={...f,...Wn(c.data,t.options)}),r._optimisticResults==="isRestoring"&&(f.fetchStatus="idle")}let{error:v,errorUpdatedAt:m,status:h}=f;x=f.data;let S=!1;if(r.placeholderData!==void 0&&x===void 0&&h==="pending"){let F;i!=null&&i.isPlaceholderData&&r.placeholderData===(a==null?void 0:a.placeholderData)?(F=i.data,S=!0):F=typeof r.placeholderData=="function"?r.placeholderData((E=p(this,at))==null?void 0:E.state.data,p(this,at)):r.placeholderData,F!==void 0&&(h="success",x=Lr(i==null?void 0:i.data,F,r),y=!0)}if(r.select&&x!==void 0&&!S)if(i&&x===(o==null?void 0:o.data)&&r.select===p(this,jt))x=p(this,ot);else try{C(this,jt,r.select),x=r.select(x),x=Lr(i==null?void 0:i.data,x,r),C(this,ot,x),C(this,je,null)}catch(F){C(this,je,F)}p(this,je)&&(v=p(this,je),x=p(this,ot),m=Date.now(),h="error");const b=f.fetchStatus==="fetching",R=h==="pending",_=h==="error",j=R&&b,P=x!==void 0,L={status:h,fetchStatus:f.fetchStatus,isPending:R,isSuccess:h==="success",isError:_,isInitialLoading:j,isLoading:j,data:x,dataUpdatedAt:f.dataUpdatedAt,error:v,errorUpdatedAt:m,failureCount:f.fetchFailureCount,failureReason:f.fetchFailureReason,errorUpdateCount:f.errorUpdateCount,isFetched:f.dataUpdateCount>0||f.errorUpdateCount>0,isFetchedAfterMount:f.dataUpdateCount>d.dataUpdateCount||f.errorUpdateCount>d.errorUpdateCount,isFetching:b,isRefetching:b&&!R,isLoadingError:_&&!P,isPaused:f.fetchStatus==="paused",isPlaceholderData:y,isRefetchError:_&&P,isStale:is(t,r),refetch:this.refetch,promise:p(this,Ce),isEnabled:te(r.enabled,t)!==!1};if(this.options.experimental_prefetchInRender){const F=ve=>{L.status==="error"?ve.reject(L.error):L.data!==void 0&&ve.resolve(L.data)},se=()=>{const ve=C(this,Ce,L.promise=Nr());F(ve)},pe=p(this,Ce);switch(pe.status){case"pending":t.queryHash===s.queryHash&&F(pe);break;case"fulfilled":(L.status==="error"||L.data!==pe.value)&&se();break;case"rejected":(L.status!=="error"||L.error!==pe.reason)&&se();break}}return L}updateResult(){const t=p(this,W),r=this.createResult(p(this,I),this.options);if(C(this,qe,p(this,I).state),C(this,it,this.options),p(this,qe).data!==void 0&&C(this,at,p(this,I)),Fr(r,t))return;C(this,W,r);const s=()=>{if(!t)return!0;const{notifyOnChangeProps:n}=this.options,i=typeof n=="function"?n():n;if(i==="all"||!i&&!p(this,lt).size)return!0;const o=new Set(i??p(this,lt));return this.options.throwOnError&&o.add("error"),Object.keys(p(this,W)).some(a=>{const u=a;return p(this,W)[u]!==t[u]&&o.has(u)})};A(this,D,Qn).call(this,{listeners:s()})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&A(this,D,Br).call(this)}},V=new WeakMap,I=new WeakMap,Ct=new WeakMap,W=new WeakMap,qe=new WeakMap,it=new WeakMap,Ce=new WeakMap,je=new WeakMap,jt=new WeakMap,ot=new WeakMap,at=new WeakMap,He=new WeakMap,We=new WeakMap,_e=new WeakMap,lt=new WeakMap,D=new WeakSet,yt=function(t){A(this,D,Hr).call(this);let r=p(this,I).fetch(this.options,t);return t!=null&&t.throwOnError||(r=r.catch(Y)),r},Ur=function(){A(this,D,zr).call(this);const t=Pe(this.options.staleTime,p(this,I));if(Je||p(this,W).isStale||!Ir(t))return;const s=Un(p(this,W).dataUpdatedAt,t)+1;C(this,He,setTimeout(()=>{p(this,W).isStale||this.updateResult()},s))},Mr=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(p(this,I)):this.options.refetchInterval)??!1},$r=function(t){A(this,D,qr).call(this),C(this,_e,t),!(Je||te(this.options.enabled,p(this,I))===!1||!Ir(p(this,_e))||p(this,_e)===0)&&C(this,We,setInterval(()=>{(this.options.refetchIntervalInBackground||ns.isFocused())&&A(this,D,yt).call(this)},p(this,_e)))},Br=function(){A(this,D,Ur).call(this),A(this,D,$r).call(this,A(this,D,Mr).call(this))},zr=function(){p(this,He)&&(clearTimeout(p(this,He)),C(this,He,void 0))},qr=function(){p(this,We)&&(clearInterval(p(this,We)),C(this,We,void 0))},Hr=function(){const t=p(this,V).getQueryCache().build(p(this,V),this.options);if(t===p(this,I))return;const r=p(this,I);C(this,I,t),C(this,Ct,t.state),this.hasListeners()&&(r==null||r.removeObserver(this),t.addObserver(this))},Qn=function(t){B.batch(()=>{t.listeners&&this.listeners.forEach(r=>{r(p(this,W))}),p(this,V).getQueryCache().notify({query:p(this,I),type:"observerResultsUpdated"})})},gn);function Fa(e,t){return te(t.enabled,e)!==!1&&e.state.data===void 0&&!(e.state.status==="error"&&t.retryOnMount===!1)}function Us(e,t){return Fa(e,t)||e.state.data!==void 0&&Wr(e,t,t.refetchOnMount)}function Wr(e,t,r){if(te(t.enabled,e)!==!1&&Pe(t.staleTime,e)!=="static"){const s=typeof r=="function"?r(e):r;return s==="always"||s!==!1&&is(e,t)}return!1}function Ms(e,t,r,s){return(e!==t||te(s.enabled,e)===!1)&&(!r.suspense||e.state.status!=="error")&&is(e,r)}function is(e,t){return te(t.enabled,e)!==!1&&e.isStaleByTime(Pe(t.staleTime,e))}function Da(e,t){return!Fr(e.getCurrentResult(),t)}var Kn=w.createContext(void 0),La=e=>{const t=w.useContext(Kn);if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},Na=({client:e,children:t})=>(w.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),l.jsx(Kn.Provider,{value:e,children:t})),Jn=w.createContext(!1),Ua=()=>w.useContext(Jn);Jn.Provider;function Ma(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var $a=w.createContext(Ma()),Ba=()=>w.useContext($a),za=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},qa=e=>{w.useEffect(()=>{e.clearReset()},[e])},Ha=({result:e,errorResetBoundary:t,throwOnError:r,query:s,suspense:n})=>e.isError&&!t.isReset()&&!e.isFetching&&s&&(n&&e.data===void 0||ba(r,[e.error,s])),Wa=e=>{if(e.suspense){const t=s=>s==="static"?s:Math.max(s??1e3,1e3),r=e.staleTime;e.staleTime=typeof r=="function"?(...s)=>t(r(...s)):t(r),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3))}},Qa=(e,t)=>e.isLoading&&e.isFetching&&!t,Ka=(e,t)=>(e==null?void 0:e.suspense)&&t.isPending,$s=(e,t,r)=>t.fetchOptimistic(e).catch(()=>{r.clearReset()});function Ja(e,t,r){var f,y,x,v,m;const s=Ua(),n=Ba(),i=La(),o=i.defaultQueryOptions(e);(y=(f=i.getDefaultOptions().queries)==null?void 0:f._experimental_beforeQuery)==null||y.call(f,o),o._optimisticResults=s?"isRestoring":"optimistic",Wa(o),za(o,n),qa(n);const a=!i.getQueryCache().get(o.queryHash),[u]=w.useState(()=>new t(i,o)),d=u.getOptimisticResult(o),c=!s&&e.subscribed!==!1;if(w.useSyncExternalStore(w.useCallback(h=>{const S=c?u.subscribe(B.batchCalls(h)):Y;return u.updateResult(),S},[u,c]),()=>u.getCurrentResult(),()=>u.getCurrentResult()),w.useEffect(()=>{u.setOptions(o)},[o,u]),Ka(o,d))throw $s(o,u,n);if(Ha({result:d,errorResetBoundary:n,throwOnError:o.throwOnError,query:i.getQueryCache().get(o.queryHash),suspense:o.suspense}))throw d.error;if((v=(x=i.getDefaultOptions().queries)==null?void 0:x._experimental_afterQuery)==null||v.call(x,o,d),o.experimental_prefetchInRender&&!Je&&Qa(d,s)){const h=a?$s(o,u,n):(m=i.getQueryCache().get(o.queryHash))==null?void 0:m.promise;h==null||h.catch(Y).finally(()=>{u.updateResult()})}return o.notifyOnChangeProps?d:u.trackResult(d)}function xr(e,t){return Ja(e,Ia)}var Gt={},Vn={exports:{}};(function(e){function t(r){return r&&r.__esModule?r:{default:r}}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports})(Vn);var Xt=Vn.exports,Zt={};Object.defineProperty(Zt,"__esModule",{value:!0});Zt.default=void 0;var Va={items_per_page:"条/页",jump_to:"跳至",jump_to_confirm:"确定",page:"页",prev_page:"上一页",next_page:"下一页",prev_5:"向前 5 页",next_5:"向后 5 页",prev_3:"向前 3 页",next_3:"向后 3 页",page_size:"页码"};Zt.default=Va;var er={},kt={},tr={},Yn={exports:{}},Gn={exports:{}},Xn={exports:{}},Zn={exports:{}};(function(e){function t(r){"@babel/helpers - typeof";return e.exports=t=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(s){return typeof s}:function(s){return s&&typeof Symbol=="function"&&s.constructor===Symbol&&s!==Symbol.prototype?"symbol":typeof s},e.exports.__esModule=!0,e.exports.default=e.exports,t(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports})(Zn);var ei=Zn.exports,ti={exports:{}};(function(e){var t=ei.default;function r(s,n){if(t(s)!="object"||!s)return s;var i=s[Symbol.toPrimitive];if(i!==void 0){var o=i.call(s,n||"default");if(t(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(n==="string"?String:Number)(s)}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports})(ti);var Ya=ti.exports;(function(e){var t=ei.default,r=Ya;function s(n){var i=r(n,"string");return t(i)=="symbol"?i:i+""}e.exports=s,e.exports.__esModule=!0,e.exports.default=e.exports})(Xn);var Ga=Xn.exports;(function(e){var t=Ga;function r(s,n,i){return(n=t(n))in s?Object.defineProperty(s,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):s[n]=i,s}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports})(Gn);var Xa=Gn.exports;(function(e){var t=Xa;function r(n,i){var o=Object.keys(n);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(n);i&&(a=a.filter(function(u){return Object.getOwnPropertyDescriptor(n,u).enumerable})),o.push.apply(o,a)}return o}function s(n){for(var i=1;i<arguments.length;i++){var o=arguments[i]!=null?arguments[i]:{};i%2?r(Object(o),!0).forEach(function(a){t(n,a,o[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(o)):r(Object(o)).forEach(function(a){Object.defineProperty(n,a,Object.getOwnPropertyDescriptor(o,a))})}return n}e.exports=s,e.exports.__esModule=!0,e.exports.default=e.exports})(Yn);var Za=Yn.exports,rr={};Object.defineProperty(rr,"__esModule",{value:!0});rr.commonLocale=void 0;rr.commonLocale={yearFormat:"YYYY",dayFormat:"D",cellMeridiemFormat:"A",monthBeforeYear:!0};var el=Xt.default;Object.defineProperty(tr,"__esModule",{value:!0});tr.default=void 0;var Bs=el(Za),tl=rr,rl=(0,Bs.default)((0,Bs.default)({},tl.commonLocale),{},{locale:"zh_CN",today:"今天",now:"此刻",backToToday:"返回今天",ok:"确定",timeSelect:"选择时间",dateSelect:"选择日期",weekSelect:"选择周",clear:"清除",week:"周",month:"月",year:"年",previousMonth:"上个月 (翻页上键)",nextMonth:"下个月 (翻页下键)",monthSelect:"选择月份",yearSelect:"选择年份",decadeSelect:"选择年代",previousYear:"上一年 (Control键加左方向键)",nextYear:"下一年 (Control键加右方向键)",previousDecade:"上一年代",nextDecade:"下一年代",previousCentury:"上一世纪",nextCentury:"下一世纪",yearFormat:"YYYY年",cellDateFormat:"D",monthBeforeYear:!1});tr.default=rl;var At={};Object.defineProperty(At,"__esModule",{value:!0});At.default=void 0;const sl={placeholder:"请选择时间",rangePlaceholder:["开始时间","结束时间"]};At.default=sl;var ri=Xt.default;Object.defineProperty(kt,"__esModule",{value:!0});kt.default=void 0;var nl=ri(tr),il=ri(At);const si={lang:Object.assign({placeholder:"请选择日期",yearPlaceholder:"请选择年份",quarterPlaceholder:"请选择季度",monthPlaceholder:"请选择月份",weekPlaceholder:"请选择周",rangePlaceholder:["开始日期","结束日期"],rangeYearPlaceholder:["开始年份","结束年份"],rangeMonthPlaceholder:["开始月份","结束月份"],rangeQuarterPlaceholder:["开始季度","结束季度"],rangeWeekPlaceholder:["开始周","结束周"]},nl.default),timePickerLocale:Object.assign({},il.default)};si.lang.ok="确定";kt.default=si;var ol=Xt.default;Object.defineProperty(er,"__esModule",{value:!0});er.default=void 0;var al=ol(kt);er.default=al.default;var sr=Xt.default;Object.defineProperty(Gt,"__esModule",{value:!0});Gt.default=void 0;var ll=sr(Zt),cl=sr(er),ul=sr(kt),dl=sr(At);const Z="${label}不是一个有效的${type}",fl={locale:"zh-cn",Pagination:ll.default,DatePicker:ul.default,TimePicker:dl.default,Calendar:cl.default,global:{placeholder:"请选择",close:"关闭"},Table:{filterTitle:"筛选",filterConfirm:"确定",filterReset:"重置",filterEmptyText:"无筛选项",filterCheckAll:"全选",filterSearchPlaceholder:"在筛选项中搜索",emptyText:"暂无数据",selectAll:"全选当页",selectInvert:"反选当页",selectNone:"清空所有",selectionAll:"全选所有",sortTitle:"排序",expand:"展开行",collapse:"关闭行",triggerDesc:"点击降序",triggerAsc:"点击升序",cancelSort:"取消排序"},Modal:{okText:"确定",cancelText:"取消",justOkText:"知道了"},Tour:{Next:"下一步",Previous:"上一步",Finish:"结束导览"},Popconfirm:{cancelText:"取消",okText:"确定"},Transfer:{titles:["",""],searchPlaceholder:"请输入搜索内容",itemUnit:"项",itemsUnit:"项",remove:"删除",selectCurrent:"全选当页",removeCurrent:"删除当页",selectAll:"全选所有",deselectAll:"取消全选",removeAll:"删除全部",selectInvert:"反选当页"},Upload:{uploading:"文件上传中",removeFile:"删除文件",uploadError:"上传错误",previewFile:"预览文件",downloadFile:"下载文件"},Empty:{description:"暂无数据"},Icon:{icon:"图标"},Text:{edit:"编辑",copy:"复制",copied:"复制成功",expand:"展开",collapse:"收起"},Form:{optional:"（可选）",defaultValidateMessages:{default:"字段验证错误${label}",required:"请输入${label}",enum:"${label}必须是其中一个[${enum}]",whitespace:"${label}不能为空字符",date:{format:"${label}日期格式无效",parse:"${label}不能转换为日期",invalid:"${label}是一个无效日期"},types:{string:Z,method:Z,array:Z,object:Z,number:Z,date:Z,boolean:Z,integer:Z,float:Z,regexp:Z,email:Z,url:Z,hex:Z},string:{len:"${label}须为${len}个字符",min:"${label}最少${min}个字符",max:"${label}最多${max}个字符",range:"${label}须在${min}-${max}字符之间"},number:{len:"${label}必须等于${len}",min:"${label}最小值为${min}",max:"${label}最大值为${max}",range:"${label}须在${min}-${max}之间"},array:{len:"须为${len}个${label}",min:"最少${min}个${label}",max:"最多${max}个${label}",range:"${label}数量须在${min}-${max}之间"},pattern:{mismatch:"${label}与模式不匹配${pattern}"}}},Image:{preview:"预览"},QRCode:{expired:"二维码过期",refresh:"点击刷新",scanned:"已扫描"},ColorPicker:{presetEmpty:"暂无",transparent:"无色",singleColor:"单色",gradientColor:"渐变色"}};Gt.default=fl;var hl=Gt;const pl=vn(hl);var ml={exports:{}};(function(e,t){(function(r,s){e.exports=s(Ni)})(Li,function(r){function s(o){return o&&typeof o=="object"&&"default"in o?o:{default:o}}var n=s(r),i={name:"zh-cn",weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),ordinal:function(o,a){return a==="W"?o+"周":o+"日"},weekStart:1,yearStart:4,formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日Ah点mm分",LLLL:"YYYY年M月D日ddddAh点mm分",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},relativeTime:{future:"%s内",past:"%s前",s:"几秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},meridiem:function(o,a){var u=100*o+a;return u<600?"凌晨":u<900?"早上":u<1100?"上午":u<1300?"中午":u<1800?"下午":"晚上"}};return n.default.locale(i,null,!0),i})})(ml);const yl="modulepreload",gl=function(e){return"/"+e},zs={},ke=function(t,r,s){let n=Promise.resolve();if(r&&r.length>0){document.getElementsByTagName("link");const o=document.querySelector("meta[property=csp-nonce]"),a=(o==null?void 0:o.nonce)||(o==null?void 0:o.getAttribute("nonce"));n=Promise.allSettled(r.map(u=>{if(u=gl(u),u in zs)return;zs[u]=!0;const d=u.endsWith(".css"),c=d?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${u}"]${c}`))return;const f=document.createElement("link");if(f.rel=d?"stylesheet":yl,d||(f.as="script"),f.crossOrigin="",f.href=u,a&&f.setAttribute("nonce",a),document.head.appendChild(f),d)return new Promise((y,x)=>{f.addEventListener("load",y),f.addEventListener("error",()=>x(new Error(`Unable to preload CSS for ${u}`)))})}))}function i(o){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=o,window.dispatchEvent(a),!a.defaultPrevented)throw o}return n.then(o=>{for(const a of o||[])a.status==="rejected"&&i(a.reason);return t().catch(i)})},vl={},qs=e=>{let t;const r=new Set,s=(c,f)=>{const y=typeof c=="function"?c(t):c;if(!Object.is(y,t)){const x=t;t=f??(typeof y!="object"||y===null)?y:Object.assign({},t,y),r.forEach(v=>v(t,x))}},n=()=>t,u={setState:s,getState:n,getInitialState:()=>d,subscribe:c=>(r.add(c),()=>r.delete(c)),destroy:()=>{(vl?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),r.clear()}},d=t=e(s,n,u);return u},bl=e=>e?qs(e):qs;var ni={exports:{}},ii={},oi={exports:{}},ai={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ct=w;function xl(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Sl=typeof Object.is=="function"?Object.is:xl,wl=ct.useState,Rl=ct.useEffect,El=ct.useLayoutEffect,Cl=ct.useDebugValue;function jl(e,t){var r=t(),s=wl({inst:{value:r,getSnapshot:t}}),n=s[0].inst,i=s[1];return El(function(){n.value=r,n.getSnapshot=t,Sr(n)&&i({inst:n})},[e,r,t]),Rl(function(){return Sr(n)&&i({inst:n}),e(function(){Sr(n)&&i({inst:n})})},[e]),Cl(r),r}function Sr(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!Sl(e,r)}catch{return!0}}function _l(e,t){return t()}var Ol=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?_l:jl;ai.useSyncExternalStore=ct.useSyncExternalStore!==void 0?ct.useSyncExternalStore:Ol;oi.exports=ai;var Pl=oi.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var nr=w,Tl=Pl;function kl(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Al=typeof Object.is=="function"?Object.is:kl,Il=Tl.useSyncExternalStore,Fl=nr.useRef,Dl=nr.useEffect,Ll=nr.useMemo,Nl=nr.useDebugValue;ii.useSyncExternalStoreWithSelector=function(e,t,r,s,n){var i=Fl(null);if(i.current===null){var o={hasValue:!1,value:null};i.current=o}else o=i.current;i=Ll(function(){function u(x){if(!d){if(d=!0,c=x,x=s(x),n!==void 0&&o.hasValue){var v=o.value;if(n(v,x))return f=v}return f=x}if(v=f,Al(c,x))return v;var m=s(x);return n!==void 0&&n(v,m)?(c=x,v):(c=x,f=m)}var d=!1,c,f,y=r===void 0?null:r;return[function(){return u(t())},y===null?void 0:function(){return u(y())}]},[t,r,s,n]);var a=Il(e,i[0],i[1]);return Dl(function(){o.hasValue=!0,o.value=a},[a]),Nl(a),a};ni.exports=ii;var Ul=ni.exports;const Ml=vn(Ul),li={},{useDebugValue:$l}=re,{useSyncExternalStoreWithSelector:Bl}=Ml;let Hs=!1;const zl=e=>e;function ql(e,t=zl,r){(li?"production":void 0)!=="production"&&r&&!Hs&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),Hs=!0);const s=Bl(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,r);return $l(s),s}const Hl=e=>{(li?"production":void 0)!=="production"&&typeof e!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t=typeof e=="function"?bl(e):e,r=(s,n)=>ql(t,s,n);return Object.assign(r,t),r},ir=e=>Hl,zt={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1},Qr=new Map,Mt=e=>{const t=Qr.get(e);return t?Object.fromEntries(Object.entries(t.stores).map(([r,s])=>[r,s.getState()])):{}},Wl=(e,t,r)=>{if(e===void 0)return{type:"untracked",connection:t.connect(r)};const s=Qr.get(r.name);if(s)return{type:"tracked",store:e,...s};const n={connection:t.connect(r),stores:{}};return Qr.set(r.name,n),{type:"tracked",store:e,...n}},Ql=(e,t={})=>(r,s,n)=>{const{enabled:i,anonymousActionType:o,store:a,...u}=t;let d;try{d=(i??(zt?"production":void 0)!=="production")&&window.__REDUX_DEVTOOLS_EXTENSION__}catch{}if(!d)return(zt?"production":void 0)!=="production"&&i&&console.warn("[zustand devtools middleware] Please install/enable Redux devtools extension"),e(r,s,n);const{connection:c,...f}=Wl(a,d,u);let y=!0;n.setState=(m,h,S)=>{const b=r(m,h);if(!y)return b;const R=S===void 0?{type:o||"anonymous"}:typeof S=="string"?{type:S}:S;return a===void 0?(c==null||c.send(R,s()),b):(c==null||c.send({...R,type:`${a}/${R.type}`},{...Mt(u.name),[a]:n.getState()}),b)};const x=(...m)=>{const h=y;y=!1,r(...m),y=h},v=e(n.setState,s,n);if(f.type==="untracked"?c==null||c.init(v):(f.stores[f.store]=n,c==null||c.init(Object.fromEntries(Object.entries(f.stores).map(([m,h])=>[m,m===f.store?v:h.getState()])))),n.dispatchFromDevtools&&typeof n.dispatch=="function"){let m=!1;const h=n.dispatch;n.dispatch=(...S)=>{(zt?"production":void 0)!=="production"&&S[0].type==="__setState"&&!m&&(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),m=!0),h(...S)}}return c.subscribe(m=>{var h;switch(m.type){case"ACTION":if(typeof m.payload!="string"){console.error("[zustand devtools middleware] Unsupported action format");return}return wr(m.payload,S=>{if(S.type==="__setState"){if(a===void 0){x(S.state);return}Object.keys(S.state).length!==1&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format. 
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);const b=S.state[a];if(b==null)return;JSON.stringify(n.getState())!==JSON.stringify(b)&&x(b);return}n.dispatchFromDevtools&&typeof n.dispatch=="function"&&n.dispatch(S)});case"DISPATCH":switch(m.payload.type){case"RESET":return x(v),a===void 0?c==null?void 0:c.init(n.getState()):c==null?void 0:c.init(Mt(u.name));case"COMMIT":if(a===void 0){c==null||c.init(n.getState());return}return c==null?void 0:c.init(Mt(u.name));case"ROLLBACK":return wr(m.state,S=>{if(a===void 0){x(S),c==null||c.init(n.getState());return}x(S[a]),c==null||c.init(Mt(u.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return wr(m.state,S=>{if(a===void 0){x(S);return}JSON.stringify(n.getState())!==JSON.stringify(S[a])&&x(S[a])});case"IMPORT_STATE":{const{nextLiftedState:S}=m.payload,b=(h=S.computedStates.slice(-1)[0])==null?void 0:h.state;if(!b)return;x(a===void 0?b:b[a]),c==null||c.send(null,S);return}case"PAUSE_RECORDING":return y=!y}return}}),v},os=Ql,wr=(e,t)=>{let r;try{r=JSON.parse(e)}catch(s){console.error("[zustand devtools middleware] Could not parse the received json",s)}r!==void 0&&t(r)};function Kl(e,t){let r;try{r=e()}catch{return}return{getItem:n=>{var i;const o=u=>u===null?null:JSON.parse(u,void 0),a=(i=r.getItem(n))!=null?i:null;return a instanceof Promise?a.then(o):o(a)},setItem:(n,i)=>r.setItem(n,JSON.stringify(i,void 0)),removeItem:n=>r.removeItem(n)}}const St=e=>t=>{try{const r=e(t);return r instanceof Promise?r:{then(s){return St(s)(r)},catch(s){return this}}}catch(r){return{then(s){return this},catch(s){return St(s)(r)}}}},Jl=(e,t)=>(r,s,n)=>{let i={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:h=>h,version:0,merge:(h,S)=>({...S,...h}),...t},o=!1;const a=new Set,u=new Set;let d;try{d=i.getStorage()}catch{}if(!d)return e((...h)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),r(...h)},s,n);const c=St(i.serialize),f=()=>{const h=i.partialize({...s()});let S;const b=c({state:h,version:i.version}).then(R=>d.setItem(i.name,R)).catch(R=>{S=R});if(S)throw S;return b},y=n.setState;n.setState=(h,S)=>{y(h,S),f()};const x=e((...h)=>{r(...h),f()},s,n);let v;const m=()=>{var h;if(!d)return;o=!1,a.forEach(b=>b(s()));const S=((h=i.onRehydrateStorage)==null?void 0:h.call(i,s()))||void 0;return St(d.getItem.bind(d))(i.name).then(b=>{if(b)return i.deserialize(b)}).then(b=>{if(b)if(typeof b.version=="number"&&b.version!==i.version){if(i.migrate)return i.migrate(b.state,b.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return b.state}).then(b=>{var R;return v=i.merge(b,(R=s())!=null?R:x),r(v,!0),f()}).then(()=>{S==null||S(v,void 0),o=!0,u.forEach(b=>b(v))}).catch(b=>{S==null||S(void 0,b)})};return n.persist={setOptions:h=>{i={...i,...h},h.getStorage&&(d=h.getStorage())},clearStorage:()=>{d==null||d.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>m(),hasHydrated:()=>o,onHydrate:h=>(a.add(h),()=>{a.delete(h)}),onFinishHydration:h=>(u.add(h),()=>{u.delete(h)})},m(),v||x},Vl=(e,t)=>(r,s,n)=>{let i={storage:Kl(()=>localStorage),partialize:m=>m,version:0,merge:(m,h)=>({...h,...m}),...t},o=!1;const a=new Set,u=new Set;let d=i.storage;if(!d)return e((...m)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),r(...m)},s,n);const c=()=>{const m=i.partialize({...s()});return d.setItem(i.name,{state:m,version:i.version})},f=n.setState;n.setState=(m,h)=>{f(m,h),c()};const y=e((...m)=>{r(...m),c()},s,n);n.getInitialState=()=>y;let x;const v=()=>{var m,h;if(!d)return;o=!1,a.forEach(b=>{var R;return b((R=s())!=null?R:y)});const S=((h=i.onRehydrateStorage)==null?void 0:h.call(i,(m=s())!=null?m:y))||void 0;return St(d.getItem.bind(d))(i.name).then(b=>{if(b)if(typeof b.version=="number"&&b.version!==i.version){if(i.migrate)return[!0,i.migrate(b.state,b.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,b.state];return[!1,void 0]}).then(b=>{var R;const[_,j]=b;if(x=i.merge(j,(R=s())!=null?R:y),r(x,!0),_)return c()}).then(()=>{S==null||S(x,void 0),x=s(),o=!0,u.forEach(b=>b(x))}).catch(b=>{S==null||S(void 0,b)})};return n.persist={setOptions:m=>{i={...i,...m},m.storage&&(d=m.storage)},clearStorage:()=>{d==null||d.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>v(),hasHydrated:()=>o,onHydrate:m=>(a.add(m),()=>{a.delete(m)}),onFinishHydration:m=>(u.add(m),()=>{u.delete(m)})},i.skipHydration||v(),x||y},Yl=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?((zt?"production":void 0)!=="production"&&console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),Jl(e,t)):Vl(e,t),Gl=Yl;var Xl=(e=>(e.Quick="quick",e.Standard="standard",e.Deep="deep",e.Custom="custom",e.WebFocused="web_focused",e.NetworkFocused="network_focused",e))(Xl||{}),Q=(e=>(e.Pending="pending",e.Running="running",e.Completed="completed",e.Failed="failed",e.Stopped="stopped",e))(Q||{}),Zl=(e=>(e.Quick="quick",e.Standard="standard",e.Deep="deep",e.Custom="custom",e))(Zl||{}),ec=(e=>(e.SingleHost="single_host",e.Domain="domain",e.Network="network",e.WebAsset="web_asset",e.Mixed="mixed",e))(ec||{}),tc=(e=>(e.Desktop="desktop",e.Mobile="mobile",e.Crawler="crawler",e.ApiClient="api_client",e.Random="random",e))(tc||{}),ye=(e=>(e.Info="info",e.Low="low",e.Medium="medium",e.High="high",e.Critical="critical",e))(ye||{}),ue=(e=>(e.Admin="admin",e.User="user",e.Viewer="viewer",e))(ue||{});function ci(e,t){return function(){return e.apply(t,arguments)}}const{toString:rc}=Object.prototype,{getPrototypeOf:as}=Object,{iterator:or,toStringTag:ui}=Symbol,ar=(e=>t=>{const r=rc.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),oe=e=>(e=e.toLowerCase(),t=>ar(t)===e),lr=e=>t=>typeof t===e,{isArray:dt}=Array,wt=lr("undefined");function sc(e){return e!==null&&!wt(e)&&e.constructor!==null&&!wt(e.constructor)&&G(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const di=oe("ArrayBuffer");function nc(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&di(e.buffer),t}const ic=lr("string"),G=lr("function"),fi=lr("number"),cr=e=>e!==null&&typeof e=="object",oc=e=>e===!0||e===!1,qt=e=>{if(ar(e)!=="object")return!1;const t=as(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(ui in e)&&!(or in e)},ac=oe("Date"),lc=oe("File"),cc=oe("Blob"),uc=oe("FileList"),dc=e=>cr(e)&&G(e.pipe),fc=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||G(e.append)&&((t=ar(e))==="formdata"||t==="object"&&G(e.toString)&&e.toString()==="[object FormData]"))},hc=oe("URLSearchParams"),[pc,mc,yc,gc]=["ReadableStream","Request","Response","Headers"].map(oe),vc=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function It(e,t,{allOwnKeys:r=!1}={}){if(e===null||typeof e>"u")return;let s,n;if(typeof e!="object"&&(e=[e]),dt(e))for(s=0,n=e.length;s<n;s++)t.call(null,e[s],s,e);else{const i=r?Object.getOwnPropertyNames(e):Object.keys(e),o=i.length;let a;for(s=0;s<o;s++)a=i[s],t.call(null,e[a],a,e)}}function hi(e,t){t=t.toLowerCase();const r=Object.keys(e);let s=r.length,n;for(;s-- >0;)if(n=r[s],t===n.toLowerCase())return n;return null}const Le=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,pi=e=>!wt(e)&&e!==Le;function Kr(){const{caseless:e}=pi(this)&&this||{},t={},r=(s,n)=>{const i=e&&hi(t,n)||n;qt(t[i])&&qt(s)?t[i]=Kr(t[i],s):qt(s)?t[i]=Kr({},s):dt(s)?t[i]=s.slice():t[i]=s};for(let s=0,n=arguments.length;s<n;s++)arguments[s]&&It(arguments[s],r);return t}const bc=(e,t,r,{allOwnKeys:s}={})=>(It(t,(n,i)=>{r&&G(n)?e[i]=ci(n,r):e[i]=n},{allOwnKeys:s}),e),xc=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Sc=(e,t,r,s)=>{e.prototype=Object.create(t.prototype,s),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},wc=(e,t,r,s)=>{let n,i,o;const a={};if(t=t||{},e==null)return t;do{for(n=Object.getOwnPropertyNames(e),i=n.length;i-- >0;)o=n[i],(!s||s(o,e,t))&&!a[o]&&(t[o]=e[o],a[o]=!0);e=r!==!1&&as(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},Rc=(e,t,r)=>{e=String(e),(r===void 0||r>e.length)&&(r=e.length),r-=t.length;const s=e.indexOf(t,r);return s!==-1&&s===r},Ec=e=>{if(!e)return null;if(dt(e))return e;let t=e.length;if(!fi(t))return null;const r=new Array(t);for(;t-- >0;)r[t]=e[t];return r},Cc=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&as(Uint8Array)),jc=(e,t)=>{const s=(e&&e[or]).call(e);let n;for(;(n=s.next())&&!n.done;){const i=n.value;t.call(e,i[0],i[1])}},_c=(e,t)=>{let r;const s=[];for(;(r=e.exec(t))!==null;)s.push(r);return s},Oc=oe("HTMLFormElement"),Pc=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,s,n){return s.toUpperCase()+n}),Ws=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),Tc=oe("RegExp"),mi=(e,t)=>{const r=Object.getOwnPropertyDescriptors(e),s={};It(r,(n,i)=>{let o;(o=t(n,i,e))!==!1&&(s[i]=o||n)}),Object.defineProperties(e,s)},kc=e=>{mi(e,(t,r)=>{if(G(e)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const s=e[r];if(G(s)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},Ac=(e,t)=>{const r={},s=n=>{n.forEach(i=>{r[i]=!0})};return dt(e)?s(e):s(String(e).split(t)),r},Ic=()=>{},Fc=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function Dc(e){return!!(e&&G(e.append)&&e[ui]==="FormData"&&e[or])}const Lc=e=>{const t=new Array(10),r=(s,n)=>{if(cr(s)){if(t.indexOf(s)>=0)return;if(!("toJSON"in s)){t[n]=s;const i=dt(s)?[]:{};return It(s,(o,a)=>{const u=r(o,n+1);!wt(u)&&(i[a]=u)}),t[n]=void 0,i}}return s};return r(e,0)},Nc=oe("AsyncFunction"),Uc=e=>e&&(cr(e)||G(e))&&G(e.then)&&G(e.catch),yi=((e,t)=>e?setImmediate:t?((r,s)=>(Le.addEventListener("message",({source:n,data:i})=>{n===Le&&i===r&&s.length&&s.shift()()},!1),n=>{s.push(n),Le.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",G(Le.postMessage)),Mc=typeof queueMicrotask<"u"?queueMicrotask.bind(Le):typeof process<"u"&&process.nextTick||yi,$c=e=>e!=null&&G(e[or]),g={isArray:dt,isArrayBuffer:di,isBuffer:sc,isFormData:fc,isArrayBufferView:nc,isString:ic,isNumber:fi,isBoolean:oc,isObject:cr,isPlainObject:qt,isReadableStream:pc,isRequest:mc,isResponse:yc,isHeaders:gc,isUndefined:wt,isDate:ac,isFile:lc,isBlob:cc,isRegExp:Tc,isFunction:G,isStream:dc,isURLSearchParams:hc,isTypedArray:Cc,isFileList:uc,forEach:It,merge:Kr,extend:bc,trim:vc,stripBOM:xc,inherits:Sc,toFlatObject:wc,kindOf:ar,kindOfTest:oe,endsWith:Rc,toArray:Ec,forEachEntry:jc,matchAll:_c,isHTMLForm:Oc,hasOwnProperty:Ws,hasOwnProp:Ws,reduceDescriptors:mi,freezeMethods:kc,toObjectSet:Ac,toCamelCase:Pc,noop:Ic,toFiniteNumber:Fc,findKey:hi,global:Le,isContextDefined:pi,isSpecCompliantForm:Dc,toJSONObject:Lc,isAsyncFn:Nc,isThenable:Uc,setImmediate:yi,asap:Mc,isIterable:$c};function T(e,t,r,s,n){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),s&&(this.request=s),n&&(this.response=n,this.status=n.status?n.status:null)}g.inherits(T,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:g.toJSONObject(this.config),code:this.code,status:this.status}}});const gi=T.prototype,vi={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{vi[e]={value:e}});Object.defineProperties(T,vi);Object.defineProperty(gi,"isAxiosError",{value:!0});T.from=(e,t,r,s,n,i)=>{const o=Object.create(gi);return g.toFlatObject(e,o,function(u){return u!==Error.prototype},a=>a!=="isAxiosError"),T.call(o,e.message,t,r,s,n),o.cause=e,o.name=e.name,i&&Object.assign(o,i),o};const Bc=null;function Jr(e){return g.isPlainObject(e)||g.isArray(e)}function bi(e){return g.endsWith(e,"[]")?e.slice(0,-2):e}function Qs(e,t,r){return e?e.concat(t).map(function(n,i){return n=bi(n),!r&&i?"["+n+"]":n}).join(r?".":""):t}function zc(e){return g.isArray(e)&&!e.some(Jr)}const qc=g.toFlatObject(g,{},null,function(t){return/^is[A-Z]/.test(t)});function ur(e,t,r){if(!g.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,r=g.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(m,h){return!g.isUndefined(h[m])});const s=r.metaTokens,n=r.visitor||c,i=r.dots,o=r.indexes,u=(r.Blob||typeof Blob<"u"&&Blob)&&g.isSpecCompliantForm(t);if(!g.isFunction(n))throw new TypeError("visitor must be a function");function d(v){if(v===null)return"";if(g.isDate(v))return v.toISOString();if(g.isBoolean(v))return v.toString();if(!u&&g.isBlob(v))throw new T("Blob is not supported. Use a Buffer instead.");return g.isArrayBuffer(v)||g.isTypedArray(v)?u&&typeof Blob=="function"?new Blob([v]):Buffer.from(v):v}function c(v,m,h){let S=v;if(v&&!h&&typeof v=="object"){if(g.endsWith(m,"{}"))m=s?m:m.slice(0,-2),v=JSON.stringify(v);else if(g.isArray(v)&&zc(v)||(g.isFileList(v)||g.endsWith(m,"[]"))&&(S=g.toArray(v)))return m=bi(m),S.forEach(function(R,_){!(g.isUndefined(R)||R===null)&&t.append(o===!0?Qs([m],_,i):o===null?m:m+"[]",d(R))}),!1}return Jr(v)?!0:(t.append(Qs(h,m,i),d(v)),!1)}const f=[],y=Object.assign(qc,{defaultVisitor:c,convertValue:d,isVisitable:Jr});function x(v,m){if(!g.isUndefined(v)){if(f.indexOf(v)!==-1)throw Error("Circular reference detected in "+m.join("."));f.push(v),g.forEach(v,function(S,b){(!(g.isUndefined(S)||S===null)&&n.call(t,S,g.isString(b)?b.trim():b,m,y))===!0&&x(S,m?m.concat(b):[b])}),f.pop()}}if(!g.isObject(e))throw new TypeError("data must be an object");return x(e),t}function Ks(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(s){return t[s]})}function ls(e,t){this._pairs=[],e&&ur(e,this,t)}const xi=ls.prototype;xi.append=function(t,r){this._pairs.push([t,r])};xi.toString=function(t){const r=t?function(s){return t.call(this,s,Ks)}:Ks;return this._pairs.map(function(n){return r(n[0])+"="+r(n[1])},"").join("&")};function Hc(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Si(e,t,r){if(!t)return e;const s=r&&r.encode||Hc;g.isFunction(r)&&(r={serialize:r});const n=r&&r.serialize;let i;if(n?i=n(t,r):i=g.isURLSearchParams(t)?t.toString():new ls(t,r).toString(s),i){const o=e.indexOf("#");o!==-1&&(e=e.slice(0,o)),e+=(e.indexOf("?")===-1?"?":"&")+i}return e}class Js{constructor(){this.handlers=[]}use(t,r,s){return this.handlers.push({fulfilled:t,rejected:r,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){g.forEach(this.handlers,function(s){s!==null&&t(s)})}}const wi={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Wc=typeof URLSearchParams<"u"?URLSearchParams:ls,Qc=typeof FormData<"u"?FormData:null,Kc=typeof Blob<"u"?Blob:null,Jc={isBrowser:!0,classes:{URLSearchParams:Wc,FormData:Qc,Blob:Kc},protocols:["http","https","file","blob","url","data"]},cs=typeof window<"u"&&typeof document<"u",Vr=typeof navigator=="object"&&navigator||void 0,Vc=cs&&(!Vr||["ReactNative","NativeScript","NS"].indexOf(Vr.product)<0),Yc=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Gc=cs&&window.location.href||"http://localhost",Xc=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:cs,hasStandardBrowserEnv:Vc,hasStandardBrowserWebWorkerEnv:Yc,navigator:Vr,origin:Gc},Symbol.toStringTag,{value:"Module"})),q={...Xc,...Jc};function Zc(e,t){return ur(e,new q.classes.URLSearchParams,Object.assign({visitor:function(r,s,n,i){return q.isNode&&g.isBuffer(r)?(this.append(s,r.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)}},t))}function eu(e){return g.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function tu(e){const t={},r=Object.keys(e);let s;const n=r.length;let i;for(s=0;s<n;s++)i=r[s],t[i]=e[i];return t}function Ri(e){function t(r,s,n,i){let o=r[i++];if(o==="__proto__")return!0;const a=Number.isFinite(+o),u=i>=r.length;return o=!o&&g.isArray(n)?n.length:o,u?(g.hasOwnProp(n,o)?n[o]=[n[o],s]:n[o]=s,!a):((!n[o]||!g.isObject(n[o]))&&(n[o]=[]),t(r,s,n[o],i)&&g.isArray(n[o])&&(n[o]=tu(n[o])),!a)}if(g.isFormData(e)&&g.isFunction(e.entries)){const r={};return g.forEachEntry(e,(s,n)=>{t(eu(s),n,r,0)}),r}return null}function ru(e,t,r){if(g.isString(e))try{return(t||JSON.parse)(e),g.trim(e)}catch(s){if(s.name!=="SyntaxError")throw s}return(r||JSON.stringify)(e)}const Ft={transitional:wi,adapter:["xhr","http","fetch"],transformRequest:[function(t,r){const s=r.getContentType()||"",n=s.indexOf("application/json")>-1,i=g.isObject(t);if(i&&g.isHTMLForm(t)&&(t=new FormData(t)),g.isFormData(t))return n?JSON.stringify(Ri(t)):t;if(g.isArrayBuffer(t)||g.isBuffer(t)||g.isStream(t)||g.isFile(t)||g.isBlob(t)||g.isReadableStream(t))return t;if(g.isArrayBufferView(t))return t.buffer;if(g.isURLSearchParams(t))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(i){if(s.indexOf("application/x-www-form-urlencoded")>-1)return Zc(t,this.formSerializer).toString();if((a=g.isFileList(t))||s.indexOf("multipart/form-data")>-1){const u=this.env&&this.env.FormData;return ur(a?{"files[]":t}:t,u&&new u,this.formSerializer)}}return i||n?(r.setContentType("application/json",!1),ru(t)):t}],transformResponse:[function(t){const r=this.transitional||Ft.transitional,s=r&&r.forcedJSONParsing,n=this.responseType==="json";if(g.isResponse(t)||g.isReadableStream(t))return t;if(t&&g.isString(t)&&(s&&!this.responseType||n)){const o=!(r&&r.silentJSONParsing)&&n;try{return JSON.parse(t)}catch(a){if(o)throw a.name==="SyntaxError"?T.from(a,T.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:q.classes.FormData,Blob:q.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};g.forEach(["delete","get","head","post","put","patch"],e=>{Ft.headers[e]={}});const su=g.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),nu=e=>{const t={};let r,s,n;return e&&e.split(`
`).forEach(function(o){n=o.indexOf(":"),r=o.substring(0,n).trim().toLowerCase(),s=o.substring(n+1).trim(),!(!r||t[r]&&su[r])&&(r==="set-cookie"?t[r]?t[r].push(s):t[r]=[s]:t[r]=t[r]?t[r]+", "+s:s)}),t},Vs=Symbol("internals");function mt(e){return e&&String(e).trim().toLowerCase()}function Ht(e){return e===!1||e==null?e:g.isArray(e)?e.map(Ht):String(e)}function iu(e){const t=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=r.exec(e);)t[s[1]]=s[2];return t}const ou=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Rr(e,t,r,s,n){if(g.isFunction(s))return s.call(this,t,r);if(n&&(t=r),!!g.isString(t)){if(g.isString(s))return t.indexOf(s)!==-1;if(g.isRegExp(s))return s.test(t)}}function au(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,r,s)=>r.toUpperCase()+s)}function lu(e,t){const r=g.toCamelCase(" "+t);["get","set","has"].forEach(s=>{Object.defineProperty(e,s+r,{value:function(n,i,o){return this[s].call(this,t,n,i,o)},configurable:!0})})}let X=class{constructor(t){t&&this.set(t)}set(t,r,s){const n=this;function i(a,u,d){const c=mt(u);if(!c)throw new Error("header name must be a non-empty string");const f=g.findKey(n,c);(!f||n[f]===void 0||d===!0||d===void 0&&n[f]!==!1)&&(n[f||u]=Ht(a))}const o=(a,u)=>g.forEach(a,(d,c)=>i(d,c,u));if(g.isPlainObject(t)||t instanceof this.constructor)o(t,r);else if(g.isString(t)&&(t=t.trim())&&!ou(t))o(nu(t),r);else if(g.isObject(t)&&g.isIterable(t)){let a={},u,d;for(const c of t){if(!g.isArray(c))throw TypeError("Object iterator must return a key-value pair");a[d=c[0]]=(u=a[d])?g.isArray(u)?[...u,c[1]]:[u,c[1]]:c[1]}o(a,r)}else t!=null&&i(r,t,s);return this}get(t,r){if(t=mt(t),t){const s=g.findKey(this,t);if(s){const n=this[s];if(!r)return n;if(r===!0)return iu(n);if(g.isFunction(r))return r.call(this,n,s);if(g.isRegExp(r))return r.exec(n);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,r){if(t=mt(t),t){const s=g.findKey(this,t);return!!(s&&this[s]!==void 0&&(!r||Rr(this,this[s],s,r)))}return!1}delete(t,r){const s=this;let n=!1;function i(o){if(o=mt(o),o){const a=g.findKey(s,o);a&&(!r||Rr(s,s[a],a,r))&&(delete s[a],n=!0)}}return g.isArray(t)?t.forEach(i):i(t),n}clear(t){const r=Object.keys(this);let s=r.length,n=!1;for(;s--;){const i=r[s];(!t||Rr(this,this[i],i,t,!0))&&(delete this[i],n=!0)}return n}normalize(t){const r=this,s={};return g.forEach(this,(n,i)=>{const o=g.findKey(s,i);if(o){r[o]=Ht(n),delete r[i];return}const a=t?au(i):String(i).trim();a!==i&&delete r[i],r[a]=Ht(n),s[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const r=Object.create(null);return g.forEach(this,(s,n)=>{s!=null&&s!==!1&&(r[n]=t&&g.isArray(s)?s.join(", "):s)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,r])=>t+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...r){const s=new this(t);return r.forEach(n=>s.set(n)),s}static accessor(t){const s=(this[Vs]=this[Vs]={accessors:{}}).accessors,n=this.prototype;function i(o){const a=mt(o);s[a]||(lu(n,o),s[a]=!0)}return g.isArray(t)?t.forEach(i):i(t),this}};X.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);g.reduceDescriptors(X.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(s){this[r]=s}}});g.freezeMethods(X);function Er(e,t){const r=this||Ft,s=t||r,n=X.from(s.headers);let i=s.data;return g.forEach(e,function(a){i=a.call(r,i,n.normalize(),t?t.status:void 0)}),n.normalize(),i}function Ei(e){return!!(e&&e.__CANCEL__)}function ft(e,t,r){T.call(this,e??"canceled",T.ERR_CANCELED,t,r),this.name="CanceledError"}g.inherits(ft,T,{__CANCEL__:!0});function Ci(e,t,r){const s=r.config.validateStatus;!r.status||!s||s(r.status)?e(r):t(new T("Request failed with status code "+r.status,[T.ERR_BAD_REQUEST,T.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function cu(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function uu(e,t){e=e||10;const r=new Array(e),s=new Array(e);let n=0,i=0,o;return t=t!==void 0?t:1e3,function(u){const d=Date.now(),c=s[i];o||(o=d),r[n]=u,s[n]=d;let f=i,y=0;for(;f!==n;)y+=r[f++],f=f%e;if(n=(n+1)%e,n===i&&(i=(i+1)%e),d-o<t)return;const x=c&&d-c;return x?Math.round(y*1e3/x):void 0}}function du(e,t){let r=0,s=1e3/t,n,i;const o=(d,c=Date.now())=>{r=c,n=null,i&&(clearTimeout(i),i=null),e.apply(null,d)};return[(...d)=>{const c=Date.now(),f=c-r;f>=s?o(d,c):(n=d,i||(i=setTimeout(()=>{i=null,o(n)},s-f)))},()=>n&&o(n)]}const Kt=(e,t,r=3)=>{let s=0;const n=uu(50,250);return du(i=>{const o=i.loaded,a=i.lengthComputable?i.total:void 0,u=o-s,d=n(u),c=o<=a;s=o;const f={loaded:o,total:a,progress:a?o/a:void 0,bytes:u,rate:d||void 0,estimated:d&&a&&c?(a-o)/d:void 0,event:i,lengthComputable:a!=null,[t?"download":"upload"]:!0};e(f)},r)},Ys=(e,t)=>{const r=e!=null;return[s=>t[0]({lengthComputable:r,total:e,loaded:s}),t[1]]},Gs=e=>(...t)=>g.asap(()=>e(...t)),fu=q.hasStandardBrowserEnv?((e,t)=>r=>(r=new URL(r,q.origin),e.protocol===r.protocol&&e.host===r.host&&(t||e.port===r.port)))(new URL(q.origin),q.navigator&&/(msie|trident)/i.test(q.navigator.userAgent)):()=>!0,hu=q.hasStandardBrowserEnv?{write(e,t,r,s,n,i){const o=[e+"="+encodeURIComponent(t)];g.isNumber(r)&&o.push("expires="+new Date(r).toGMTString()),g.isString(s)&&o.push("path="+s),g.isString(n)&&o.push("domain="+n),i===!0&&o.push("secure"),document.cookie=o.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function pu(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function mu(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function ji(e,t,r){let s=!pu(t);return e&&(s||r==!1)?mu(e,t):t}const Xs=e=>e instanceof X?{...e}:e;function Ve(e,t){t=t||{};const r={};function s(d,c,f,y){return g.isPlainObject(d)&&g.isPlainObject(c)?g.merge.call({caseless:y},d,c):g.isPlainObject(c)?g.merge({},c):g.isArray(c)?c.slice():c}function n(d,c,f,y){if(g.isUndefined(c)){if(!g.isUndefined(d))return s(void 0,d,f,y)}else return s(d,c,f,y)}function i(d,c){if(!g.isUndefined(c))return s(void 0,c)}function o(d,c){if(g.isUndefined(c)){if(!g.isUndefined(d))return s(void 0,d)}else return s(void 0,c)}function a(d,c,f){if(f in t)return s(d,c);if(f in e)return s(void 0,d)}const u={url:i,method:i,data:i,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:a,headers:(d,c,f)=>n(Xs(d),Xs(c),f,!0)};return g.forEach(Object.keys(Object.assign({},e,t)),function(c){const f=u[c]||n,y=f(e[c],t[c],c);g.isUndefined(y)&&f!==a||(r[c]=y)}),r}const _i=e=>{const t=Ve({},e);let{data:r,withXSRFToken:s,xsrfHeaderName:n,xsrfCookieName:i,headers:o,auth:a}=t;t.headers=o=X.from(o),t.url=Si(ji(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),a&&o.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let u;if(g.isFormData(r)){if(q.hasStandardBrowserEnv||q.hasStandardBrowserWebWorkerEnv)o.setContentType(void 0);else if((u=o.getContentType())!==!1){const[d,...c]=u?u.split(";").map(f=>f.trim()).filter(Boolean):[];o.setContentType([d||"multipart/form-data",...c].join("; "))}}if(q.hasStandardBrowserEnv&&(s&&g.isFunction(s)&&(s=s(t)),s||s!==!1&&fu(t.url))){const d=n&&i&&hu.read(i);d&&o.set(n,d)}return t},yu=typeof XMLHttpRequest<"u",gu=yu&&function(e){return new Promise(function(r,s){const n=_i(e);let i=n.data;const o=X.from(n.headers).normalize();let{responseType:a,onUploadProgress:u,onDownloadProgress:d}=n,c,f,y,x,v;function m(){x&&x(),v&&v(),n.cancelToken&&n.cancelToken.unsubscribe(c),n.signal&&n.signal.removeEventListener("abort",c)}let h=new XMLHttpRequest;h.open(n.method.toUpperCase(),n.url,!0),h.timeout=n.timeout;function S(){if(!h)return;const R=X.from("getAllResponseHeaders"in h&&h.getAllResponseHeaders()),j={data:!a||a==="text"||a==="json"?h.responseText:h.response,status:h.status,statusText:h.statusText,headers:R,config:e,request:h};Ci(function(U){r(U),m()},function(U){s(U),m()},j),h=null}"onloadend"in h?h.onloadend=S:h.onreadystatechange=function(){!h||h.readyState!==4||h.status===0&&!(h.responseURL&&h.responseURL.indexOf("file:")===0)||setTimeout(S)},h.onabort=function(){h&&(s(new T("Request aborted",T.ECONNABORTED,e,h)),h=null)},h.onerror=function(){s(new T("Network Error",T.ERR_NETWORK,e,h)),h=null},h.ontimeout=function(){let _=n.timeout?"timeout of "+n.timeout+"ms exceeded":"timeout exceeded";const j=n.transitional||wi;n.timeoutErrorMessage&&(_=n.timeoutErrorMessage),s(new T(_,j.clarifyTimeoutError?T.ETIMEDOUT:T.ECONNABORTED,e,h)),h=null},i===void 0&&o.setContentType(null),"setRequestHeader"in h&&g.forEach(o.toJSON(),function(_,j){h.setRequestHeader(j,_)}),g.isUndefined(n.withCredentials)||(h.withCredentials=!!n.withCredentials),a&&a!=="json"&&(h.responseType=n.responseType),d&&([y,v]=Kt(d,!0),h.addEventListener("progress",y)),u&&h.upload&&([f,x]=Kt(u),h.upload.addEventListener("progress",f),h.upload.addEventListener("loadend",x)),(n.cancelToken||n.signal)&&(c=R=>{h&&(s(!R||R.type?new ft(null,e,h):R),h.abort(),h=null)},n.cancelToken&&n.cancelToken.subscribe(c),n.signal&&(n.signal.aborted?c():n.signal.addEventListener("abort",c)));const b=cu(n.url);if(b&&q.protocols.indexOf(b)===-1){s(new T("Unsupported protocol "+b+":",T.ERR_BAD_REQUEST,e));return}h.send(i||null)})},vu=(e,t)=>{const{length:r}=e=e?e.filter(Boolean):[];if(t||r){let s=new AbortController,n;const i=function(d){if(!n){n=!0,a();const c=d instanceof Error?d:this.reason;s.abort(c instanceof T?c:new ft(c instanceof Error?c.message:c))}};let o=t&&setTimeout(()=>{o=null,i(new T(`timeout ${t} of ms exceeded`,T.ETIMEDOUT))},t);const a=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(d=>{d.unsubscribe?d.unsubscribe(i):d.removeEventListener("abort",i)}),e=null)};e.forEach(d=>d.addEventListener("abort",i));const{signal:u}=s;return u.unsubscribe=()=>g.asap(a),u}},bu=function*(e,t){let r=e.byteLength;if(r<t){yield e;return}let s=0,n;for(;s<r;)n=s+t,yield e.slice(s,n),s=n},xu=async function*(e,t){for await(const r of Su(e))yield*bu(r,t)},Su=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:r,value:s}=await t.read();if(r)break;yield s}}finally{await t.cancel()}},Zs=(e,t,r,s)=>{const n=xu(e,t);let i=0,o,a=u=>{o||(o=!0,s&&s(u))};return new ReadableStream({async pull(u){try{const{done:d,value:c}=await n.next();if(d){a(),u.close();return}let f=c.byteLength;if(r){let y=i+=f;r(y)}u.enqueue(new Uint8Array(c))}catch(d){throw a(d),d}},cancel(u){return a(u),n.return()}},{highWaterMark:2})},dr=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Oi=dr&&typeof ReadableStream=="function",wu=dr&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Pi=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Ru=Oi&&Pi(()=>{let e=!1;const t=new Request(q.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),en=64*1024,Yr=Oi&&Pi(()=>g.isReadableStream(new Response("").body)),Jt={stream:Yr&&(e=>e.body)};dr&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Jt[t]&&(Jt[t]=g.isFunction(e[t])?r=>r[t]():(r,s)=>{throw new T(`Response type '${t}' is not supported`,T.ERR_NOT_SUPPORT,s)})})})(new Response);const Eu=async e=>{if(e==null)return 0;if(g.isBlob(e))return e.size;if(g.isSpecCompliantForm(e))return(await new Request(q.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(g.isArrayBufferView(e)||g.isArrayBuffer(e))return e.byteLength;if(g.isURLSearchParams(e)&&(e=e+""),g.isString(e))return(await wu(e)).byteLength},Cu=async(e,t)=>{const r=g.toFiniteNumber(e.getContentLength());return r??Eu(t)},ju=dr&&(async e=>{let{url:t,method:r,data:s,signal:n,cancelToken:i,timeout:o,onDownloadProgress:a,onUploadProgress:u,responseType:d,headers:c,withCredentials:f="same-origin",fetchOptions:y}=_i(e);d=d?(d+"").toLowerCase():"text";let x=vu([n,i&&i.toAbortSignal()],o),v;const m=x&&x.unsubscribe&&(()=>{x.unsubscribe()});let h;try{if(u&&Ru&&r!=="get"&&r!=="head"&&(h=await Cu(c,s))!==0){let j=new Request(t,{method:"POST",body:s,duplex:"half"}),P;if(g.isFormData(s)&&(P=j.headers.get("content-type"))&&c.setContentType(P),j.body){const[U,L]=Ys(h,Kt(Gs(u)));s=Zs(j.body,en,U,L)}}g.isString(f)||(f=f?"include":"omit");const S="credentials"in Request.prototype;v=new Request(t,{...y,signal:x,method:r.toUpperCase(),headers:c.normalize().toJSON(),body:s,duplex:"half",credentials:S?f:void 0});let b=await fetch(v,y);const R=Yr&&(d==="stream"||d==="response");if(Yr&&(a||R&&m)){const j={};["status","statusText","headers"].forEach(E=>{j[E]=b[E]});const P=g.toFiniteNumber(b.headers.get("content-length")),[U,L]=a&&Ys(P,Kt(Gs(a),!0))||[];b=new Response(Zs(b.body,en,U,()=>{L&&L(),m&&m()}),j)}d=d||"text";let _=await Jt[g.findKey(Jt,d)||"text"](b,e);return!R&&m&&m(),await new Promise((j,P)=>{Ci(j,P,{data:_,headers:X.from(b.headers),status:b.status,statusText:b.statusText,config:e,request:v})})}catch(S){throw m&&m(),S&&S.name==="TypeError"&&/Load failed|fetch/i.test(S.message)?Object.assign(new T("Network Error",T.ERR_NETWORK,e,v),{cause:S.cause||S}):T.from(S,S&&S.code,e,v)}}),Gr={http:Bc,xhr:gu,fetch:ju};g.forEach(Gr,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const tn=e=>`- ${e}`,_u=e=>g.isFunction(e)||e===null||e===!1,Ti={getAdapter:e=>{e=g.isArray(e)?e:[e];const{length:t}=e;let r,s;const n={};for(let i=0;i<t;i++){r=e[i];let o;if(s=r,!_u(r)&&(s=Gr[(o=String(r)).toLowerCase()],s===void 0))throw new T(`Unknown adapter '${o}'`);if(s)break;n[o||"#"+i]=s}if(!s){const i=Object.entries(n).map(([a,u])=>`adapter ${a} `+(u===!1?"is not supported by the environment":"is not available in the build"));let o=t?i.length>1?`since :
`+i.map(tn).join(`
`):" "+tn(i[0]):"as no adapter specified";throw new T("There is no suitable adapter to dispatch the request "+o,"ERR_NOT_SUPPORT")}return s},adapters:Gr};function Cr(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new ft(null,e)}function rn(e){return Cr(e),e.headers=X.from(e.headers),e.data=Er.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Ti.getAdapter(e.adapter||Ft.adapter)(e).then(function(s){return Cr(e),s.data=Er.call(e,e.transformResponse,s),s.headers=X.from(s.headers),s},function(s){return Ei(s)||(Cr(e),s&&s.response&&(s.response.data=Er.call(e,e.transformResponse,s.response),s.response.headers=X.from(s.response.headers))),Promise.reject(s)})}const ki="1.10.0",fr={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{fr[e]=function(s){return typeof s===e||"a"+(t<1?"n ":" ")+e}});const sn={};fr.transitional=function(t,r,s){function n(i,o){return"[Axios v"+ki+"] Transitional option '"+i+"'"+o+(s?". "+s:"")}return(i,o,a)=>{if(t===!1)throw new T(n(o," has been removed"+(r?" in "+r:"")),T.ERR_DEPRECATED);return r&&!sn[o]&&(sn[o]=!0,console.warn(n(o," has been deprecated since v"+r+" and will be removed in the near future"))),t?t(i,o,a):!0}};fr.spelling=function(t){return(r,s)=>(console.warn(`${s} is likely a misspelling of ${t}`),!0)};function Ou(e,t,r){if(typeof e!="object")throw new T("options must be an object",T.ERR_BAD_OPTION_VALUE);const s=Object.keys(e);let n=s.length;for(;n-- >0;){const i=s[n],o=t[i];if(o){const a=e[i],u=a===void 0||o(a,i,e);if(u!==!0)throw new T("option "+i+" must be "+u,T.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new T("Unknown option "+i,T.ERR_BAD_OPTION)}}const Wt={assertOptions:Ou,validators:fr},ae=Wt.validators;let Ke=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Js,response:new Js}}async request(t,r){try{return await this._request(t,r)}catch(s){if(s instanceof Error){let n={};Error.captureStackTrace?Error.captureStackTrace(n):n=new Error;const i=n.stack?n.stack.replace(/^.+\n/,""):"";try{s.stack?i&&!String(s.stack).endsWith(i.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+i):s.stack=i}catch{}}throw s}}_request(t,r){typeof t=="string"?(r=r||{},r.url=t):r=t||{},r=Ve(this.defaults,r);const{transitional:s,paramsSerializer:n,headers:i}=r;s!==void 0&&Wt.assertOptions(s,{silentJSONParsing:ae.transitional(ae.boolean),forcedJSONParsing:ae.transitional(ae.boolean),clarifyTimeoutError:ae.transitional(ae.boolean)},!1),n!=null&&(g.isFunction(n)?r.paramsSerializer={serialize:n}:Wt.assertOptions(n,{encode:ae.function,serialize:ae.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),Wt.assertOptions(r,{baseUrl:ae.spelling("baseURL"),withXsrfToken:ae.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let o=i&&g.merge(i.common,i[r.method]);i&&g.forEach(["delete","get","head","post","put","patch","common"],v=>{delete i[v]}),r.headers=X.concat(o,i);const a=[];let u=!0;this.interceptors.request.forEach(function(m){typeof m.runWhen=="function"&&m.runWhen(r)===!1||(u=u&&m.synchronous,a.unshift(m.fulfilled,m.rejected))});const d=[];this.interceptors.response.forEach(function(m){d.push(m.fulfilled,m.rejected)});let c,f=0,y;if(!u){const v=[rn.bind(this),void 0];for(v.unshift.apply(v,a),v.push.apply(v,d),y=v.length,c=Promise.resolve(r);f<y;)c=c.then(v[f++],v[f++]);return c}y=a.length;let x=r;for(f=0;f<y;){const v=a[f++],m=a[f++];try{x=v(x)}catch(h){m.call(this,h);break}}try{c=rn.call(this,x)}catch(v){return Promise.reject(v)}for(f=0,y=d.length;f<y;)c=c.then(d[f++],d[f++]);return c}getUri(t){t=Ve(this.defaults,t);const r=ji(t.baseURL,t.url,t.allowAbsoluteUrls);return Si(r,t.params,t.paramsSerializer)}};g.forEach(["delete","get","head","options"],function(t){Ke.prototype[t]=function(r,s){return this.request(Ve(s||{},{method:t,url:r,data:(s||{}).data}))}});g.forEach(["post","put","patch"],function(t){function r(s){return function(i,o,a){return this.request(Ve(a||{},{method:t,headers:s?{"Content-Type":"multipart/form-data"}:{},url:i,data:o}))}}Ke.prototype[t]=r(),Ke.prototype[t+"Form"]=r(!0)});let Pu=class Ai{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(i){r=i});const s=this;this.promise.then(n=>{if(!s._listeners)return;let i=s._listeners.length;for(;i-- >0;)s._listeners[i](n);s._listeners=null}),this.promise.then=n=>{let i;const o=new Promise(a=>{s.subscribe(a),i=a}).then(n);return o.cancel=function(){s.unsubscribe(i)},o},t(function(i,o,a){s.reason||(s.reason=new ft(i,o,a),r(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const r=this._listeners.indexOf(t);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const t=new AbortController,r=s=>{t.abort(s)};return this.subscribe(r),t.signal.unsubscribe=()=>this.unsubscribe(r),t.signal}static source(){let t;return{token:new Ai(function(n){t=n}),cancel:t}}};function Tu(e){return function(r){return e.apply(null,r)}}function ku(e){return g.isObject(e)&&e.isAxiosError===!0}const Xr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Xr).forEach(([e,t])=>{Xr[t]=e});function Ii(e){const t=new Ke(e),r=ci(Ke.prototype.request,t);return g.extend(r,Ke.prototype,t,{allOwnKeys:!0}),g.extend(r,t,null,{allOwnKeys:!0}),r.create=function(n){return Ii(Ve(e,n))},r}const M=Ii(Ft);M.Axios=Ke;M.CanceledError=ft;M.CancelToken=Pu;M.isCancel=Ei;M.VERSION=ki;M.toFormData=ur;M.AxiosError=T;M.Cancel=M.CanceledError;M.all=function(t){return Promise.all(t)};M.spread=Tu;M.isAxiosError=ku;M.mergeConfig=Ve;M.AxiosHeaders=X;M.formToJSON=e=>Ri(g.isHTMLForm(e)?new FormData(e):e);M.getAdapter=Ti.getAdapter;M.HttpStatusCode=Xr;M.default=M;const{Axios:ad,AxiosError:ld,CanceledError:cd,isCancel:ud,CancelToken:dd,VERSION:fd,all:hd,Cancel:pd,isAxiosError:md,spread:yd,toFormData:gd,AxiosHeaders:vd,HttpStatusCode:bd,formToJSON:xd,getAdapter:Sd,mergeConfig:wd}=M,k=M.create({baseURL:"http://localhost:8080/api",timeout:3e4,headers:{"Content-Type":"application/json"}});k.interceptors.request.use(e=>{const t=localStorage.getItem("token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e));k.interceptors.response.use(e=>e,e=>{var t;return console.error("API Error:",e),((t=e.response)==null?void 0:t.status)===401&&(localStorage.removeItem("token"),localStorage.removeItem("user"),window.location.href="/login"),Promise.reject(e)});const Au={getTasks:(e=1,t=20)=>k.get("/tasks",{params:{page:e,size:t}}),getTask:e=>k.get(`/tasks/${e}`),createTask:e=>k.post("/tasks",e),startTask:e=>k.post(`/tasks/${e}/start`),stopTask:e=>k.post(`/tasks/${e}/stop`),deleteTask:e=>k.delete(`/tasks/${e}`),restartTask:e=>k.post(`/tasks/${e}/restart`)},nn={getHosts:e=>k.get("/results/hosts"+(e?`?${e}`:"")),getPorts:e=>k.get("/results/ports"+(e?`?${e}`:"")),getSubdomains:e=>k.get("/results/subdomains"+(e?`?${e}`:"")),getWebAssets:e=>k.get("/results/web-assets"+(e?`?${e}`:"")),getCrawledPages:e=>k.get("/results/crawled-pages"+(e?`?${e}`:"")),getVulnerabilities:e=>k.get("/results/vulnerabilities"+(e?`?${e}`:"")),getStatistics:e=>k.get("/results/statistics",{params:{task_id:e}}),exportResults:(e,t)=>k.get(`/results/${e}/export`,{params:{format:t},responseType:"blob"})},Rd={getStatus:()=>k.get("/system/status"),getInfo:()=>k.get("/system/info"),checkTools:()=>k.get("/system/tools"),getLogs:(e,t=100)=>k.get("/system/logs",{params:{level:e,limit:t}}),getHealth:()=>k.get("/system/health"),getDatabaseStats:()=>k.get("/system/database/stats"),optimizeDatabase:e=>k.post("/system/database/optimize",e),getPerformanceRecommendations:()=>k.get("/system/database/recommendations"),runPerformanceTest:()=>k.get("/system/database/performance-test"),cleanupCompletedTasks:e=>k.post("/system/database/cleanup-tasks",{retention_days:e}),getSystemMetrics:()=>k.get("/system/metrics"),getSystemAlerts:()=>k.get("/system/alerts"),getMonitoringDashboard:()=>k.get("/system/monitoring/dashboard")},on={login:e=>k.post("/auth/login",e),register:e=>k.post("/auth/register",e),getProfile:()=>k.get("/user/profile"),changePassword:e=>k.post("/user/change-password",e),getUsers:(e=1,t=20)=>k.get("/user/users",{params:{page:e,size:t}}),updateUser:(e,t)=>k.put(`/user/users/${e}`,t),deleteUser:e=>k.delete(`/user/users/${e}`)},us=ir()(Gl((e,t)=>({user:null,token:null,isAuthenticated:!1,isLoading:!1,login:async(r,s)=>{e({isLoading:!0});try{const n=await on.login({username:r,password:s});if(n.data.success&&n.data.data){const{access_token:i,user:o}=n.data.data;return localStorage.setItem("token",i),localStorage.setItem("user",JSON.stringify(o)),e({user:o,token:i,isAuthenticated:!0,isLoading:!1}),!0}return e({isLoading:!1}),!1}catch(n){return console.error("Login failed:",n),e({isLoading:!1}),!1}},logout:()=>{localStorage.removeItem("token"),localStorage.removeItem("user"),e({user:null,token:null,isAuthenticated:!1})},updateProfile:r=>{localStorage.setItem("user",JSON.stringify(r)),e({user:r})},checkAuth:async()=>{const r=localStorage.getItem("token"),s=localStorage.getItem("user");if(!r||!s)return!1;try{const n=await on.getProfile();if(n.data.success&&n.data.data){const i=n.data.data;return e({user:i,token:r,isAuthenticated:!0}),!0}return t().logout(),!1}catch(n){return console.error("Auth check failed:",n),t().logout(),!1}}}),{name:"auth-storage",partialize:e=>({user:e.user,token:e.token,isAuthenticated:e.isAuthenticated})})),an=e=>e===ue.Admin,{Title:ln,Text:Ie}=Zr,Iu=()=>{var u,d;const[e]=pt.useForm(),[t,r]=w.useState(!1),s=ts(),n=Pt(),{login:i}=us(),o=((d=(u=n.state)==null?void 0:u.from)==null?void 0:d.pathname)||"/",a=async c=>{var f,y;r(!0);try{await i(c.username,c.password)?(Bt.success("登录成功！"),s(o,{replace:!0})):Bt.error("用户名或密码错误")}catch(x){Bt.error(((y=(f=x.response)==null?void 0:f.data)==null?void 0:y.error)||"登录失败，请稍后重试")}finally{r(!1)}};return l.jsx("div",{style:{minHeight:"100vh",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",display:"flex",alignItems:"center",justifyContent:"center",padding:"20px"},children:l.jsxs($t,{style:{width:"100%",maxWidth:"1200px"},children:[l.jsx(ce,{xs:24,md:12,style:{display:"flex",alignItems:"center",justifyContent:"center"},children:l.jsxs("div",{style:{textAlign:"center",color:"white",padding:"0 40px"},children:[l.jsx(Ui,{style:{fontSize:"80px",marginBottom:"20px"}}),l.jsx(ln,{level:1,style:{color:"white",marginBottom:"16px"},children:"RustScan Web"}),l.jsx(Ie,{style:{fontSize:"18px",color:"rgba(255, 255, 255, 0.8)"},children:"强大的网络安全扫描平台"}),l.jsxs("div",{style:{marginTop:"30px"},children:[l.jsx(Ie,{style:{color:"rgba(255, 255, 255, 0.7)",display:"block",marginBottom:"8px"},children:"• 多工具集成扫描"}),l.jsx(Ie,{style:{color:"rgba(255, 255, 255, 0.7)",display:"block",marginBottom:"8px"},children:"• 实时扫描监控"}),l.jsx(Ie,{style:{color:"rgba(255, 255, 255, 0.7)",display:"block",marginBottom:"8px"},children:"• 智能结果分析"}),l.jsx(Ie,{style:{color:"rgba(255, 255, 255, 0.7)",display:"block"},children:"• 可视化数据展示"})]})]})}),l.jsx(ce,{xs:24,md:12,style:{display:"flex",alignItems:"center",justifyContent:"center"},children:l.jsxs(le,{style:{width:"100%",maxWidth:"400px",boxShadow:"0 8px 32px rgba(0, 0, 0, 0.1)",borderRadius:"12px"},children:[l.jsxs("div",{style:{textAlign:"center",marginBottom:"32px"},children:[l.jsx(ln,{level:3,style:{marginBottom:"8px"},children:"用户登录"}),l.jsx(Ie,{type:"secondary",children:"请输入您的账号密码"})]}),l.jsxs(pt,{form:e,name:"login",onFinish:a,autoComplete:"off",size:"large",children:[l.jsx(pt.Item,{name:"username",rules:[{required:!0,message:"请输入用户名"},{min:3,message:"用户名至少3个字符"}],children:l.jsx(ps,{prefix:l.jsx(_r,{}),placeholder:"用户名"})}),l.jsx(pt.Item,{name:"password",rules:[{required:!0,message:"请输入密码"},{min:6,message:"密码至少6个字符"}],children:l.jsx(ps.Password,{prefix:l.jsx(Mi,{}),placeholder:"密码"})}),l.jsx(pt.Item,{style:{marginBottom:"16px"},children:l.jsx(De,{type:"primary",htmlType:"submit",style:{width:"100%"},loading:t,children:"登录"})})]}),l.jsx("div",{style:{textAlign:"center"},children:l.jsx(Ie,{type:"secondary",style:{fontSize:"12px"},children:"默认管理员账号: admin / admin123"})})]})})]})})},ds=ir()(os((e,t)=>({tasks:[],activeTasks:[],selectedTask:null,isLoading:!1,error:null,setTasks:r=>e(s=>({...s,tasks:r,activeTasks:r.filter(n=>n.status===Q.Running||n.status===Q.Pending)})),addTask:r=>e(s=>{const n=[...s.tasks,r];return{...s,tasks:n,activeTasks:n.filter(i=>i.status===Q.Running||i.status===Q.Pending)}}),updateTask:(r,s)=>e(n=>{var o;const i=n.tasks.map(a=>a.id===r?{...a,...s}:a);return{...n,tasks:i,activeTasks:i.filter(a=>a.status===Q.Running||a.status===Q.Pending),selectedTask:((o=n.selectedTask)==null?void 0:o.id)===r?{...n.selectedTask,...s}:n.selectedTask}}),removeTask:r=>e(s=>{var i;const n=s.tasks.filter(o=>o.id!==r);return{...s,tasks:n,activeTasks:n.filter(o=>o.status===Q.Running||o.status===Q.Pending),selectedTask:((i=s.selectedTask)==null?void 0:i.id)===r?null:s.selectedTask}}),setSelectedTask:r=>e({selectedTask:r}),setLoading:r=>e({isLoading:r}),setError:r=>e({error:r}),getTaskById:r=>t().tasks.find(s=>s.id===r),getTasksByStatus:r=>t().tasks.filter(s=>s.status===r)}),{name:"task-store"})),fs=ir()(os((e,t)=>({connected:!1,logs:[],maxLogs:1e3,setConnected:r=>e({connected:r}),addLog:r=>e(s=>{const n=[...s.logs,r];return n.length>s.maxLogs&&n.splice(0,n.length-s.maxLogs),{...s,logs:n}}),clearLogs:()=>e({logs:[]}),setMaxLogs:r=>e({maxLogs:r})}),{name:"websocket-store"})),Fu=ir()(os(e=>({theme:"auto",language:"zh-CN",autoRefresh:!0,refreshInterval:3e4,showNotifications:!0,terminalTheme:"dark",pageSize:20,setTheme:t=>e({theme:t}),setLanguage:t=>e({language:t}),setAutoRefresh:t=>e({autoRefresh:t}),setRefreshInterval:t=>e({refreshInterval:t}),setShowNotifications:t=>e({showNotifications:t}),setTerminalTheme:t=>e({terminalTheme:t}),setPageSize:t=>e({pageSize:t})}),{name:"settings-store"})),{Title:Du,Text:K}=Zr,Lu=()=>{var j,P,U,L;const{tasks:e,activeTasks:t}=ds(),{connected:r,logs:s}=fs(),[n,i]=w.useState([]),{data:o,isLoading:a,refetch:u}=xr({queryKey:["statistics"],queryFn:()=>nn.getStatistics(),refetchInterval:3e4}),{data:d,refetch:c}=xr({queryKey:["recent-vulnerabilities"],queryFn:()=>nn.getVulnerabilities(),refetchInterval:3e4}),{data:f}=xr({queryKey:["task-history"],queryFn:()=>Au.getTasks(1,20),refetchInterval:6e4}),y=(j=o==null?void 0:o.data)==null?void 0:j.data,x=((P=d==null?void 0:d.data)==null?void 0:P.items)||[],v=((U=f==null?void 0:f.data)==null?void 0:U.data)||[],m={total:e.length,running:e.filter(E=>E.status===Q.Running).length,completed:e.filter(E=>E.status===Q.Completed).length,failed:e.filter(E=>E.status===Q.Failed).length};w.useEffect(()=>{const E=Fe(),F=[];for(let se=23;se>=0;se--){const pe=E.subtract(se,"hour"),ve=v.filter(ht=>Fe(ht.created_at).isSame(pe,"hour"));F.push({hour:pe.format("HH:mm"),scans:ve.length,completed:ve.filter(ht=>ht.status===Q.Completed).length,failed:ve.filter(ht=>ht.status===Q.Failed).length})}i(F)},[v]);const h={[ye.Critical]:"#f5222d",[ye.High]:"#fa541c",[ye.Medium]:"#faad14",[ye.Low]:"#1890ff",[ye.Info]:"#52c41a"},S=y!=null&&y.vulnerability_by_severity?Object.entries(y.vulnerability_by_severity).map(([E,F])=>({name:E.toUpperCase(),value:F,color:h[E]})):[],b=Object.values(s).flat().sort((E,F)=>Fe(F.timestamp).valueOf()-Fe(E.timestamp).valueOf()).slice(0,5),R=[{title:"严重性",dataIndex:"severity",key:"severity",width:100,render:E=>l.jsx(Ae,{color:E===ye.Critical?"red":E===ye.High?"orange":E===ye.Medium?"gold":"blue",children:E.toUpperCase()})},{title:"漏洞标题",dataIndex:"title",key:"title",ellipsis:!0},{title:"漏洞ID",dataIndex:"vulnerability_id",key:"vulnerability_id",width:150,render:E=>l.jsx(K,{code:!0,style:{fontSize:12},children:E})},{title:"CVE ID",dataIndex:"cve_id",key:"cve_id",width:120,render:E=>E?l.jsx(Ae,{color:"purple",children:E}):l.jsx(K,{type:"secondary",children:"-"})},{title:"CVSS",dataIndex:"cvss_score",key:"cvss_score",width:80,render:E=>E?l.jsx(K,{strong:!0,children:E.toFixed(1)}):l.jsx(K,{type:"secondary",children:"-"})},{title:"发现时间",dataIndex:"created_at",key:"created_at",width:120,render:E=>l.jsx(K,{type:"secondary",children:Fe(E).format("MM-DD HH:mm")})}],_=()=>{u(),c(),window.location.reload()};return l.jsxs("div",{style:{padding:24},children:[l.jsxs("div",{style:{marginBottom:24,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[l.jsxs("div",{children:[l.jsx(Du,{level:2,style:{margin:0},children:"仪表板"}),l.jsx(K,{type:"secondary",children:"实时监控扫描任务状态和安全发现"})]}),l.jsxs(pr,{children:[l.jsx("div",{style:{display:"flex",alignItems:"center"},children:r?l.jsx(Ae,{color:"success",icon:l.jsx(bn,{}),children:"实时连接"}):l.jsx(Ae,{color:"error",icon:l.jsx(xn,{}),children:"连接断开"})}),l.jsx(De,{type:"primary",icon:l.jsx(mr,{}),onClick:_,children:"刷新全部"})]})]}),l.jsxs($t,{gutter:[16,16],style:{marginBottom:24},children:[l.jsx(ce,{xs:24,sm:12,md:6,children:l.jsxs(le,{children:[l.jsx(Lt,{title:"总任务数",value:m.total,prefix:l.jsx(Ye,{}),valueStyle:{color:"#1890ff"}}),l.jsx("div",{style:{marginTop:8},children:l.jsxs(K,{type:"secondary",style:{fontSize:12},children:["运行中: ",m.running," | 已完成: ",m.completed]})})]})}),l.jsx(ce,{xs:24,sm:12,md:6,children:l.jsxs(le,{children:[l.jsx(Lt,{title:"发现主机",value:(y==null?void 0:y.total_hosts)||0,prefix:l.jsx(Sn,{}),valueStyle:{color:"#52c41a"},loading:a}),l.jsx("div",{style:{marginTop:8},children:l.jsxs(K,{type:"secondary",style:{fontSize:12},children:["开放端口: ",(y==null?void 0:y.total_ports)||0]})})]})}),l.jsx(ce,{xs:24,sm:12,md:6,children:l.jsxs(le,{children:[l.jsx(Lt,{title:"Web资产",value:(y==null?void 0:y.total_web_assets)||0,prefix:l.jsx($i,{}),valueStyle:{color:"#722ed1"},loading:a}),l.jsx("div",{style:{marginTop:8},children:l.jsxs(K,{type:"secondary",style:{fontSize:12},children:["爬取页面: ",(y==null?void 0:y.total_pages)||0]})})]})}),l.jsx(ce,{xs:24,sm:12,md:6,children:l.jsxs(le,{children:[l.jsx(Lt,{title:"发现漏洞",value:(y==null?void 0:y.total_vulnerabilities)||0,prefix:l.jsx(ms,{}),valueStyle:{color:"#f5222d"},loading:a}),l.jsx("div",{style:{marginTop:8},children:l.jsxs(K,{type:"secondary",style:{fontSize:12},children:["高危: ",((L=y==null?void 0:y.vulnerability_by_severity)==null?void 0:L.high)||0]})})]})})]}),l.jsxs($t,{gutter:[16,16],style:{marginBottom:24},children:[l.jsx(ce,{xs:24,lg:16,children:l.jsx(le,{title:l.jsxs("span",{children:[l.jsx(Bi,{style:{marginRight:8}}),"24小时扫描活动"]}),extra:l.jsx(K,{type:"secondary",children:"最近24小时"}),children:l.jsx(xs,{width:"100%",height:300,children:l.jsxs(lo,{data:n,children:[l.jsx(co,{strokeDasharray:"3 3"}),l.jsx(uo,{dataKey:"hour"}),l.jsx(fo,{}),l.jsx(Ss,{}),l.jsx(ho,{}),l.jsx(yr,{type:"monotone",dataKey:"scans",stroke:"#1890ff",name:"扫描任务",strokeWidth:2}),l.jsx(yr,{type:"monotone",dataKey:"completed",stroke:"#52c41a",name:"已完成",strokeWidth:2}),l.jsx(yr,{type:"monotone",dataKey:"failed",stroke:"#f5222d",name:"失败",strokeWidth:2})]})})})}),l.jsx(ce,{xs:24,lg:8,children:l.jsx(le,{title:"活动任务",extra:l.jsx(De,{type:"text",icon:l.jsx(mr,{}),size:"small",onClick:()=>window.location.reload(),children:"刷新"}),children:t.length===0?l.jsxs("div",{style:{textAlign:"center",padding:"40px 0",color:"#999"},children:[l.jsx(Ye,{style:{fontSize:48,marginBottom:16}}),l.jsx("div",{children:"当前没有运行中的任务"})]}):l.jsx(pr,{direction:"vertical",style:{width:"100%"},size:"middle",children:t.slice(0,5).map(E=>l.jsxs("div",{children:[l.jsxs("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:4},children:[l.jsx(K,{ellipsis:!0,style:{maxWidth:200},children:E.target}),l.jsxs(K,{children:[E.progress,"%"]})]}),l.jsx(zi,{percent:E.progress,size:"small",status:E.status===Q.Failed?"exception":"active"})]},E.id))})})})]}),l.jsxs($t,{gutter:[16,16],style:{marginBottom:24},children:[l.jsx(ce,{xs:24,lg:12,children:l.jsx(le,{title:l.jsxs("span",{children:[l.jsx(qi,{style:{marginRight:8}}),"漏洞严重性分布"]}),children:S.length>0?l.jsx(xs,{width:"100%",height:300,children:l.jsxs(po,{children:[l.jsx(mo,{data:S,cx:"50%",cy:"50%",labelLine:!1,label:({name:E,percent:F})=>`${E} ${(F*100).toFixed(0)}%`,outerRadius:80,fill:"#8884d8",dataKey:"value",children:S.map((E,F)=>l.jsx(yo,{fill:E.color},`cell-${F}`))}),l.jsx(Ss,{})]})}):l.jsx("div",{style:{textAlign:"center",padding:"60px 0",color:"#999"},children:"暂无漏洞数据"})})}),l.jsx(ce,{xs:24,lg:12,children:l.jsx(le,{title:l.jsxs("span",{children:[l.jsx(gs,{style:{marginRight:8}}),"实时活动"]}),extra:l.jsx("div",{style:{display:"flex",alignItems:"center"},children:r?l.jsx(Ae,{color:"success",children:"实时"}):l.jsx(Ae,{color:"default",children:"离线"})}),children:b.length>0?l.jsx(ys,{style:{marginTop:16,maxHeight:240,overflow:"auto"},children:b.map((E,F)=>l.jsxs(ys.Item,{color:E.level==="error"?"red":E.level==="warn"?"orange":"blue",children:[l.jsxs("div",{style:{fontSize:12},children:[l.jsx(Ae,{color:E.level==="error"?"red":E.level==="warn"?"orange":"blue",children:E.level.toUpperCase()}),l.jsx(K,{type:"secondary",children:Fe(E.timestamp).format("HH:mm:ss")})]}),l.jsx("div",{style:{fontSize:13,marginTop:4},children:E.message.length>100?`${E.message.substring(0,100)}...`:E.message})]},F))}):l.jsxs("div",{style:{textAlign:"center",padding:"60px 0",color:"#999"},children:[l.jsx(gs,{style:{fontSize:32,marginBottom:8}}),l.jsx("div",{children:"暂无活动日志"})]})})})]}),l.jsx(le,{title:"最近发现的漏洞",extra:l.jsxs(pr,{children:[l.jsx(De,{type:"text",icon:l.jsx(Wi,{}),size:"small",children:"导出"}),l.jsx(De,{type:"text",icon:l.jsx(mr,{}),size:"small",onClick:()=>c(),children:"刷新"})]}),children:l.jsx(Hi,{columns:R,dataSource:x,rowKey:"id",size:"small",pagination:!1,scroll:{x:800},locale:{emptyText:l.jsxs("div",{style:{padding:"40px 0",color:"#999"},children:[l.jsx(ms,{style:{fontSize:48,marginBottom:16}}),l.jsx("div",{children:"暂无漏洞发现"})]})}})})]})},Nu=({connected:e,showText:t=!0})=>{const r=e?"success":"error",s=e?l.jsx(bn,{}):l.jsx(xn,{}),n=e?"已连接":"连接断开",i=e?"实时连接正常，将接收任务状态更新":"实时连接断开，无法接收任务状态更新";return l.jsx(Qi,{title:i,children:l.jsxs("div",{style:{display:"flex",alignItems:"center",gap:6,padding:"4px 8px",borderRadius:4,background:e?"rgba(82, 196, 26, 0.1)":"rgba(255, 77, 79, 0.1)",border:`1px solid ${e?"#52c41a":"#ff4d4f"}`,fontSize:12,color:e?"#52c41a":"#ff4d4f"},children:[l.jsx(Or,{status:r}),s,t&&l.jsx("span",{children:n})]})})},{Header:Uu,Sider:Mu,Content:$u}=Pr,{Text:jr}=Zr,Bu=({children:e})=>{const t=ts(),r=Pt(),[s,n]=w.useState(!1),[i,o]=w.useState([]),{activeTasks:a}=ds(),{connected:u,logs:d}=fs(),{user:c,logout:f}=us();re.useEffect(()=>{console.log("Location changed to:",r.pathname),r.pathname.startsWith("/tasks")?o(["tasks-menu"]):o([])},[r.pathname]);const y=()=>{const b=r.pathname;return b==="/tasks"||b==="/tasks/create"?[b]:[b]},x=[{key:"/dashboard",icon:l.jsx(Ki,{}),label:"仪表板"},{key:"tasks-menu",icon:l.jsx(Ye,{}),label:"扫描任务",children:[{key:"/tasks",label:"任务列表"},{key:"/tasks/create",label:"创建任务"}]},{key:"/results",icon:l.jsx(Sn,{}),label:"扫描结果"},...an((c==null?void 0:c.role)||ue.Viewer)?[{key:"/users",icon:l.jsx(Ji,{}),label:"用户管理"}]:[],...an((c==null?void 0:c.role)||ue.Viewer)?[{key:"/system",icon:l.jsx(Vi,{}),label:"系统管理"}]:[],{key:"/settings",icon:l.jsx(Yi,{}),label:"系统设置"}],v=[{key:"profile",icon:l.jsx(_r,{}),label:"个人资料"},{key:"about",icon:l.jsx(io,{}),label:"关于"},{type:"divider"},{key:"logout",icon:l.jsx(oo,{}),label:"退出登录",danger:!0}],m=({key:b})=>{if(console.log("Menu clicked:",b),b!=="tasks-menu"){console.log("Navigating to:",b);try{t(b)}catch(R){console.error("Navigation error:",R)}}},h=({key:b})=>{switch(b){case"profile":t("/profile");break;case"about":vs.info({title:"关于 RustScan Web",content:l.jsxs("div",{children:[l.jsx("p",{children:"版本: 2.4.1"}),l.jsx("p",{children:"RustScan Web 是一个强大的网络安全扫描平台"}),l.jsx("p",{children:"集成了多种安全工具，提供实时扫描监控"})]})});break;case"logout":vs.confirm({title:"确认退出",content:"您确定要退出登录吗？",onOk:()=>{f(),Bt.success("已退出登录"),t("/login")}});break}},S=d.filter(b=>b.level==="ERROR"||b.level==="WARN").length;return l.jsxs(Pr,{style:{minHeight:"100vh"},children:[l.jsxs(Mu,{trigger:null,collapsible:!0,collapsed:s,width:240,style:{overflow:"auto",height:"100vh",position:"fixed",left:0,top:0,bottom:0},children:[l.jsx("div",{style:{height:64,display:"flex",alignItems:"center",justifyContent:s?"center":"flex-start",padding:s?0:"0 24px",borderBottom:"1px solid #f0f0f0"},children:s?l.jsx(Ye,{style:{fontSize:24,color:"#1890ff"}}):l.jsxs("div",{style:{display:"flex",alignItems:"center",gap:8},children:[l.jsx(Ye,{style:{fontSize:24,color:"#1890ff"}}),l.jsx(jr,{strong:!0,style:{color:"#1890ff",fontSize:18},children:"RustScan Web"})]})}),l.jsx(Gi,{theme:"dark",mode:"inline",selectedKeys:y(),openKeys:i,onOpenChange:o,onClick:m,items:x,style:{borderRight:0},inlineCollapsed:s}),!s&&l.jsxs("div",{style:{position:"absolute",bottom:16,left:16,right:16},children:[l.jsx(Nu,{connected:u}),a.length>0&&l.jsxs("div",{style:{marginTop:8,color:"#1890ff",fontSize:12},children:[a.length," 个任务运行中"]})]})]}),l.jsxs(Pr,{style:{marginLeft:s?80:240,transition:"all 0.2s"},children:[l.jsxs(Uu,{style:{padding:"0 24px",background:"#fff",borderBottom:"1px solid #f0f0f0",display:"flex",alignItems:"center",justifyContent:"space-between",position:"fixed",top:0,right:0,left:s?80:240,zIndex:1e3,transition:"all 0.2s"},children:[l.jsxs("div",{style:{display:"flex",alignItems:"center",gap:16},children:[l.jsx(De,{type:"text",icon:s?l.jsx(Xi,{}):l.jsx(Zi,{}),onClick:()=>n(!s),style:{fontSize:16,width:32,height:32}}),a.length>0&&l.jsx(Or,{count:a.length,color:"#52c41a",children:l.jsx(Ye,{style:{fontSize:16,color:"#52c41a"}})})]}),l.jsxs("div",{style:{display:"flex",alignItems:"center",gap:16},children:[l.jsx(Or,{count:S,size:"small",children:l.jsx(De,{type:"text",icon:l.jsx(eo,{}),style:{fontSize:16,width:32,height:32},onClick:()=>{}})}),l.jsx(to,{menu:{items:v,onClick:h},placement:"bottomRight",children:l.jsxs("div",{style:{display:"flex",alignItems:"center",gap:8,cursor:"pointer",padding:"4px 8px",borderRadius:6,transition:"background 0.2s"},onMouseEnter:b=>{b.currentTarget.style.background="#f5f5f5"},onMouseLeave:b=>{b.currentTarget.style.background="transparent"},children:[l.jsx(ro,{size:"small",style:{backgroundColor:(c==null?void 0:c.role)===ue.Admin?"#f56a00":(c==null?void 0:c.role)===ue.User?"#1890ff":"#52c41a"},children:(c==null?void 0:c.role)===ue.Admin?l.jsx(so,{}):(c==null?void 0:c.role)===ue.User?l.jsx(_r,{}):l.jsx(no,{})}),l.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"flex-start"},children:[l.jsx(jr,{style:{fontSize:"14px",lineHeight:1.2},children:c==null?void 0:c.username}),l.jsx(jr,{type:"secondary",style:{fontSize:"12px",lineHeight:1.2},children:(c==null?void 0:c.role)===ue.Admin?"管理员":(c==null?void 0:c.role)===ue.User?"用户":"观察者"})]})]})})]})]}),l.jsx($u,{style:{margin:"88px 24px 24px",minHeight:"calc(100vh - 112px)",background:"#f5f5f5"},children:e})]})]})},zu=(e=!0)=>{const t=w.useRef(null),r=w.useRef(null),s=w.useRef(null),n=w.useRef(!1),[i,o]=w.useState(!1),[a,u]=w.useState(0),d=5,c=3e3,{updateTask:f}=ds(),{setConnected:y,addLog:x}=fs(),v=w.useCallback(()=>{if(e){if(n.current){console.log("🔄 WebSocket connection already in progress, skipping...");return}if(t.current&&(t.current.readyState===WebSocket.OPEN||t.current.readyState===WebSocket.CONNECTING)){console.log("✅ WebSocket already connected or connecting, skipping...");return}t.current&&(console.log("🧹 Cleaning up old WebSocket connection"),t.current.close(),t.current=null),n.current=!0;try{const _=new WebSocket("ws://localhost:8080/ws");t.current=_,_.onopen=()=>{console.log("✅ WebSocket connected"),n.current=!1,o(!0),y(!0),u(0),s.current&&clearTimeout(s.current),s.current=setTimeout(()=>{var j;((j=t.current)==null?void 0:j.readyState)===WebSocket.OPEN&&R()},6e4)},_.onclose=j=>{if(j.code!==1e3&&console.warn("🔌 WebSocket disconnected:",j.code,j.reason||"Unknown reason"),o(!1),y(!1),t.current=null,r.current&&(clearTimeout(r.current),r.current=null),e&&j.code!==1e3&&a<d){const P=c+a*1e3;console.log(`🔄 WebSocket reconnecting in ${P}ms (${a+1}/${d})`),r.current=setTimeout(()=>{u(U=>U+1),v()},P)}else a>=d&&console.error("❌ WebSocket max reconnection attempts reached")},_.onerror=j=>{console.error("WebSocket error:",j),n.current=!1,o(!1),y(!1)},_.onmessage=j=>{try{const P=JSON.parse(j.data);switch(P.message_type!=="notification"&&console.log("📨 WebSocket message:",P.message_type,P.data),s.current&&(clearTimeout(s.current),s.current=setTimeout(()=>{var U;((U=t.current)==null?void 0:U.readyState)===WebSocket.OPEN&&R()},6e4)),P.message_type){case"task_update":P.task_id&&f(P.task_id,{status:P.data.status,progress:P.data.progress,error:void 0});break;case"log":P.task_id&&x({task_id:P.task_id,level:P.data.level,message:P.data.message,timestamp:P.timestamp});break;case"scan_result":console.log("Scan result received:",P.data);break;case"notification":P.data.message!=="WebSocket connected successfully"&&console.log("🔔 System notification:",P.data);break;default:console.log("Unknown message type:",P.message_type)}}catch(P){console.error("Failed to parse WebSocket message:",P)}}}catch(_){console.error("Failed to create WebSocket connection:",_),n.current=!1,o(!1),y(!1)}}},[e,a,f,y,x]),m=w.useCallback(()=>{r.current&&(clearTimeout(r.current),r.current=null),s.current&&(clearTimeout(s.current),s.current=null),t.current&&(t.current.close(1e3,"Client disconnect"),t.current=null),o(!1),y(!1),u(0)},[y]),h=w.useCallback(_=>{var j;return((j=t.current)==null?void 0:j.readyState)===WebSocket.OPEN?(t.current.send(JSON.stringify(_)),!0):(console.warn("WebSocket is not connected"),!1)},[]),S=w.useCallback(_=>h({message_type:"subscribe",task_id:_}),[h]),b=w.useCallback(_=>h({message_type:"unsubscribe",task_id:_}),[h]),R=w.useCallback(()=>h({message_type:"ping"}),[h]);return w.useEffect(()=>(console.log("🔧 WebSocket useEffect triggered, enabled:",e),e?v():m(),()=>{console.log("🧹 WebSocket useEffect cleanup triggered"),m()}),[e,v,m]),w.useEffect(()=>{if(!i||!e)return;const _=setInterval(()=>{var j;((j=t.current)==null?void 0:j.readyState)===WebSocket.OPEN&&R()},3e4);return()=>clearInterval(_)},[i,e,R]),{connected:i,sendMessage:h,subscribeToTask:S,unsubscribeFromTask:b,ping:R}},qu=re.lazy(()=>ke(()=>import("./TaskList-Bttm5b5J.js"),__vite__mapDeps([0,1,2,3,4]))),Hu=re.lazy(()=>ke(()=>import("./CreateTask-DV6R3ssa.js"),__vite__mapDeps([5,1,2,3,4]))),Wu=re.lazy(()=>ke(()=>import("./TaskDetail-Bfmfpbt2.js"),__vite__mapDeps([6,1,3,4]))),Qu=re.lazy(()=>ke(()=>import("./index-Byjsv-ns.js"),__vite__mapDeps([7,1,3,4]))),Ku=re.lazy(()=>ke(()=>import("./index-huypI1iw.js"),__vite__mapDeps([8,3,1,4]))),Ju=re.lazy(()=>ke(()=>import("./index-CWFnNnBI.js"),__vite__mapDeps([9,1,3,4]))),Vu=re.lazy(()=>ke(()=>import("./index-C1hbtBsp.js"),__vite__mapDeps([10,1,3,4]))),Yu=re.lazy(()=>ke(()=>import("./NotFound-CqlA3poe.js"),__vite__mapDeps([11,3,1,4]))),Gu=()=>{const{isAuthenticated:e,checkAuth:t}=us(),[r,s]=w.useState(!0);return zu(e),w.useEffect(()=>{(async()=>{await t(),s(!1)})()},[t]),r?l.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh"},children:l.jsx(bs,{size:"large"})}):l.jsxs(Os,{children:[l.jsx(J,{path:"/login",element:l.jsx(Iu,{})}),l.jsx(J,{path:"/*",element:e?l.jsx(Bu,{children:l.jsx(re.Suspense,{fallback:l.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"50vh"},children:l.jsx(bs,{size:"large"})}),children:l.jsxs(Os,{children:[l.jsx(J,{path:"/",element:l.jsx(vr,{to:"/dashboard",replace:!0})}),l.jsx(J,{path:"/dashboard",element:l.jsx(Lu,{})}),l.jsx(J,{path:"/tasks",element:l.jsx(qu,{})}),l.jsx(J,{path:"/tasks/create",element:l.jsx(Hu,{})}),l.jsx(J,{path:"/tasks/:taskId",element:l.jsx(Wu,{})}),l.jsx(J,{path:"/results",element:l.jsx(Qu,{})}),l.jsx(J,{path:"/settings",element:l.jsx(Ku,{})}),l.jsx(J,{path:"/users",element:l.jsx(Ju,{})}),l.jsx(J,{path:"/system",element:l.jsx(Vu,{})}),l.jsx(J,{path:"/404",element:l.jsx(Yu,{})}),l.jsx(J,{path:"*",element:l.jsx(vr,{to:"/dashboard",replace:!0})})]})})}):l.jsx(vr,{to:"/login",replace:!0})})]})};Fe.locale("zh-cn");const Xu=new Aa({defaultOptions:{queries:{retry:1,staleTime:5*60*1e3,refetchOnWindowFocus:!1}}}),Zu=({children:e})=>{const{theme:t}=Fu(),r=()=>t==="auto"?window.matchMedia("(prefers-color-scheme: dark)").matches?Nt.darkAlgorithm:Nt.defaultAlgorithm:t==="dark"?Nt.darkAlgorithm:Nt.defaultAlgorithm;return l.jsx(ao,{locale:pl,theme:{algorithm:r(),token:{colorPrimary:"#1890ff",borderRadius:6,wireframe:!1},components:{Layout:{siderBg:"#001529",triggerBg:t==="dark"?"#1f1f1f":"#002140"},Menu:{darkItemBg:"#001529",darkSubMenuItemBg:"#000c17",darkItemSelectedBg:"#1890ff"}}},children:l.jsx(wn,{children:e})})};Tr.createRoot(document.getElementById("root")).render(l.jsx(re.StrictMode,{children:l.jsx(Na,{client:Xu,children:l.jsx(pa,{future:{v7_startTransition:!0,v7_relativeSplatPath:!0},children:l.jsx(Zu,{children:l.jsx(wn,{children:l.jsx(Gu,{})})})})})}));export{Xl as S,Q as T,tc as U,La as a,ds as b,xr as c,ec as d,Zl as e,Tt as f,Pa as g,bt as h,Y as i,l as j,ba as k,nd as l,zu as m,B as n,fs as o,k as p,us as q,nn as r,Fr as s,Au as t,ts as u,ue as v,on as w,Rd as x};
//# sourceMappingURL=index-z9zCfJ11.js.map
