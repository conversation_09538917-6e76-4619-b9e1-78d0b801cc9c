import{c as S,j as e,r as w}from"./index-z9zCfJ11.js";import{r as p,R as H}from"./vendor-o6zXO7vr.js";import{f as g,T as ye,S as O,B as U,ad as pe,b as o,R as k,C as i,I as ge,a0 as me,a1 as F,ag as fe,ah as ve,ab as Se,ai as we,v as C,m as W,n as E,aa as ke,o as Q,g as m,aj as Ie,k as R,ae as _e,P as be,a7 as T,ak as Ce,al as Re,y as Te}from"./ui-Smnqh0BA.js";import{R as V,B as $e,C as J,X,Y as G,T as N,e as De,L as Ke,a as Pe,b as $}from"./charts-CfM58meh.js";const{Title:Z,Text:r}=ye,{RangePicker:ze}=fe,{Option:j}=F,Ae=()=>{var q,A,B,Y;const[ee,te]=p.useState("overview"),[d,se]=p.useState(""),[I,re]=p.useState(),[h,ae]=p.useState(),[x,ne]=p.useState(null),[_,ie]=p.useState(10),{data:D,isLoading:b}=S({queryKey:["results-statistics"],queryFn:()=>w.getStatistics(),refetchInterval:3e4}),{data:K,isLoading:le}=S({queryKey:["hosts",d,h,x],queryFn:()=>{var a,n;const t=new URLSearchParams;return d&&t.append("search",d),h&&t.append("status",h),x&&(t.append("start_date",((a=x[0])==null?void 0:a.toISOString())||""),t.append("end_date",((n=x[1])==null?void 0:n.toISOString())||"")),w.getHosts(t.toString())}}),{data:P}=S({queryKey:["ports",d,h],queryFn:()=>{const t=new URLSearchParams;return d&&t.append("search",d),h&&t.append("state",h),w.getPorts(t.toString())}}),{data:z}=S({queryKey:["vulnerabilities",d,I,x],queryFn:()=>{var a,n;const t=new URLSearchParams;return d&&t.append("search",d),I&&t.append("severity",I),x&&(t.append("start_date",((a=x[0])==null?void 0:a.toISOString())||""),t.append("end_date",((n=x[1])==null?void 0:n.toISOString())||"")),w.getVulnerabilities(t.toString())}}),{data:L}=S({queryKey:["web-assets",d,h],queryFn:()=>{const t=new URLSearchParams;return d&&t.append("search",d),h&&t.append("status",h),w.getWebAssets(t.toString())}}),c=D==null?void 0:D.data,f=((q=K==null?void 0:K.data)==null?void 0:q.items)||[],y=((A=P==null?void 0:P.data)==null?void 0:A.items)||[],l=((B=z==null?void 0:z.data)==null?void 0:B.items)||[],M=((Y=L==null?void 0:L.data)==null?void 0:Y.items)||[],de=H.useMemo(()=>{const t={};return y.forEach(a=>{const n=`${a.port}/${a.protocol}`;t[n]=(t[n]||0)+1}),Object.entries(t).sort(([,a],[,n])=>n-a).slice(0,20).map(([a,n])=>({port:a,count:n}))},[y]),oe=H.useMemo(()=>Array.from({length:30},(a,n)=>{const v=g().subtract(n,"day"),s=l.filter(u=>g(u.created_at).isSame(v,"day"));return{date:v.format("MM-DD"),total:s.length,critical:s.filter(u=>u.severity==="critical").length,high:s.filter(u=>u.severity==="high").length,medium:s.filter(u=>u.severity==="medium").length,low:s.filter(u=>u.severity==="low").length}}).reverse(),[l]),ce={critical:"#f5222d",high:"#fa541c",medium:"#faad14",low:"#1890ff",info:"#52c41a"},he=[{title:"IP地址",dataIndex:"ip_address",key:"ip_address",render:t=>e.jsx(r,{code:!0,children:t})},{title:"主机名",dataIndex:"hostname",key:"hostname",render:t=>t||e.jsx(r,{type:"secondary",children:"-"})},{title:"状态",dataIndex:"status",key:"status",render:t=>e.jsx(m,{color:t==="up"?"green":"red",children:t.toUpperCase()})},{title:"操作系统",dataIndex:"os_name",key:"os_name",render:t=>t||e.jsx(r,{type:"secondary",children:"未知"})},{title:"开放端口",dataIndex:"port_count",key:"port_count",render:t=>e.jsx(Te,{count:t,style:{backgroundColor:"#52c41a"}})},{title:"发现时间",dataIndex:"created_at",key:"created_at",render:t=>g(t).format("YYYY-MM-DD HH:mm")}],xe=[{title:"IP地址",dataIndex:"host_ip",key:"host_ip",render:t=>e.jsx(r,{code:!0,children:t})},{title:"端口",dataIndex:"port",key:"port",render:(t,a)=>e.jsxs(r,{strong:!0,children:[t,"/",a.protocol]})},{title:"状态",dataIndex:"state",key:"state",render:t=>e.jsx(m,{color:t==="open"?"green":t==="closed"?"red":"orange",children:t.toUpperCase()})},{title:"服务",dataIndex:"service",key:"service",render:t=>t||e.jsx(r,{type:"secondary",children:"未知"})},{title:"版本",dataIndex:"version",key:"version",render:t=>t||e.jsx(r,{type:"secondary",children:"-"})}],ue=[{title:"严重性",dataIndex:"severity",key:"severity",render:t=>e.jsx(m,{color:ce[t],children:t.toUpperCase()})},{title:"漏洞名称",dataIndex:"name",key:"name",ellipsis:!0,render:t=>e.jsx(r,{strong:!0,children:t})},{title:"CVE ID",dataIndex:"cve_id",key:"cve_id",render:t=>t?e.jsx(m,{color:"purple",children:t}):e.jsx(r,{type:"secondary",children:"-"})},{title:"CVSS评分",dataIndex:"cvss_score",key:"cvss_score",render:t=>t?e.jsx(r,{strong:!0,style:{color:t>=7?"#f5222d":t>=4?"#fa541c":"#52c41a"},children:t.toFixed(1)}):e.jsx(r,{type:"secondary",children:"-"})},{title:"影响主机",dataIndex:"host_ip",key:"host_ip",render:t=>e.jsx(r,{code:!0,children:t})},{title:"发现时间",dataIndex:"created_at",key:"created_at",render:t=>g(t).format("MM-DD HH:mm")}],je=()=>{var t,a,n,v;return e.jsxs("div",{children:[e.jsxs(k,{gutter:[16,16],style:{marginBottom:24},children:[e.jsx(i,{xs:24,sm:12,md:6,children:e.jsxs(o,{children:[e.jsx(R,{title:"发现主机",value:((t=c==null?void 0:c.data)==null?void 0:t.total_hosts)||0,prefix:e.jsx(W,{}),valueStyle:{color:"#1890ff"},loading:b}),e.jsx("div",{style:{marginTop:8},children:e.jsxs(r,{type:"secondary",style:{fontSize:12},children:["在线: ",f.filter(s=>s.status==="up").length]})})]})}),e.jsx(i,{xs:24,sm:12,md:6,children:e.jsxs(o,{children:[e.jsx(R,{title:"开放端口",value:((a=c==null?void 0:c.data)==null?void 0:a.total_ports)||0,prefix:e.jsx(E,{}),valueStyle:{color:"#52c41a"},loading:b}),e.jsx("div",{style:{marginTop:8},children:e.jsxs(r,{type:"secondary",style:{fontSize:12},children:["服务: ",y.filter(s=>s.service).length]})})]})}),e.jsx(i,{xs:24,sm:12,md:6,children:e.jsxs(o,{children:[e.jsx(R,{title:"Web资产",value:((n=c==null?void 0:c.data)==null?void 0:n.total_web_assets)||0,prefix:e.jsx(_e,{}),valueStyle:{color:"#722ed1"},loading:b}),e.jsx("div",{style:{marginTop:8},children:e.jsxs(r,{type:"secondary",style:{fontSize:12},children:["活跃: ",M.filter(s=>s.status_code===200).length]})})]})}),e.jsx(i,{xs:24,sm:12,md:6,children:e.jsxs(o,{children:[e.jsx(R,{title:"发现漏洞",value:((v=c==null?void 0:c.data)==null?void 0:v.total_vulnerabilities)||0,prefix:e.jsx(Q,{}),valueStyle:{color:"#f5222d"},loading:b}),e.jsx("div",{style:{marginTop:8},children:e.jsxs(r,{type:"secondary",style:{fontSize:12},children:["高危: ",l.filter(s=>["critical","high"].includes(s.severity)).length]})})]})})]}),e.jsxs(k,{gutter:[16,16],style:{marginBottom:24},children:[e.jsx(i,{xs:24,lg:12,children:e.jsx(o,{title:"常见端口分布",extra:e.jsx(r,{type:"secondary",children:"TOP 20"}),children:e.jsx(V,{width:"100%",height:300,children:e.jsxs($e,{data:de,margin:{top:20,right:30,left:20,bottom:5},children:[e.jsx(J,{strokeDasharray:"3 3"}),e.jsx(X,{dataKey:"port",angle:-45,textAnchor:"end",height:80}),e.jsx(G,{}),e.jsx(N,{}),e.jsx(De,{dataKey:"count",fill:"#1890ff"})]})})})}),e.jsx(i,{xs:24,lg:12,children:e.jsx(o,{title:"30天漏洞发现趋势",extra:e.jsx(r,{type:"secondary",children:"按严重性分类"}),children:e.jsx(V,{width:"100%",height:300,children:e.jsxs(Ke,{data:oe,children:[e.jsx(J,{strokeDasharray:"3 3"}),e.jsx(X,{dataKey:"date"}),e.jsx(G,{}),e.jsx(N,{}),e.jsx(Pe,{}),e.jsx($,{type:"monotone",dataKey:"critical",stroke:"#f5222d",strokeWidth:2,name:"严重"}),e.jsx($,{type:"monotone",dataKey:"high",stroke:"#fa541c",strokeWidth:2,name:"高危"}),e.jsx($,{type:"monotone",dataKey:"medium",stroke:"#faad14",strokeWidth:2,name:"中危"}),e.jsx($,{type:"monotone",dataKey:"low",stroke:"#1890ff",strokeWidth:2,name:"低危"})]})})})})]}),e.jsxs(k,{gutter:[16,16],children:[e.jsx(i,{xs:24,lg:8,children:e.jsx(o,{title:"整体风险评估",variant:"outlined",children:e.jsxs("div",{style:{textAlign:"center",padding:"20px 0"},children:[e.jsx(be,{type:"circle",percent:l.filter(s=>["critical","high"].includes(s.severity)).length>0?85:l.filter(s=>s.severity==="medium").length>5?65:35,status:l.filter(s=>["critical","high"].includes(s.severity)).length>0?"exception":"normal",size:120,strokeColor:{"0%":"#108ee9","100%":l.filter(s=>["critical","high"].includes(s.severity)).length>0?"#f5222d":"#87d068"}}),e.jsx("div",{style:{marginTop:16},children:e.jsx(r,{strong:!0,children:l.filter(s=>["critical","high"].includes(s.severity)).length>0?"高风险":l.filter(s=>s.severity==="medium").length>5?"中等风险":"低风险"})})]})})}),e.jsx(i,{xs:24,lg:16,children:e.jsx(o,{title:"安全建议",variant:"outlined",children:e.jsxs(O,{direction:"vertical",style:{width:"100%"},children:[l.filter(s=>s.severity==="critical").length>0&&e.jsx(T,{message:"发现严重漏洞",description:`检测到 ${l.filter(s=>s.severity==="critical").length} 个严重漏洞，建议立即修复`,type:"error",showIcon:!0,icon:e.jsx(Ce,{})}),y.filter(s=>["21","23","135","139","445"].includes(s.port.toString())).length>0&&e.jsx(T,{message:"发现敏感端口",description:"检测到高风险端口开放，建议审查是否需要关闭",type:"warning",showIcon:!0}),f.filter(s=>!s.os_name).length>0&&e.jsx(T,{message:"主机指纹不完整",description:"部分主机操作系统信息缺失，建议进行深度扫描",type:"info",showIcon:!0}),e.jsx(T,{message:"扫描完成",description:`已完成对 ${f.length} 台主机的安全扫描，发现 ${y.length} 个端口和 ${l.length} 个安全问题`,type:"success",showIcon:!0,icon:e.jsx(Re,{})})]})})})]})]})};return e.jsxs("div",{style:{padding:24},children:[e.jsxs("div",{style:{marginBottom:24},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:16},children:[e.jsx(Z,{level:2,style:{margin:0},children:"扫描结果"}),e.jsx(O,{children:e.jsx(U,{type:"primary",icon:e.jsx(pe,{}),children:"导出报告"})})]}),e.jsx(o,{size:"small",children:e.jsxs(k,{gutter:16,children:[e.jsx(i,{xs:24,sm:8,md:6,children:e.jsx(ge,{placeholder:"搜索IP、主机名、漏洞等",prefix:e.jsx(me,{}),value:d,onChange:t=>se(t.target.value),allowClear:!0})}),e.jsx(i,{xs:24,sm:8,md:4,children:e.jsxs(F,{placeholder:"严重性",value:I,onChange:re,allowClear:!0,style:{width:"100%"},children:[e.jsx(j,{value:"critical",children:"严重"}),e.jsx(j,{value:"high",children:"高危"}),e.jsx(j,{value:"medium",children:"中危"}),e.jsx(j,{value:"low",children:"低危"})]})}),e.jsx(i,{xs:24,sm:8,md:4,children:e.jsxs(F,{placeholder:"状态",value:h,onChange:ae,allowClear:!0,style:{width:"100%"},children:[e.jsx(j,{value:"up",children:"在线"}),e.jsx(j,{value:"down",children:"离线"}),e.jsx(j,{value:"open",children:"开放"}),e.jsx(j,{value:"closed",children:"关闭"})]})}),e.jsx(i,{xs:24,sm:12,md:6,children:e.jsx(ze,{value:x,onChange:ne,style:{width:"100%"},placeholder:["开始日期","结束日期"]})}),e.jsx(i,{xs:24,sm:12,md:4,children:e.jsx(U,{icon:e.jsx(ve,{}),block:!0,children:"高级筛选"})})]})})]}),e.jsx(Se,{activeKey:ee,onChange:te,items:[{key:"overview",label:e.jsxs("span",{children:[e.jsx(we,{}),"概览"]}),children:je()},{key:"hosts",label:e.jsxs("span",{children:[e.jsx(W,{}),"主机 (",f.length,")"]}),children:e.jsx(o,{children:e.jsx(C,{columns:he,dataSource:f,rowKey:"id",loading:le,pagination:{pageSize:_,showSizeChanger:!0,showQuickJumper:!0,showTotal:t=>`共 ${t} 条记录`,onShowSizeChange:(t,a)=>ie(a)},scroll:{x:800}})})},{key:"ports",label:e.jsxs("span",{children:[e.jsx(E,{}),"端口 (",y.length,")"]}),children:e.jsx(o,{children:e.jsx(C,{columns:xe,dataSource:y,rowKey:"id",pagination:{pageSize:_,showSizeChanger:!0,showQuickJumper:!0,showTotal:t=>`共 ${t} 条记录`},scroll:{x:800}})})},{key:"vulnerabilities",label:e.jsxs("span",{children:[e.jsx(Q,{}),"漏洞 (",l.length,")"]}),children:e.jsx(o,{children:e.jsx(C,{columns:ue,dataSource:l,rowKey:"id",pagination:{pageSize:_,showSizeChanger:!0,showQuickJumper:!0,showTotal:t=>`共 ${t} 条记录`},scroll:{x:800},expandable:{expandedRowRender:t=>e.jsxs("div",{style:{padding:16},children:[e.jsx(Z,{level:5,children:"漏洞描述"}),e.jsx(r,{children:t.description||"暂无描述"}),e.jsx(ke,{}),e.jsxs(k,{gutter:16,children:[e.jsxs(i,{span:8,children:[e.jsx(r,{strong:!0,children:"漏洞ID: "}),e.jsx(r,{code:!0,children:t.vulnerability_id})]}),e.jsxs(i,{span:8,children:[e.jsx(r,{strong:!0,children:"CVSS评分: "}),e.jsx(r,{children:t.cvss_score||"N/A"})]}),e.jsxs(i,{span:8,children:[e.jsx(r,{strong:!0,children:"发现时间: "}),e.jsx(r,{children:g(t.created_at).format("YYYY-MM-DD HH:mm:ss")})]})]})]}),rowExpandable:()=>!0}})})},{key:"web-assets",label:e.jsxs("span",{children:[e.jsx(Ie,{}),"Web资产 (",M.length,")"]}),children:e.jsx(o,{children:e.jsx(C,{columns:[{title:"URL",dataIndex:"url",key:"url",ellipsis:!0,render:t=>e.jsx("a",{href:t,target:"_blank",rel:"noopener noreferrer",children:t})},{title:"状态码",dataIndex:"status_code",key:"status_code",render:t=>e.jsx(m,{color:t===200?"green":t>=400?"red":"orange",children:t})},{title:"标题",dataIndex:"title",key:"title",ellipsis:!0},{title:"技术栈",dataIndex:"technologies",key:"technologies",render:t=>e.jsxs("div",{children:[t==null?void 0:t.slice(0,3).map(a=>e.jsx(m,{children:a},a)),(t==null?void 0:t.length)>3&&e.jsxs(r,{type:"secondary",children:["+",t.length-3]})]})},{title:"发现时间",dataIndex:"created_at",key:"created_at",render:t=>g(t).format("MM-DD HH:mm")}],dataSource:M,rowKey:"id",pagination:{pageSize:_,showSizeChanger:!0,showQuickJumper:!0,showTotal:t=>`共 ${t} 条记录`},scroll:{x:800}})})}]})]})};export{Ae as default};
//# sourceMappingURL=index-Byjsv-ns.js.map
