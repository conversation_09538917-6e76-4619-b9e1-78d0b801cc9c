import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { ScanTask, LogMessage } from '@/types';
import { TaskStatus } from '@/types'; // 作为值导入

interface TaskState {
  tasks: ScanTask[];
  activeTasks: ScanTask[];
  selectedTask: ScanTask | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setTasks: (tasks: ScanTask[]) => void;
  addTask: (task: ScanTask) => void;
  updateTask: (taskId: string, updates: Partial<ScanTask>) => void;
  removeTask: (taskId: string) => void;
  setSelectedTask: (task: ScanTask | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Computed
  getTaskById: (taskId: string) => ScanTask | undefined;
  getTasksByStatus: (status: TaskStatus) => ScanTask[];
}

export const useTaskStore = create<TaskState>()(
  devtools(
    (set, get) => ({
      tasks: [],
      activeTasks: [],
      selectedTask: null,
      isLoading: false,
      error: null,

      setTasks: (tasks) => 
        set((state) => ({
          ...state,
          tasks,
          activeTasks: tasks.filter(task => 
            task.status === TaskStatus.Running || task.status === TaskStatus.Pending
          ),
        })),

      addTask: (task) =>
        set((state) => {
          const newTasks = [...state.tasks, task];
          return {
            ...state,
            tasks: newTasks,
            activeTasks: newTasks.filter(t => 
              t.status === TaskStatus.Running || t.status === TaskStatus.Pending
            ),
          };
        }),

      updateTask: (taskId, updates) =>
        set((state) => {
          const newTasks = state.tasks.map(task =>
            task.id === taskId ? { ...task, ...updates } : task
          );
          return {
            ...state,
            tasks: newTasks,
            activeTasks: newTasks.filter(task => 
              task.status === TaskStatus.Running || task.status === TaskStatus.Pending
            ),
            selectedTask: state.selectedTask?.id === taskId 
              ? { ...state.selectedTask, ...updates } 
              : state.selectedTask,
          };
        }),

      removeTask: (taskId) =>
        set((state) => {
          const newTasks = state.tasks.filter(task => task.id !== taskId);
          return {
            ...state,
            tasks: newTasks,
            activeTasks: newTasks.filter(task => 
              task.status === TaskStatus.Running || task.status === TaskStatus.Pending
            ),
            selectedTask: state.selectedTask?.id === taskId ? null : state.selectedTask,
          };
        }),

      setSelectedTask: (task) => set({ selectedTask: task }),

      setLoading: (isLoading) => set({ isLoading }),

      setError: (error) => set({ error }),

      getTaskById: (taskId) => get().tasks.find(task => task.id === taskId),

      getTasksByStatus: (status) => get().tasks.filter(task => task.status === status),
    }),
    {
      name: 'task-store',
    }
  )
);

// WebSocket 和实时更新状态
interface WebSocketState {
  connected: boolean;
  logs: LogMessage[];
  maxLogs: number;
  
  // Actions
  setConnected: (connected: boolean) => void;
  addLog: (log: LogMessage) => void;
  clearLogs: () => void;
  setMaxLogs: (maxLogs: number) => void;
}

export const useWebSocketStore = create<WebSocketState>()(
  devtools(
    (set, _get) => ({ // 使用下划线前缀避免未使用警告
      connected: false,
      logs: [],
      maxLogs: 1000,

      setConnected: (connected) => set({ connected }),

      addLog: (log) =>
        set((state) => {
          const newLogs = [...state.logs, log];
          // 保持日志数量在最大限制内
          if (newLogs.length > state.maxLogs) {
            newLogs.splice(0, newLogs.length - state.maxLogs);
          }
          return { ...state, logs: newLogs };
        }),

      clearLogs: () => set({ logs: [] }),

      setMaxLogs: (maxLogs) => set({ maxLogs }),
    }),
    {
      name: 'websocket-store',
    }
  )
);

// 应用设置状态
interface SettingsState {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  autoRefresh: boolean;
  refreshInterval: number;
  showNotifications: boolean;
  terminalTheme: string;
  pageSize: number;
  
  // Actions
  setTheme: (theme: 'light' | 'dark' | 'auto') => void;
  setLanguage: (language: string) => void;
  setAutoRefresh: (autoRefresh: boolean) => void;
  setRefreshInterval: (interval: number) => void;
  setShowNotifications: (show: boolean) => void;
  setTerminalTheme: (theme: string) => void;
  setPageSize: (size: number) => void;
}

export const useSettingsStore = create<SettingsState>()(
  devtools(
    (set) => ({
      theme: 'auto',
      language: 'zh-CN',
      autoRefresh: true,
      refreshInterval: 30000, // 30秒
      showNotifications: true,
      terminalTheme: 'dark',
      pageSize: 20,

      setTheme: (theme) => set({ theme }),
      setLanguage: (language) => set({ language }),
      setAutoRefresh: (autoRefresh) => set({ autoRefresh }),
      setRefreshInterval: (refreshInterval) => set({ refreshInterval }),
      setShowNotifications: (showNotifications) => set({ showNotifications }),
      setTerminalTheme: (terminalTheme) => set({ terminalTheme }),
      setPageSize: (pageSize) => set({ pageSize }),
    }),
    {
      name: 'settings-store',
    }
  )
);