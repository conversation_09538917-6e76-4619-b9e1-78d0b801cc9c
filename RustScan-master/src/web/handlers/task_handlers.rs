use crate::models::task::{CreateTaskRequest, TaskStatus};
use crate::services::database::{Database, TaskQuery};
use crate::tools::workflow::WorkflowOrchestrator;
use actix_web::{delete, get, post, web, HttpResponse, Result};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Serialize)]
pub struct ApiResponse<T> {
    success: bool,
    data: Option<T>,
    error: Option<String>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
        }
    }

    pub fn error(message: String) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(message),
        }
    }
}

#[post("")]
async fn create_task(
    db: web::Data<Database>,
    req: web::Json<CreateTaskRequest>,
) -> Result<HttpResponse> {
    match db.create_task(req.into_inner()).await {
        Ok(task) => Ok(HttpResponse::Ok().json(ApiResponse::success(task))),
        Err(e) => {
            log::error!("Failed to create task: {}", e);
            Ok(HttpResponse::InternalServerError().json(ApiResponse::<()>::error(e.to_string())))
        }
    }
}

#[get("")]
async fn get_tasks(
    db: web::Data<Database>,
    query: web::Query<TaskQueryParams>,
) -> Result<HttpResponse> {
    let task_query = TaskQuery {
        page: query.page,
        per_page: query.per_page,
        status: query.status,
    };
    match db.get_tasks(task_query).await {
        Ok(tasks) => Ok(HttpResponse::Ok().json(ApiResponse::success(tasks))),
        Err(e) => {
            log::error!("Failed to get tasks: {}", e);
            Ok(HttpResponse::InternalServerError().json(ApiResponse::<()>::error(e.to_string())))
        }
    }
}

#[get("/{id}")]
async fn get_task(db: web::Data<Database>, path: web::Path<Uuid>) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    match db.get_task(task_id).await {
        Ok(Some(task)) => Ok(HttpResponse::Ok().json(ApiResponse::success(task))),
        Ok(None) => {
            Ok(HttpResponse::NotFound()
                .json(ApiResponse::<()>::error("Task not found".to_string())))
        }
        Err(e) => {
            log::error!("Failed to get task: {}", e);
            Ok(HttpResponse::InternalServerError().json(ApiResponse::<()>::error(e.to_string())))
        }
    }
}

#[delete("/{id}")]
async fn delete_task(db: web::Data<Database>, path: web::Path<Uuid>) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    match db.delete_task(task_id).await {
        Ok(()) => Ok(HttpResponse::Ok().json(ApiResponse::success("Task deleted successfully"))),
        Err(e) => {
            log::error!("Failed to delete task: {}", e);
            Ok(HttpResponse::InternalServerError().json(ApiResponse::<()>::error(e.to_string())))
        }
    }
}

#[post("/{id}/start")]
async fn start_task(db: web::Data<Database>, path: web::Path<Uuid>) -> Result<HttpResponse> {
    let task_id = path.into_inner();

    // 检查任务是否存在
    match db.get_task(task_id).await {
        Ok(Some(task)) => {
            if task.status != TaskStatus::Pending {
                return Ok(
                    HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                        "Task is not in pending state, current status: {:?}",
                        task.status
                    ))),
                );
            }

            // 立即更新任务状态为 Running
            if let Err(e) = db.update_task_status(task_id, TaskStatus::Running).await {
                log::error!("Failed to update task status to Running: {}", e);
                return Ok(HttpResponse::InternalServerError()
                    .json(ApiResponse::<()>::error(e.to_string())));
            }

            // 启动任务执行
            let db_clone = db.get_ref().clone();
            let target = task.target.clone();
            let task_type = task.task_type.clone();

            tokio::spawn(async move {
                match execute_task(db_clone.clone(), task_id, &target, &task_type).await {
                    Ok(_) => {
                        log::info!("Task {} completed successfully", task_id);
                        let _ = db_clone
                            .update_task_status(task_id, TaskStatus::Completed)
                            .await;
                    }
                    Err(e) => {
                        log::error!("Task execution failed: {}", e);
                        let _ = db_clone
                            .update_task_status(task_id, TaskStatus::Failed)
                            .await;
                    }
                }
            });

            Ok(HttpResponse::Ok().json(ApiResponse::success("Task started successfully")))
        }
        Ok(None) => {
            Ok(HttpResponse::NotFound()
                .json(ApiResponse::<()>::error("Task not found".to_string())))
        }
        Err(e) => {
            log::error!("Failed to get task: {}", e);
            Ok(HttpResponse::InternalServerError().json(ApiResponse::<()>::error(e.to_string())))
        }
    }
}

#[post("/{id}/stop")]
async fn stop_task(db: web::Data<Database>, path: web::Path<Uuid>) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    match db.update_task_status(task_id, TaskStatus::Stopped).await {
        Ok(()) => Ok(HttpResponse::Ok().json(ApiResponse::success("Task stopped successfully"))),
        Err(e) => {
            log::error!("Failed to stop task: {}", e);
            Ok(HttpResponse::InternalServerError().json(ApiResponse::<()>::error(e.to_string())))
        }
    }
}

async fn execute_task(
    db: Database,
    task_id: Uuid,
    target: &str,
    task_type: &str,
) -> anyhow::Result<()> {
    log::info!(
        "Starting task execution: {} for target: {}",
        task_id,
        target
    );

    // 根据任务类型选择工作流配置
    let config = match task_type {
        "quick" => WorkflowOrchestrator::quick_scan_config(),
        "standard" => WorkflowOrchestrator::standard_scan_config(),
        "deep" => WorkflowOrchestrator::deep_scan_config(),
        "web_focused" => WorkflowOrchestrator::web_focused_config(),
        _ => WorkflowOrchestrator::standard_scan_config(),
    };

    // 创建工作流编排器
    let orchestrator = WorkflowOrchestrator::new(config, db);

    // 执行工作流
    match orchestrator.execute_workflow(task_id, target).await {
        Ok(_result) => {
            log::info!("Task {} completed successfully", task_id);
            Ok(())
        }
        Err(e) => {
            log::error!("Task {} failed: {}", task_id, e);
            Err(e)
        }
    }
}

#[derive(Deserialize)]
struct TaskQueryParams {
    page: Option<u32>,
    per_page: Option<u32>,
    status: Option<TaskStatus>,
}
