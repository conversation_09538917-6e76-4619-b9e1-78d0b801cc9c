import React, { useState } from 'react';
import { 
  Table, 
  Button, 
  Space, 
  Tag, 
  Progress, 
  Typography, 
  Input, 
  Select, 
  Card,
  Popconfirm,
  message,
  Tooltip,
  Badge,
} from 'antd';
import { useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  PlusOutlined,
  ReloadOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  FilterOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';

import { taskApi } from '@/api';
import { useTaskStore } from '@/hooks/useStore';
import { ScanTask, TaskStatus, ScanType } from '@/types';

const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;

const TaskList: React.FC = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { tasks: _tasks, setTasks: _setTasks } = useTaskStore(); // 暂时未使用，使用下划线前缀
  
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<TaskStatus | 'all'>('all');
  const [typeFilter, setTypeFilter] = useState<ScanType | 'all'>('all');

  // 获取任务列表
  const { data, isLoading, refetch } = useQuery({
    queryKey: ['tasks'],
    queryFn: () => taskApi.getTasks(1, 100),
    refetchInterval: 10000, // 每10秒刷新
  });

  // 启动任务
  const startTaskMutation = useMutation({
    mutationFn: taskApi.startTask,
    onSuccess: () => {
      message.success('任务已启动');
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
    },
    onError: (error: any) => {
      message.error('启动任务失败: ' + error.message);
    },
  });

  // 停止任务
  const stopTaskMutation = useMutation({
    mutationFn: taskApi.stopTask,
    onSuccess: () => {
      message.success('任务已停止');
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
    },
    onError: (error: any) => {
      message.error('停止任务失败: ' + error.message);
    },
  });

  // 删除任务
  const deleteTaskMutation = useMutation({
    mutationFn: taskApi.deleteTask,
    onSuccess: () => {
      message.success('任务已删除');
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
    },
    onError: (error: any) => {
      message.error('删除任务失败: ' + error.message);
    },
  });

  // 重启任务
  const restartTaskMutation = useMutation({
    mutationFn: taskApi.restartTask,
    onSuccess: () => {
      message.success('任务已重启');
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
    },
    onError: (error: any) => {
      message.error('重启任务失败: ' + error.message);
    },
  });

  const taskData = data?.data?.data || [];

  // 过滤任务
  const filteredTasks = taskData.filter((task: ScanTask) => {
    const matchesSearch = task.target.toLowerCase().includes(searchText.toLowerCase()) ||
                         task.id.toLowerCase().includes(searchText.toLowerCase());
    const matchesStatus = statusFilter === 'all' || task.status === statusFilter;
    const matchesType = typeFilter === 'all' || task.task_type === typeFilter;
    
    return matchesSearch && matchesStatus && matchesType;
  });

  // 状态标签颜色
  const getStatusColor = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.Pending:
        return 'default';
      case TaskStatus.Running:
        return 'processing';
      case TaskStatus.Completed:
        return 'success';
      case TaskStatus.Failed:
        return 'error';
      case TaskStatus.Stopped:
        return 'warning';
      default:
        return 'default';
    }
  };

  // 任务类型标签颜色
  const getTypeColor = (type: ScanType) => {
    switch (type) {
      case ScanType.Quick:
        return 'green';
      case ScanType.Standard:
        return 'blue';
      case ScanType.Deep:
        return 'purple';
      case ScanType.WebFocused:
        return 'orange';
      default:
        return 'default';
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '任务ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
      render: (id: string) => (
        <Text code style={{ fontSize: 12 }}>
          {id.slice(-8)}
        </Text>
      ),
    },
    {
      title: '扫描目标',
      dataIndex: 'target',
      key: 'target',
      ellipsis: true,
      render: (target: string) => (
        <Tooltip title={target}>
          <Text>{target}</Text>
        </Tooltip>
      ),
    },
    {
      title: '扫描类型',
      dataIndex: 'task_type',
      key: 'task_type',
      width: 100,
      render: (type: ScanType) => (
        <Tag color={getTypeColor(type)}>
          {type === ScanType.Quick && '快速'}
          {type === ScanType.Standard && '标准'}
          {type === ScanType.Deep && '深度'}
          {type === ScanType.WebFocused && 'Web'}
          {type === ScanType.Custom && '自定义'}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: TaskStatus, record: ScanTask) => (
        <Space>
          <Badge 
            status={
              status === TaskStatus.Running ? 'processing' :
              status === TaskStatus.Completed ? 'success' :
              status === TaskStatus.Failed ? 'error' : 'default'
            } 
          />
          <Tag color={getStatusColor(status)}>
            {status === TaskStatus.Pending && '等待中'}
            {status === TaskStatus.Running && '运行中'}
            {status === TaskStatus.Completed && '已完成'}
            {status === TaskStatus.Failed && '失败'}
            {status === TaskStatus.Stopped && '已停止'}
          </Tag>
        </Space>
      ),
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      width: 120,
      render: (progress: number, record: ScanTask) => (
        <Progress 
          percent={progress} 
          size="small" 
          status={
            record.status === TaskStatus.Failed ? 'exception' :
            record.status === TaskStatus.Completed ? 'success' : 'active'
          }
        />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 160,
      render: (time: string) => (
        <Text>{dayjs(time).format('YYYY-MM-DD HH:mm')}</Text>
      ),
    },
    {
      title: '耗时',
      key: 'duration',
      width: 100,
      render: (record: ScanTask) => {
        if (!record.started_at) return <Text type="secondary">-</Text>;
        
        const endTime = record.completed_at || new Date().toISOString();
        const duration = dayjs(endTime).diff(dayjs(record.started_at), 'second');
        
        if (duration < 60) {
          return <Text>{duration}秒</Text>;
        } else if (duration < 3600) {
          return <Text>{Math.floor(duration / 60)}分钟</Text>;
        } else {
          return <Text>{Math.floor(duration / 3600)}小时</Text>;
        }
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (record: ScanTask) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              size="small"
              onClick={() => navigate(`/tasks/${record.id}`)}
            />
          </Tooltip>
          
          {record.status === TaskStatus.Pending && (
            <Tooltip title="启动任务">
              <Button
                type="text"
                icon={<PlayCircleOutlined />}
                size="small"
                loading={startTaskMutation.isPending}
                onClick={() => startTaskMutation.mutate(record.id)}
              />
            </Tooltip>
          )}

          {record.status === TaskStatus.Running && (
            <Tooltip title="停止任务">
              <Button
                type="text"
                icon={<PauseCircleOutlined />}
                size="small"
                loading={stopTaskMutation.isPending}
                onClick={() => stopTaskMutation.mutate(record.id)}
              />
            </Tooltip>
          )}

          {(record.status === TaskStatus.Failed || record.status === TaskStatus.Stopped) && (
            <Tooltip title="重启任务">
              <Button
                type="text"
                icon={<PlayCircleOutlined />}
                size="small"
                loading={restartTaskMutation.isPending}
                onClick={() => restartTaskMutation.mutate(record.id)}
              />
            </Tooltip>
          )}
          
          <Popconfirm
            title="确定删除此任务？"
            description="删除后将无法恢复任务数据"
            onConfirm={() => deleteTaskMutation.mutate(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除任务">
              <Button
                type="text"
                icon={<DeleteOutlined />}
                size="small"
                danger
                loading={deleteTaskMutation.isPending}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <Card>
        {/* 页面标题和操作 */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          marginBottom: 24 
        }}>
          <div>
            <Title level={3} style={{ margin: 0 }}>
              扫描任务
            </Title>
            <Text type="secondary">
              管理和监控所有扫描任务
            </Text>
          </div>
          
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => refetch()}
              loading={isLoading}
            >
              刷新
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => navigate('/tasks/create')}
            >
              创建任务
            </Button>
          </Space>
        </div>

        {/* 过滤器 */}
        <div style={{ 
          display: 'flex', 
          gap: 16, 
          marginBottom: 16,
          flexWrap: 'wrap' 
        }}>
          <Search
            placeholder="搜索任务ID或目标"
            allowClear
            style={{ width: 300 }}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            prefix={<SearchOutlined />}
          />
          
          <Select
            placeholder="任务状态"
            style={{ width: 120 }}
            value={statusFilter}
            onChange={setStatusFilter}
          >
            <Option value="all">全部状态</Option>
            <Option value={TaskStatus.Pending}>等待中</Option>
            <Option value={TaskStatus.Running}>运行中</Option>
            <Option value={TaskStatus.Completed}>已完成</Option>
            <Option value={TaskStatus.Failed}>失败</Option>
            <Option value={TaskStatus.Stopped}>已停止</Option>
          </Select>
          
          <Select
            placeholder="扫描类型"
            style={{ width: 120 }}
            value={typeFilter}
            onChange={setTypeFilter}
          >
            <Option value="all">全部类型</Option>
            <Option value={ScanType.Quick}>快速扫描</Option>
            <Option value={ScanType.Standard}>标准扫描</Option>
            <Option value={ScanType.Deep}>深度扫描</Option>
            <Option value={ScanType.WebFocused}>Web扫描</Option>
          </Select>
        </div>

        {/* 任务表格 */}
        <Table
          columns={columns}
          dataSource={filteredTasks}
          rowKey="id"
          loading={isLoading}
          pagination={{
            total: filteredTasks.length,
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  );
};

export default TaskList;