import{e as xi,g as ae,r as R,R as S}from"./vendor-o6zXO7vr.js";import{r as wm}from"./ui-Smnqh0BA.js";function Nh(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(r=Nh(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function J(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=Nh(e))&&(n&&(n+=" "),n+=t);return n}var Om=Array.isArray,Le=Om,Am=typeof xi=="object"&&xi&&xi.Object===Object&&xi,Rh=Am,Sm=Rh,Pm=typeof self=="object"&&self&&self.Object===Object&&self,_m=Sm||Pm||Function("return this")(),ft=_m,$m=ft,Tm=$m.Symbol,li=Tm,ks=li,Lh=Object.prototype,Em=Lh.hasOwnProperty,jm=Lh.toString,fn=ks?ks.toStringTag:void 0;function Mm(e){var t=Em.call(e,fn),r=e[fn];try{e[fn]=void 0;var n=!0}catch{}var i=jm.call(e);return n&&(t?e[fn]=r:delete e[fn]),i}var Cm=Mm,Im=Object.prototype,km=Im.toString;function Dm(e){return km.call(e)}var Nm=Dm,Ds=li,Rm=Cm,Lm=Nm,Bm="[object Null]",Fm="[object Undefined]",Ns=Ds?Ds.toStringTag:void 0;function Wm(e){return e==null?e===void 0?Fm:Bm:Ns&&Ns in Object(e)?Rm(e):Lm(e)}var Pt=Wm;function zm(e){return e!=null&&typeof e=="object"}var _t=zm,Um=Pt,qm=_t,Hm="[object Symbol]";function Km(e){return typeof e=="symbol"||qm(e)&&Um(e)==Hm}var Yr=Km,Gm=Le,Vm=Yr,Xm=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Ym=/^\w*$/;function Zm(e,t){if(Gm(e))return!1;var r=typeof e;return r=="number"||r=="symbol"||r=="boolean"||e==null||Vm(e)?!0:Ym.test(e)||!Xm.test(e)||t!=null&&e in Object(t)}var _c=Zm;function Jm(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var It=Jm;const Zr=ae(It);var Qm=Pt,eg=It,tg="[object AsyncFunction]",rg="[object Function]",ng="[object GeneratorFunction]",ig="[object Proxy]";function ag(e){if(!eg(e))return!1;var t=Qm(e);return t==rg||t==ng||t==tg||t==ig}var $c=ag;const X=ae($c);var og=ft,ug=og["__core-js_shared__"],cg=ug,So=cg,Rs=function(){var e=/[^.]+$/.exec(So&&So.keys&&So.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function sg(e){return!!Rs&&Rs in e}var lg=sg,fg=Function.prototype,pg=fg.toString;function hg(e){if(e!=null){try{return pg.call(e)}catch{}try{return e+""}catch{}}return""}var Bh=hg,dg=$c,vg=lg,yg=It,mg=Bh,gg=/[\\^$.*+?()[\]{}|]/g,bg=/^\[object .+?Constructor\]$/,xg=Function.prototype,wg=Object.prototype,Og=xg.toString,Ag=wg.hasOwnProperty,Sg=RegExp("^"+Og.call(Ag).replace(gg,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function Pg(e){if(!yg(e)||vg(e))return!1;var t=dg(e)?Sg:bg;return t.test(mg(e))}var _g=Pg;function $g(e,t){return e==null?void 0:e[t]}var Tg=$g,Eg=_g,jg=Tg;function Mg(e,t){var r=jg(e,t);return Eg(r)?r:void 0}var or=Mg,Cg=or,Ig=Cg(Object,"create"),Ra=Ig,Ls=Ra;function kg(){this.__data__=Ls?Ls(null):{},this.size=0}var Dg=kg;function Ng(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var Rg=Ng,Lg=Ra,Bg="__lodash_hash_undefined__",Fg=Object.prototype,Wg=Fg.hasOwnProperty;function zg(e){var t=this.__data__;if(Lg){var r=t[e];return r===Bg?void 0:r}return Wg.call(t,e)?t[e]:void 0}var Ug=zg,qg=Ra,Hg=Object.prototype,Kg=Hg.hasOwnProperty;function Gg(e){var t=this.__data__;return qg?t[e]!==void 0:Kg.call(t,e)}var Vg=Gg,Xg=Ra,Yg="__lodash_hash_undefined__";function Zg(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=Xg&&t===void 0?Yg:t,this}var Jg=Zg,Qg=Dg,eb=Rg,tb=Ug,rb=Vg,nb=Jg;function Jr(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Jr.prototype.clear=Qg;Jr.prototype.delete=eb;Jr.prototype.get=tb;Jr.prototype.has=rb;Jr.prototype.set=nb;var ib=Jr;function ab(){this.__data__=[],this.size=0}var ob=ab;function ub(e,t){return e===t||e!==e&&t!==t}var Tc=ub,cb=Tc;function sb(e,t){for(var r=e.length;r--;)if(cb(e[r][0],t))return r;return-1}var La=sb,lb=La,fb=Array.prototype,pb=fb.splice;function hb(e){var t=this.__data__,r=lb(t,e);if(r<0)return!1;var n=t.length-1;return r==n?t.pop():pb.call(t,r,1),--this.size,!0}var db=hb,vb=La;function yb(e){var t=this.__data__,r=vb(t,e);return r<0?void 0:t[r][1]}var mb=yb,gb=La;function bb(e){return gb(this.__data__,e)>-1}var xb=bb,wb=La;function Ob(e,t){var r=this.__data__,n=wb(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this}var Ab=Ob,Sb=ob,Pb=db,_b=mb,$b=xb,Tb=Ab;function Qr(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Qr.prototype.clear=Sb;Qr.prototype.delete=Pb;Qr.prototype.get=_b;Qr.prototype.has=$b;Qr.prototype.set=Tb;var Ba=Qr,Eb=or,jb=ft,Mb=Eb(jb,"Map"),Ec=Mb,Bs=ib,Cb=Ba,Ib=Ec;function kb(){this.size=0,this.__data__={hash:new Bs,map:new(Ib||Cb),string:new Bs}}var Db=kb;function Nb(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}var Rb=Nb,Lb=Rb;function Bb(e,t){var r=e.__data__;return Lb(t)?r[typeof t=="string"?"string":"hash"]:r.map}var Fa=Bb,Fb=Fa;function Wb(e){var t=Fb(this,e).delete(e);return this.size-=t?1:0,t}var zb=Wb,Ub=Fa;function qb(e){return Ub(this,e).get(e)}var Hb=qb,Kb=Fa;function Gb(e){return Kb(this,e).has(e)}var Vb=Gb,Xb=Fa;function Yb(e,t){var r=Xb(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this}var Zb=Yb,Jb=Db,Qb=zb,e0=Hb,t0=Vb,r0=Zb;function en(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}en.prototype.clear=Jb;en.prototype.delete=Qb;en.prototype.get=e0;en.prototype.has=t0;en.prototype.set=r0;var jc=en,Fh=jc,n0="Expected a function";function Mc(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new TypeError(n0);var r=function(){var n=arguments,i=t?t.apply(this,n):n[0],a=r.cache;if(a.has(i))return a.get(i);var o=e.apply(this,n);return r.cache=a.set(i,o)||a,o};return r.cache=new(Mc.Cache||Fh),r}Mc.Cache=Fh;var Wh=Mc;const i0=ae(Wh);var a0=Wh,o0=500;function u0(e){var t=a0(e,function(n){return r.size===o0&&r.clear(),n}),r=t.cache;return t}var c0=u0,s0=c0,l0=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,f0=/\\(\\)?/g,p0=s0(function(e){var t=[];return e.charCodeAt(0)===46&&t.push(""),e.replace(l0,function(r,n,i,a){t.push(i?a.replace(f0,"$1"):n||r)}),t}),h0=p0;function d0(e,t){for(var r=-1,n=e==null?0:e.length,i=Array(n);++r<n;)i[r]=t(e[r],r,e);return i}var Cc=d0,Fs=li,v0=Cc,y0=Le,m0=Yr,Ws=Fs?Fs.prototype:void 0,zs=Ws?Ws.toString:void 0;function zh(e){if(typeof e=="string")return e;if(y0(e))return v0(e,zh)+"";if(m0(e))return zs?zs.call(e):"";var t=e+"";return t=="0"&&1/e==-1/0?"-0":t}var g0=zh,b0=g0;function x0(e){return e==null?"":b0(e)}var Uh=x0,w0=Le,O0=_c,A0=h0,S0=Uh;function P0(e,t){return w0(e)?e:O0(e,t)?[e]:A0(S0(e))}var qh=P0,_0=Yr;function $0(e){if(typeof e=="string"||_0(e))return e;var t=e+"";return t=="0"&&1/e==-1/0?"-0":t}var Wa=$0,T0=qh,E0=Wa;function j0(e,t){t=T0(t,e);for(var r=0,n=t.length;e!=null&&r<n;)e=e[E0(t[r++])];return r&&r==n?e:void 0}var Ic=j0,M0=Ic;function C0(e,t,r){var n=e==null?void 0:M0(e,t);return n===void 0?r:n}var Hh=C0;const He=ae(Hh);function I0(e){return e==null}var k0=I0;const Y=ae(k0);var D0=Pt,N0=Le,R0=_t,L0="[object String]";function B0(e){return typeof e=="string"||!N0(e)&&R0(e)&&D0(e)==L0}var F0=B0;const er=ae(F0);var W0=Pt,z0=_t,U0="[object Number]";function q0(e){return typeof e=="number"||z0(e)&&W0(e)==U0}var Kh=q0;const H0=ae(Kh);var K0=Kh;function G0(e){return K0(e)&&e!=+e}var V0=G0;const fi=ae(V0);var Ce=function(t){return t===0?0:t>0?1:-1},Vt=function(t){return er(t)&&t.indexOf("%")===t.length-1},L=function(t){return H0(t)&&!fi(t)},X0=function(t){return Y(t)},Se=function(t){return L(t)||er(t)},Y0=0,tn=function(t){var r=++Y0;return"".concat(t||"").concat(r)},Ie=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!L(t)&&!er(t))return n;var a;if(Vt(t)){var o=t.indexOf("%");a=r*parseFloat(t.slice(0,o))/100}else a=+t;return fi(a)&&(a=n),i&&a>r&&(a=r),a},jt=function(t){if(!t)return null;var r=Object.keys(t);return r&&r.length?t[r[0]]:null},Z0=function(t){if(!Array.isArray(t))return!1;for(var r=t.length,n={},i=0;i<r;i++)if(!n[t[i]])n[t[i]]=!0;else return!0;return!1},Ue=function(t,r){return L(t)&&L(r)?function(n){return t+n*(r-t)}:function(){return r}};function Ni(e,t,r){return!e||!e.length?null:e.find(function(n){return n&&(typeof t=="function"?t(n):He(n,t))===r})}var J0=function(t,r){return L(t)&&L(r)?t-r:er(t)&&er(r)?t.localeCompare(r):t instanceof Date&&r instanceof Date?t.getTime()-r.getTime():String(t).localeCompare(String(r))};function wr(e,t){for(var r in e)if({}.hasOwnProperty.call(e,r)&&(!{}.hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if({}.hasOwnProperty.call(t,n)&&!{}.hasOwnProperty.call(e,n))return!1;return!0}function Jo(e){"@babel/helpers - typeof";return Jo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Jo(e)}var Q0=["viewBox","children"],ex=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],Us=["points","pathLength"],Po={svg:Q0,polygon:Us,polyline:Us},kc=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],Ri=function(t,r){if(!t||typeof t=="function"||typeof t=="boolean")return null;var n=t;if(R.isValidElement(t)&&(n=t.props),!Zr(n))return null;var i={};return Object.keys(n).forEach(function(a){kc.includes(a)&&(i[a]=r||function(o){return n[a](n,o)})}),i},tx=function(t,r,n){return function(i){return t(r,n,i),null}},tr=function(t,r,n){if(!Zr(t)||Jo(t)!=="object")return null;var i=null;return Object.keys(t).forEach(function(a){var o=t[a];kc.includes(a)&&typeof o=="function"&&(i||(i={}),i[a]=tx(o,r,n))}),i},rx=["children"],nx=["children"];function qs(e,t){if(e==null)return{};var r=ix(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function ix(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Qo(e){"@babel/helpers - typeof";return Qo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qo(e)}var Hs={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},bt=function(t){return typeof t=="string"?t:t?t.displayName||t.name||"Component":""},Ks=null,_o=null,Dc=function e(t){if(t===Ks&&Array.isArray(_o))return _o;var r=[];return R.Children.forEach(t,function(n){Y(n)||(wm.isFragment(n)?r=r.concat(e(n.props.children)):r.push(n))}),_o=r,Ks=t,r};function Ke(e,t){var r=[],n=[];return Array.isArray(t)?n=t.map(function(i){return bt(i)}):n=[bt(t)],Dc(e).forEach(function(i){var a=He(i,"type.displayName")||He(i,"type.name");n.indexOf(a)!==-1&&r.push(i)}),r}function ze(e,t){var r=Ke(e,t);return r&&r[0]}var Gs=function(t){if(!t||!t.props)return!1;var r=t.props,n=r.width,i=r.height;return!(!L(n)||n<=0||!L(i)||i<=0)},ax=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],ox=function(t){return t&&t.type&&er(t.type)&&ax.indexOf(t.type)>=0},ux=function(t){return t&&Qo(t)==="object"&&"clipDot"in t},cx=function(t,r,n,i){var a,o=(a=Po==null?void 0:Po[i])!==null&&a!==void 0?a:[];return r.startsWith("data-")||!X(t)&&(i&&o.includes(r)||ex.includes(r))||n&&kc.includes(r)},H=function(t,r,n){if(!t||typeof t=="function"||typeof t=="boolean")return null;var i=t;if(R.isValidElement(t)&&(i=t.props),!Zr(i))return null;var a={};return Object.keys(i).forEach(function(o){var u;cx((u=i)===null||u===void 0?void 0:u[o],o,r,n)&&(a[o]=i[o])}),a},eu=function e(t,r){if(t===r)return!0;var n=R.Children.count(t);if(n!==R.Children.count(r))return!1;if(n===0)return!0;if(n===1)return Vs(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var a=t[i],o=r[i];if(Array.isArray(a)||Array.isArray(o)){if(!e(a,o))return!1}else if(!Vs(a,o))return!1}return!0},Vs=function(t,r){if(Y(t)&&Y(r))return!0;if(!Y(t)&&!Y(r)){var n=t.props||{},i=n.children,a=qs(n,rx),o=r.props||{},u=o.children,c=qs(o,nx);return i&&u?wr(a,c)&&eu(i,u):!i&&!u?wr(a,c):!1}return!1},Xs=function(t,r){var n=[],i={};return Dc(t).forEach(function(a,o){if(ox(a))n.push(a);else if(a){var u=bt(a.type),c=r[u]||{},s=c.handler,f=c.once;if(s&&(!f||!i[u])){var l=s(a,u,o);n.push(l),i[u]=!0}}}),n},sx=function(t){var r=t&&t.type;return r&&Hs[r]?Hs[r]:null},lx=function(t,r){return Dc(r).indexOf(t)},fx=["children","width","height","viewBox","className","style","title","desc"];function tu(){return tu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},tu.apply(this,arguments)}function px(e,t){if(e==null)return{};var r=hx(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function hx(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function ru(e){var t=e.children,r=e.width,n=e.height,i=e.viewBox,a=e.className,o=e.style,u=e.title,c=e.desc,s=px(e,fx),f=i||{width:r,height:n,x:0,y:0},l=J("recharts-surface",a);return S.createElement("svg",tu({},H(s,!0,"svg"),{className:l,width:r,height:n,style:o,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),S.createElement("title",null,u),S.createElement("desc",null,c),t)}var dx=["children","className"];function nu(){return nu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},nu.apply(this,arguments)}function vx(e,t){if(e==null)return{};var r=yx(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function yx(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var te=S.forwardRef(function(e,t){var r=e.children,n=e.className,i=vx(e,dx),a=J("recharts-layer",n);return S.createElement("g",nu({className:a},H(i,!0),{ref:t}),r)}),it=function(t,r){for(var n=arguments.length,i=new Array(n>2?n-2:0),a=2;a<n;a++)i[a-2]=arguments[a]};function mx(e,t,r){var n=-1,i=e.length;t<0&&(t=-t>i?0:i+t),r=r>i?i:r,r<0&&(r+=i),i=t>r?0:r-t>>>0,t>>>=0;for(var a=Array(i);++n<i;)a[n]=e[n+t];return a}var gx=mx,bx=gx;function xx(e,t,r){var n=e.length;return r=r===void 0?n:r,!t&&r>=n?e:bx(e,t,r)}var wx=xx,Ox="\\ud800-\\udfff",Ax="\\u0300-\\u036f",Sx="\\ufe20-\\ufe2f",Px="\\u20d0-\\u20ff",_x=Ax+Sx+Px,$x="\\ufe0e\\ufe0f",Tx="\\u200d",Ex=RegExp("["+Tx+Ox+_x+$x+"]");function jx(e){return Ex.test(e)}var Gh=jx;function Mx(e){return e.split("")}var Cx=Mx,Vh="\\ud800-\\udfff",Ix="\\u0300-\\u036f",kx="\\ufe20-\\ufe2f",Dx="\\u20d0-\\u20ff",Nx=Ix+kx+Dx,Rx="\\ufe0e\\ufe0f",Lx="["+Vh+"]",iu="["+Nx+"]",au="\\ud83c[\\udffb-\\udfff]",Bx="(?:"+iu+"|"+au+")",Xh="[^"+Vh+"]",Yh="(?:\\ud83c[\\udde6-\\uddff]){2}",Zh="[\\ud800-\\udbff][\\udc00-\\udfff]",Fx="\\u200d",Jh=Bx+"?",Qh="["+Rx+"]?",Wx="(?:"+Fx+"(?:"+[Xh,Yh,Zh].join("|")+")"+Qh+Jh+")*",zx=Qh+Jh+Wx,Ux="(?:"+[Xh+iu+"?",iu,Yh,Zh,Lx].join("|")+")",qx=RegExp(au+"(?="+au+")|"+Ux+zx,"g");function Hx(e){return e.match(qx)||[]}var Kx=Hx,Gx=Cx,Vx=Gh,Xx=Kx;function Yx(e){return Vx(e)?Xx(e):Gx(e)}var Zx=Yx,Jx=wx,Qx=Gh,ew=Zx,tw=Uh;function rw(e){return function(t){t=tw(t);var r=Qx(t)?ew(t):void 0,n=r?r[0]:t.charAt(0),i=r?Jx(r,1).join(""):t.slice(1);return n[e]()+i}}var nw=rw,iw=nw,aw=iw("toUpperCase"),ow=aw;const za=ae(ow);function ce(e){return function(){return e}}const ed=Math.cos,Li=Math.sin,at=Math.sqrt,Bi=Math.PI,Ua=2*Bi,ou=Math.PI,uu=2*ou,qt=1e-6,uw=uu-qt;function td(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}function cw(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return td;const r=10**t;return function(n){this._+=n[0];for(let i=1,a=n.length;i<a;++i)this._+=Math.round(arguments[i]*r)/r+n[i]}}class sw{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=t==null?td:cw(t)}moveTo(t,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,r){this._append`L${this._x1=+t},${this._y1=+r}`}quadraticCurveTo(t,r,n,i){this._append`Q${+t},${+r},${this._x1=+n},${this._y1=+i}`}bezierCurveTo(t,r,n,i,a,o){this._append`C${+t},${+r},${+n},${+i},${this._x1=+a},${this._y1=+o}`}arcTo(t,r,n,i,a){if(t=+t,r=+r,n=+n,i=+i,a=+a,a<0)throw new Error(`negative radius: ${a}`);let o=this._x1,u=this._y1,c=n-t,s=i-r,f=o-t,l=u-r,p=f*f+l*l;if(this._x1===null)this._append`M${this._x1=t},${this._y1=r}`;else if(p>qt)if(!(Math.abs(l*c-s*f)>qt)||!a)this._append`L${this._x1=t},${this._y1=r}`;else{let h=n-o,v=i-u,d=c*c+s*s,y=h*h+v*v,b=Math.sqrt(d),w=Math.sqrt(p),x=a*Math.tan((ou-Math.acos((d+p-y)/(2*b*w)))/2),A=x/w,m=x/b;Math.abs(A-1)>qt&&this._append`L${t+A*f},${r+A*l}`,this._append`A${a},${a},0,0,${+(l*h>f*v)},${this._x1=t+m*c},${this._y1=r+m*s}`}}arc(t,r,n,i,a,o){if(t=+t,r=+r,n=+n,o=!!o,n<0)throw new Error(`negative radius: ${n}`);let u=n*Math.cos(i),c=n*Math.sin(i),s=t+u,f=r+c,l=1^o,p=o?i-a:a-i;this._x1===null?this._append`M${s},${f}`:(Math.abs(this._x1-s)>qt||Math.abs(this._y1-f)>qt)&&this._append`L${s},${f}`,n&&(p<0&&(p=p%uu+uu),p>uw?this._append`A${n},${n},0,1,${l},${t-u},${r-c}A${n},${n},0,1,${l},${this._x1=s},${this._y1=f}`:p>qt&&this._append`A${n},${n},0,${+(p>=ou)},${l},${this._x1=t+n*Math.cos(a)},${this._y1=r+n*Math.sin(a)}`)}rect(t,r,n,i){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}h${n=+n}v${+i}h${-n}Z`}toString(){return this._}}function Nc(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(r==null)t=null;else{const n=Math.floor(r);if(!(n>=0))throw new RangeError(`invalid digits: ${r}`);t=n}return e},()=>new sw(t)}function Rc(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function rd(e){this._context=e}rd.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t);break}}};function qa(e){return new rd(e)}function nd(e){return e[0]}function id(e){return e[1]}function ad(e,t){var r=ce(!0),n=null,i=qa,a=null,o=Nc(u);e=typeof e=="function"?e:e===void 0?nd:ce(e),t=typeof t=="function"?t:t===void 0?id:ce(t);function u(c){var s,f=(c=Rc(c)).length,l,p=!1,h;for(n==null&&(a=i(h=o())),s=0;s<=f;++s)!(s<f&&r(l=c[s],s,c))===p&&((p=!p)?a.lineStart():a.lineEnd()),p&&a.point(+e(l,s,c),+t(l,s,c));if(h)return a=null,h+""||null}return u.x=function(c){return arguments.length?(e=typeof c=="function"?c:ce(+c),u):e},u.y=function(c){return arguments.length?(t=typeof c=="function"?c:ce(+c),u):t},u.defined=function(c){return arguments.length?(r=typeof c=="function"?c:ce(!!c),u):r},u.curve=function(c){return arguments.length?(i=c,n!=null&&(a=i(n)),u):i},u.context=function(c){return arguments.length?(c==null?n=a=null:a=i(n=c),u):n},u}function wi(e,t,r){var n=null,i=ce(!0),a=null,o=qa,u=null,c=Nc(s);e=typeof e=="function"?e:e===void 0?nd:ce(+e),t=typeof t=="function"?t:ce(t===void 0?0:+t),r=typeof r=="function"?r:r===void 0?id:ce(+r);function s(l){var p,h,v,d=(l=Rc(l)).length,y,b=!1,w,x=new Array(d),A=new Array(d);for(a==null&&(u=o(w=c())),p=0;p<=d;++p){if(!(p<d&&i(y=l[p],p,l))===b)if(b=!b)h=p,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),v=p-1;v>=h;--v)u.point(x[v],A[v]);u.lineEnd(),u.areaEnd()}b&&(x[p]=+e(y,p,l),A[p]=+t(y,p,l),u.point(n?+n(y,p,l):x[p],r?+r(y,p,l):A[p]))}if(w)return u=null,w+""||null}function f(){return ad().defined(i).curve(o).context(a)}return s.x=function(l){return arguments.length?(e=typeof l=="function"?l:ce(+l),n=null,s):e},s.x0=function(l){return arguments.length?(e=typeof l=="function"?l:ce(+l),s):e},s.x1=function(l){return arguments.length?(n=l==null?null:typeof l=="function"?l:ce(+l),s):n},s.y=function(l){return arguments.length?(t=typeof l=="function"?l:ce(+l),r=null,s):t},s.y0=function(l){return arguments.length?(t=typeof l=="function"?l:ce(+l),s):t},s.y1=function(l){return arguments.length?(r=l==null?null:typeof l=="function"?l:ce(+l),s):r},s.lineX0=s.lineY0=function(){return f().x(e).y(t)},s.lineY1=function(){return f().x(e).y(r)},s.lineX1=function(){return f().x(n).y(t)},s.defined=function(l){return arguments.length?(i=typeof l=="function"?l:ce(!!l),s):i},s.curve=function(l){return arguments.length?(o=l,a!=null&&(u=o(a)),s):o},s.context=function(l){return arguments.length?(l==null?a=u=null:u=o(a=l),s):a},s}class od{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}}function lw(e){return new od(e,!0)}function fw(e){return new od(e,!1)}const Lc={draw(e,t){const r=at(t/Bi);e.moveTo(r,0),e.arc(0,0,r,0,Ua)}},pw={draw(e,t){const r=at(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},ud=at(1/3),hw=ud*2,dw={draw(e,t){const r=at(t/hw),n=r*ud;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},vw={draw(e,t){const r=at(t),n=-r/2;e.rect(n,n,r,r)}},yw=.8908130915292852,cd=Li(Bi/10)/Li(7*Bi/10),mw=Li(Ua/10)*cd,gw=-ed(Ua/10)*cd,bw={draw(e,t){const r=at(t*yw),n=mw*r,i=gw*r;e.moveTo(0,-r),e.lineTo(n,i);for(let a=1;a<5;++a){const o=Ua*a/5,u=ed(o),c=Li(o);e.lineTo(c*r,-u*r),e.lineTo(u*n-c*i,c*n+u*i)}e.closePath()}},$o=at(3),xw={draw(e,t){const r=-at(t/($o*3));e.moveTo(0,r*2),e.lineTo(-$o*r,-r),e.lineTo($o*r,-r),e.closePath()}},Ge=-.5,Ve=at(3)/2,cu=1/at(12),ww=(cu/2+1)*3,Ow={draw(e,t){const r=at(t/ww),n=r/2,i=r*cu,a=n,o=r*cu+r,u=-a,c=o;e.moveTo(n,i),e.lineTo(a,o),e.lineTo(u,c),e.lineTo(Ge*n-Ve*i,Ve*n+Ge*i),e.lineTo(Ge*a-Ve*o,Ve*a+Ge*o),e.lineTo(Ge*u-Ve*c,Ve*u+Ge*c),e.lineTo(Ge*n+Ve*i,Ge*i-Ve*n),e.lineTo(Ge*a+Ve*o,Ge*o-Ve*a),e.lineTo(Ge*u+Ve*c,Ge*c-Ve*u),e.closePath()}};function Aw(e,t){let r=null,n=Nc(i);e=typeof e=="function"?e:ce(e||Lc),t=typeof t=="function"?t:ce(t===void 0?64:+t);function i(){let a;if(r||(r=a=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),a)return r=null,a+""||null}return i.type=function(a){return arguments.length?(e=typeof a=="function"?a:ce(a),i):e},i.size=function(a){return arguments.length?(t=typeof a=="function"?a:ce(+a),i):t},i.context=function(a){return arguments.length?(r=a??null,i):r},i}function Fi(){}function Wi(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function sd(e){this._context=e}sd.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:Wi(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:Wi(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function Sw(e){return new sd(e)}function ld(e){this._context=e}ld.prototype={areaStart:Fi,areaEnd:Fi,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:Wi(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function Pw(e){return new ld(e)}function fd(e){this._context=e}fd.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:Wi(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function _w(e){return new fd(e)}function pd(e){this._context=e}pd.prototype={areaStart:Fi,areaEnd:Fi,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function $w(e){return new pd(e)}function Ys(e){return e<0?-1:1}function Zs(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0),u=(a*i+o*n)/(n+i);return(Ys(a)+Ys(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(u))||0}function Js(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function To(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,u=(a-n)/3;e._context.bezierCurveTo(n+u,i+u*t,a-u,o-u*r,a,o)}function zi(e){this._context=e}zi.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:To(this,this._t0,Js(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(e=+e,t=+t,!(e===this._x1&&t===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,To(this,Js(this,r=Zs(this,e,t)),r);break;default:To(this,this._t0,r=Zs(this,e,t));break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}};function hd(e){this._context=new dd(e)}(hd.prototype=Object.create(zi.prototype)).point=function(e,t){zi.prototype.point.call(this,t,e)};function dd(e){this._context=e}dd.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}};function Tw(e){return new zi(e)}function Ew(e){return new hd(e)}function vd(e){this._context=e}vd.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),r===2)this._context.lineTo(e[1],t[1]);else for(var n=Qs(e),i=Qs(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||this._line!==0&&r===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}};function Qs(e){var t,r=e.length-1,n,i=new Array(r),a=new Array(r),o=new Array(r);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<r-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[r-1]=2,a[r-1]=7,o[r-1]=8*e[r-1]+e[r],t=1;t<r;++t)n=i[t]/a[t-1],a[t]-=n,o[t]-=n*o[t-1];for(i[r-1]=o[r-1]/a[r-1],t=r-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(a[r-1]=(e[r]+i[r-1])/2,t=0;t<r-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function jw(e){return new vd(e)}function Ha(e,t){this._context=e,this._t=t}Ha.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}break}}this._x=e,this._y=t}};function Mw(e){return new Ha(e,.5)}function Cw(e){return new Ha(e,0)}function Iw(e){return new Ha(e,1)}function Pr(e,t){if((o=e.length)>1)for(var r=1,n,i,a=e[t[0]],o,u=a.length;r<o;++r)for(i=a,a=e[t[r]],n=0;n<u;++n)a[n][1]+=a[n][0]=isNaN(i[n][1])?i[n][0]:i[n][1]}function su(e){for(var t=e.length,r=new Array(t);--t>=0;)r[t]=t;return r}function kw(e,t){return e[t]}function Dw(e){const t=[];return t.key=e,t}function Nw(){var e=ce([]),t=su,r=Pr,n=kw;function i(a){var o=Array.from(e.apply(this,arguments),Dw),u,c=o.length,s=-1,f;for(const l of a)for(u=0,++s;u<c;++u)(o[u][s]=[0,+n(l,o[u].key,s,a)]).data=l;for(u=0,f=Rc(t(o));u<c;++u)o[f[u]].index=u;return r(o,f),o}return i.keys=function(a){return arguments.length?(e=typeof a=="function"?a:ce(Array.from(a)),i):e},i.value=function(a){return arguments.length?(n=typeof a=="function"?a:ce(+a),i):n},i.order=function(a){return arguments.length?(t=a==null?su:typeof a=="function"?a:ce(Array.from(a)),i):t},i.offset=function(a){return arguments.length?(r=a??Pr,i):r},i}function Rw(e,t){if((n=e.length)>0){for(var r,n,i=0,a=e[0].length,o;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}Pr(e,t)}}function Lw(e,t){if((i=e.length)>0){for(var r=0,n=e[t[0]],i,a=n.length;r<a;++r){for(var o=0,u=0;o<i;++o)u+=e[o][r][1]||0;n[r][1]+=n[r][0]=-u/2}Pr(e,t)}}function Bw(e,t){if(!(!((o=e.length)>0)||!((a=(i=e[t[0]]).length)>0))){for(var r=0,n=1,i,a,o;n<a;++n){for(var u=0,c=0,s=0;u<o;++u){for(var f=e[t[u]],l=f[n][1]||0,p=f[n-1][1]||0,h=(l-p)/2,v=0;v<u;++v){var d=e[t[v]],y=d[n][1]||0,b=d[n-1][1]||0;h+=y-b}c+=l,s+=h*l}i[n-1][1]+=i[n-1][0]=r,c&&(r-=s/c)}i[n-1][1]+=i[n-1][0]=r,Pr(e,t)}}function En(e){"@babel/helpers - typeof";return En=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},En(e)}var Fw=["type","size","sizeType"];function lu(){return lu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},lu.apply(this,arguments)}function el(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function tl(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?el(Object(r),!0).forEach(function(n){Ww(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):el(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ww(e,t,r){return t=zw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zw(e){var t=Uw(e,"string");return En(t)=="symbol"?t:t+""}function Uw(e,t){if(En(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(En(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function qw(e,t){if(e==null)return{};var r=Hw(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Hw(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var yd={symbolCircle:Lc,symbolCross:pw,symbolDiamond:dw,symbolSquare:vw,symbolStar:bw,symbolTriangle:xw,symbolWye:Ow},Kw=Math.PI/180,Gw=function(t){var r="symbol".concat(za(t));return yd[r]||Lc},Vw=function(t,r,n){if(r==="area")return t;switch(n){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":{var i=18*Kw;return 1.25*t*t*(Math.tan(i)-Math.tan(i*2)*Math.pow(Math.tan(i),2))}case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},Xw=function(t,r){yd["symbol".concat(za(t))]=r},Bc=function(t){var r=t.type,n=r===void 0?"circle":r,i=t.size,a=i===void 0?64:i,o=t.sizeType,u=o===void 0?"area":o,c=qw(t,Fw),s=tl(tl({},c),{},{type:n,size:a,sizeType:u}),f=function(){var y=Gw(n),b=Aw().type(y).size(Vw(a,u,n));return b()},l=s.className,p=s.cx,h=s.cy,v=H(s,!0);return p===+p&&h===+h&&a===+a?S.createElement("path",lu({},v,{className:J("recharts-symbols",l),transform:"translate(".concat(p,", ").concat(h,")"),d:f()})):null};Bc.registerSymbol=Xw;function _r(e){"@babel/helpers - typeof";return _r=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_r(e)}function fu(){return fu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},fu.apply(this,arguments)}function rl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Yw(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?rl(Object(r),!0).forEach(function(n){jn(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rl(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Zw(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Jw(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,gd(n.key),n)}}function Qw(e,t,r){return t&&Jw(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function eO(e,t,r){return t=Ui(t),tO(e,md()?Reflect.construct(t,r||[],Ui(e).constructor):t.apply(e,r))}function tO(e,t){if(t&&(_r(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return rO(e)}function rO(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function md(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(md=function(){return!!e})()}function Ui(e){return Ui=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ui(e)}function nO(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&pu(e,t)}function pu(e,t){return pu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},pu(e,t)}function jn(e,t,r){return t=gd(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function gd(e){var t=iO(e,"string");return _r(t)=="symbol"?t:t+""}function iO(e,t){if(_r(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(_r(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Xe=32,Fc=function(e){function t(){return Zw(this,t),eO(this,t,arguments)}return nO(t,e),Qw(t,[{key:"renderIcon",value:function(n){var i=this.props.inactiveColor,a=Xe/2,o=Xe/6,u=Xe/3,c=n.inactive?i:n.color;if(n.type==="plainline")return S.createElement("line",{strokeWidth:4,fill:"none",stroke:c,strokeDasharray:n.payload.strokeDasharray,x1:0,y1:a,x2:Xe,y2:a,className:"recharts-legend-icon"});if(n.type==="line")return S.createElement("path",{strokeWidth:4,fill:"none",stroke:c,d:"M0,".concat(a,"h").concat(u,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(2*u,",").concat(a,`
            H`).concat(Xe,"M").concat(2*u,",").concat(a,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(u,",").concat(a),className:"recharts-legend-icon"});if(n.type==="rect")return S.createElement("path",{stroke:"none",fill:c,d:"M0,".concat(Xe/8,"h").concat(Xe,"v").concat(Xe*3/4,"h").concat(-Xe,"z"),className:"recharts-legend-icon"});if(S.isValidElement(n.legendIcon)){var s=Yw({},n);return delete s.legendIcon,S.cloneElement(n.legendIcon,s)}return S.createElement(Bc,{fill:c,cx:a,cy:a,size:Xe,sizeType:"diameter",type:n.type})}},{key:"renderItems",value:function(){var n=this,i=this.props,a=i.payload,o=i.iconSize,u=i.layout,c=i.formatter,s=i.inactiveColor,f={x:0,y:0,width:Xe,height:Xe},l={display:u==="horizontal"?"inline-block":"block",marginRight:10},p={display:"inline-block",verticalAlign:"middle",marginRight:4};return a.map(function(h,v){var d=h.formatter||c,y=J(jn(jn({"recharts-legend-item":!0},"legend-item-".concat(v),!0),"inactive",h.inactive));if(h.type==="none")return null;var b=X(h.value)?null:h.value;it(!X(h.value),`The name property is also required when using a function for the dataKey of a chart's cartesian components. Ex: <Bar name="Name of my Data"/>`);var w=h.inactive?s:h.color;return S.createElement("li",fu({className:y,style:l,key:"legend-item-".concat(v)},tr(n.props,h,v)),S.createElement(ru,{width:o,height:o,viewBox:f,style:p},n.renderIcon(h)),S.createElement("span",{className:"recharts-legend-item-text",style:{color:w}},d?d(b,h,v):b))})}},{key:"render",value:function(){var n=this.props,i=n.payload,a=n.layout,o=n.align;if(!i||!i.length)return null;var u={padding:0,margin:0,textAlign:a==="horizontal"?o:"left"};return S.createElement("ul",{className:"recharts-default-legend",style:u},this.renderItems())}}])}(R.PureComponent);jn(Fc,"displayName","Legend");jn(Fc,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var aO=Ba;function oO(){this.__data__=new aO,this.size=0}var uO=oO;function cO(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}var sO=cO;function lO(e){return this.__data__.get(e)}var fO=lO;function pO(e){return this.__data__.has(e)}var hO=pO,dO=Ba,vO=Ec,yO=jc,mO=200;function gO(e,t){var r=this.__data__;if(r instanceof dO){var n=r.__data__;if(!vO||n.length<mO-1)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new yO(n)}return r.set(e,t),this.size=r.size,this}var bO=gO,xO=Ba,wO=uO,OO=sO,AO=fO,SO=hO,PO=bO;function rn(e){var t=this.__data__=new xO(e);this.size=t.size}rn.prototype.clear=wO;rn.prototype.delete=OO;rn.prototype.get=AO;rn.prototype.has=SO;rn.prototype.set=PO;var bd=rn,_O="__lodash_hash_undefined__";function $O(e){return this.__data__.set(e,_O),this}var TO=$O;function EO(e){return this.__data__.has(e)}var jO=EO,MO=jc,CO=TO,IO=jO;function qi(e){var t=-1,r=e==null?0:e.length;for(this.__data__=new MO;++t<r;)this.add(e[t])}qi.prototype.add=qi.prototype.push=CO;qi.prototype.has=IO;var xd=qi;function kO(e,t){for(var r=-1,n=e==null?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1}var wd=kO;function DO(e,t){return e.has(t)}var Od=DO,NO=xd,RO=wd,LO=Od,BO=1,FO=2;function WO(e,t,r,n,i,a){var o=r&BO,u=e.length,c=t.length;if(u!=c&&!(o&&c>u))return!1;var s=a.get(e),f=a.get(t);if(s&&f)return s==t&&f==e;var l=-1,p=!0,h=r&FO?new NO:void 0;for(a.set(e,t),a.set(t,e);++l<u;){var v=e[l],d=t[l];if(n)var y=o?n(d,v,l,t,e,a):n(v,d,l,e,t,a);if(y!==void 0){if(y)continue;p=!1;break}if(h){if(!RO(t,function(b,w){if(!LO(h,w)&&(v===b||i(v,b,r,n,a)))return h.push(w)})){p=!1;break}}else if(!(v===d||i(v,d,r,n,a))){p=!1;break}}return a.delete(e),a.delete(t),p}var Ad=WO,zO=ft,UO=zO.Uint8Array,qO=UO;function HO(e){var t=-1,r=Array(e.size);return e.forEach(function(n,i){r[++t]=[i,n]}),r}var KO=HO;function GO(e){var t=-1,r=Array(e.size);return e.forEach(function(n){r[++t]=n}),r}var Wc=GO,nl=li,il=qO,VO=Tc,XO=Ad,YO=KO,ZO=Wc,JO=1,QO=2,e1="[object Boolean]",t1="[object Date]",r1="[object Error]",n1="[object Map]",i1="[object Number]",a1="[object RegExp]",o1="[object Set]",u1="[object String]",c1="[object Symbol]",s1="[object ArrayBuffer]",l1="[object DataView]",al=nl?nl.prototype:void 0,Eo=al?al.valueOf:void 0;function f1(e,t,r,n,i,a,o){switch(r){case l1:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case s1:return!(e.byteLength!=t.byteLength||!a(new il(e),new il(t)));case e1:case t1:case i1:return VO(+e,+t);case r1:return e.name==t.name&&e.message==t.message;case a1:case u1:return e==t+"";case n1:var u=YO;case o1:var c=n&JO;if(u||(u=ZO),e.size!=t.size&&!c)return!1;var s=o.get(e);if(s)return s==t;n|=QO,o.set(e,t);var f=XO(u(e),u(t),n,i,a,o);return o.delete(e),f;case c1:if(Eo)return Eo.call(e)==Eo.call(t)}return!1}var p1=f1;function h1(e,t){for(var r=-1,n=t.length,i=e.length;++r<n;)e[i+r]=t[r];return e}var Sd=h1,d1=Sd,v1=Le;function y1(e,t,r){var n=t(e);return v1(e)?n:d1(n,r(e))}var m1=y1;function g1(e,t){for(var r=-1,n=e==null?0:e.length,i=0,a=[];++r<n;){var o=e[r];t(o,r,e)&&(a[i++]=o)}return a}var b1=g1;function x1(){return[]}var w1=x1,O1=b1,A1=w1,S1=Object.prototype,P1=S1.propertyIsEnumerable,ol=Object.getOwnPropertySymbols,_1=ol?function(e){return e==null?[]:(e=Object(e),O1(ol(e),function(t){return P1.call(e,t)}))}:A1,$1=_1;function T1(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}var E1=T1,j1=Pt,M1=_t,C1="[object Arguments]";function I1(e){return M1(e)&&j1(e)==C1}var k1=I1,ul=k1,D1=_t,Pd=Object.prototype,N1=Pd.hasOwnProperty,R1=Pd.propertyIsEnumerable,L1=ul(function(){return arguments}())?ul:function(e){return D1(e)&&N1.call(e,"callee")&&!R1.call(e,"callee")},zc=L1,Hi={exports:{}};function B1(){return!1}var F1=B1;Hi.exports;(function(e,t){var r=ft,n=F1,i=t&&!t.nodeType&&t,a=i&&!0&&e&&!e.nodeType&&e,o=a&&a.exports===i,u=o?r.Buffer:void 0,c=u?u.isBuffer:void 0,s=c||n;e.exports=s})(Hi,Hi.exports);var _d=Hi.exports,W1=9007199254740991,z1=/^(?:0|[1-9]\d*)$/;function U1(e,t){var r=typeof e;return t=t??W1,!!t&&(r=="number"||r!="symbol"&&z1.test(e))&&e>-1&&e%1==0&&e<t}var Uc=U1,q1=9007199254740991;function H1(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=q1}var qc=H1,K1=Pt,G1=qc,V1=_t,X1="[object Arguments]",Y1="[object Array]",Z1="[object Boolean]",J1="[object Date]",Q1="[object Error]",eA="[object Function]",tA="[object Map]",rA="[object Number]",nA="[object Object]",iA="[object RegExp]",aA="[object Set]",oA="[object String]",uA="[object WeakMap]",cA="[object ArrayBuffer]",sA="[object DataView]",lA="[object Float32Array]",fA="[object Float64Array]",pA="[object Int8Array]",hA="[object Int16Array]",dA="[object Int32Array]",vA="[object Uint8Array]",yA="[object Uint8ClampedArray]",mA="[object Uint16Array]",gA="[object Uint32Array]",fe={};fe[lA]=fe[fA]=fe[pA]=fe[hA]=fe[dA]=fe[vA]=fe[yA]=fe[mA]=fe[gA]=!0;fe[X1]=fe[Y1]=fe[cA]=fe[Z1]=fe[sA]=fe[J1]=fe[Q1]=fe[eA]=fe[tA]=fe[rA]=fe[nA]=fe[iA]=fe[aA]=fe[oA]=fe[uA]=!1;function bA(e){return V1(e)&&G1(e.length)&&!!fe[K1(e)]}var xA=bA;function wA(e){return function(t){return e(t)}}var $d=wA,Ki={exports:{}};Ki.exports;(function(e,t){var r=Rh,n=t&&!t.nodeType&&t,i=n&&!0&&e&&!e.nodeType&&e,a=i&&i.exports===n,o=a&&r.process,u=function(){try{var c=i&&i.require&&i.require("util").types;return c||o&&o.binding&&o.binding("util")}catch{}}();e.exports=u})(Ki,Ki.exports);var OA=Ki.exports,AA=xA,SA=$d,cl=OA,sl=cl&&cl.isTypedArray,PA=sl?SA(sl):AA,Td=PA,_A=E1,$A=zc,TA=Le,EA=_d,jA=Uc,MA=Td,CA=Object.prototype,IA=CA.hasOwnProperty;function kA(e,t){var r=TA(e),n=!r&&$A(e),i=!r&&!n&&EA(e),a=!r&&!n&&!i&&MA(e),o=r||n||i||a,u=o?_A(e.length,String):[],c=u.length;for(var s in e)(t||IA.call(e,s))&&!(o&&(s=="length"||i&&(s=="offset"||s=="parent")||a&&(s=="buffer"||s=="byteLength"||s=="byteOffset")||jA(s,c)))&&u.push(s);return u}var DA=kA,NA=Object.prototype;function RA(e){var t=e&&e.constructor,r=typeof t=="function"&&t.prototype||NA;return e===r}var LA=RA;function BA(e,t){return function(r){return e(t(r))}}var Ed=BA,FA=Ed,WA=FA(Object.keys,Object),zA=WA,UA=LA,qA=zA,HA=Object.prototype,KA=HA.hasOwnProperty;function GA(e){if(!UA(e))return qA(e);var t=[];for(var r in Object(e))KA.call(e,r)&&r!="constructor"&&t.push(r);return t}var VA=GA,XA=$c,YA=qc;function ZA(e){return e!=null&&YA(e.length)&&!XA(e)}var pi=ZA,JA=DA,QA=VA,eS=pi;function tS(e){return eS(e)?JA(e):QA(e)}var Ka=tS,rS=m1,nS=$1,iS=Ka;function aS(e){return rS(e,iS,nS)}var oS=aS,ll=oS,uS=1,cS=Object.prototype,sS=cS.hasOwnProperty;function lS(e,t,r,n,i,a){var o=r&uS,u=ll(e),c=u.length,s=ll(t),f=s.length;if(c!=f&&!o)return!1;for(var l=c;l--;){var p=u[l];if(!(o?p in t:sS.call(t,p)))return!1}var h=a.get(e),v=a.get(t);if(h&&v)return h==t&&v==e;var d=!0;a.set(e,t),a.set(t,e);for(var y=o;++l<c;){p=u[l];var b=e[p],w=t[p];if(n)var x=o?n(w,b,p,t,e,a):n(b,w,p,e,t,a);if(!(x===void 0?b===w||i(b,w,r,n,a):x)){d=!1;break}y||(y=p=="constructor")}if(d&&!y){var A=e.constructor,m=t.constructor;A!=m&&"constructor"in e&&"constructor"in t&&!(typeof A=="function"&&A instanceof A&&typeof m=="function"&&m instanceof m)&&(d=!1)}return a.delete(e),a.delete(t),d}var fS=lS,pS=or,hS=ft,dS=pS(hS,"DataView"),vS=dS,yS=or,mS=ft,gS=yS(mS,"Promise"),bS=gS,xS=or,wS=ft,OS=xS(wS,"Set"),jd=OS,AS=or,SS=ft,PS=AS(SS,"WeakMap"),_S=PS,hu=vS,du=Ec,vu=bS,yu=jd,mu=_S,Md=Pt,nn=Bh,fl="[object Map]",$S="[object Object]",pl="[object Promise]",hl="[object Set]",dl="[object WeakMap]",vl="[object DataView]",TS=nn(hu),ES=nn(du),jS=nn(vu),MS=nn(yu),CS=nn(mu),Ht=Md;(hu&&Ht(new hu(new ArrayBuffer(1)))!=vl||du&&Ht(new du)!=fl||vu&&Ht(vu.resolve())!=pl||yu&&Ht(new yu)!=hl||mu&&Ht(new mu)!=dl)&&(Ht=function(e){var t=Md(e),r=t==$S?e.constructor:void 0,n=r?nn(r):"";if(n)switch(n){case TS:return vl;case ES:return fl;case jS:return pl;case MS:return hl;case CS:return dl}return t});var IS=Ht,jo=bd,kS=Ad,DS=p1,NS=fS,yl=IS,ml=Le,gl=_d,RS=Td,LS=1,bl="[object Arguments]",xl="[object Array]",Oi="[object Object]",BS=Object.prototype,wl=BS.hasOwnProperty;function FS(e,t,r,n,i,a){var o=ml(e),u=ml(t),c=o?xl:yl(e),s=u?xl:yl(t);c=c==bl?Oi:c,s=s==bl?Oi:s;var f=c==Oi,l=s==Oi,p=c==s;if(p&&gl(e)){if(!gl(t))return!1;o=!0,f=!1}if(p&&!f)return a||(a=new jo),o||RS(e)?kS(e,t,r,n,i,a):DS(e,t,c,r,n,i,a);if(!(r&LS)){var h=f&&wl.call(e,"__wrapped__"),v=l&&wl.call(t,"__wrapped__");if(h||v){var d=h?e.value():e,y=v?t.value():t;return a||(a=new jo),i(d,y,r,n,a)}}return p?(a||(a=new jo),NS(e,t,r,n,i,a)):!1}var WS=FS,zS=WS,Ol=_t;function Cd(e,t,r,n,i){return e===t?!0:e==null||t==null||!Ol(e)&&!Ol(t)?e!==e&&t!==t:zS(e,t,r,n,Cd,i)}var Hc=Cd,US=bd,qS=Hc,HS=1,KS=2;function GS(e,t,r,n){var i=r.length,a=i,o=!n;if(e==null)return!a;for(e=Object(e);i--;){var u=r[i];if(o&&u[2]?u[1]!==e[u[0]]:!(u[0]in e))return!1}for(;++i<a;){u=r[i];var c=u[0],s=e[c],f=u[1];if(o&&u[2]){if(s===void 0&&!(c in e))return!1}else{var l=new US;if(n)var p=n(s,f,c,e,t,l);if(!(p===void 0?qS(f,s,HS|KS,n,l):p))return!1}}return!0}var VS=GS,XS=It;function YS(e){return e===e&&!XS(e)}var Id=YS,ZS=Id,JS=Ka;function QS(e){for(var t=JS(e),r=t.length;r--;){var n=t[r],i=e[n];t[r]=[n,i,ZS(i)]}return t}var eP=QS;function tP(e,t){return function(r){return r==null?!1:r[e]===t&&(t!==void 0||e in Object(r))}}var kd=tP,rP=VS,nP=eP,iP=kd;function aP(e){var t=nP(e);return t.length==1&&t[0][2]?iP(t[0][0],t[0][1]):function(r){return r===e||rP(r,e,t)}}var oP=aP;function uP(e,t){return e!=null&&t in Object(e)}var cP=uP,sP=qh,lP=zc,fP=Le,pP=Uc,hP=qc,dP=Wa;function vP(e,t,r){t=sP(t,e);for(var n=-1,i=t.length,a=!1;++n<i;){var o=dP(t[n]);if(!(a=e!=null&&r(e,o)))break;e=e[o]}return a||++n!=i?a:(i=e==null?0:e.length,!!i&&hP(i)&&pP(o,i)&&(fP(e)||lP(e)))}var yP=vP,mP=cP,gP=yP;function bP(e,t){return e!=null&&gP(e,t,mP)}var xP=bP,wP=Hc,OP=Hh,AP=xP,SP=_c,PP=Id,_P=kd,$P=Wa,TP=1,EP=2;function jP(e,t){return SP(e)&&PP(t)?_P($P(e),t):function(r){var n=OP(r,e);return n===void 0&&n===t?AP(r,e):wP(t,n,TP|EP)}}var MP=jP;function CP(e){return e}var an=CP;function IP(e){return function(t){return t==null?void 0:t[e]}}var kP=IP,DP=Ic;function NP(e){return function(t){return DP(t,e)}}var RP=NP,LP=kP,BP=RP,FP=_c,WP=Wa;function zP(e){return FP(e)?LP(WP(e)):BP(e)}var UP=zP,qP=oP,HP=MP,KP=an,GP=Le,VP=UP;function XP(e){return typeof e=="function"?e:e==null?KP:typeof e=="object"?GP(e)?HP(e[0],e[1]):qP(e):VP(e)}var pt=XP;function YP(e,t,r,n){for(var i=e.length,a=r+(n?1:-1);n?a--:++a<i;)if(t(e[a],a,e))return a;return-1}var Dd=YP;function ZP(e){return e!==e}var JP=ZP;function QP(e,t,r){for(var n=r-1,i=e.length;++n<i;)if(e[n]===t)return n;return-1}var e_=QP,t_=Dd,r_=JP,n_=e_;function i_(e,t,r){return t===t?n_(e,t,r):t_(e,r_,r)}var a_=i_,o_=a_;function u_(e,t){var r=e==null?0:e.length;return!!r&&o_(e,t,0)>-1}var c_=u_;function s_(e,t,r){for(var n=-1,i=e==null?0:e.length;++n<i;)if(r(t,e[n]))return!0;return!1}var l_=s_;function f_(){}var p_=f_,Mo=jd,h_=p_,d_=Wc,v_=1/0,y_=Mo&&1/d_(new Mo([,-0]))[1]==v_?function(e){return new Mo(e)}:h_,m_=y_,g_=xd,b_=c_,x_=l_,w_=Od,O_=m_,A_=Wc,S_=200;function P_(e,t,r){var n=-1,i=b_,a=e.length,o=!0,u=[],c=u;if(r)o=!1,i=x_;else if(a>=S_){var s=t?null:O_(e);if(s)return A_(s);o=!1,i=w_,c=new g_}else c=t?[]:u;e:for(;++n<a;){var f=e[n],l=t?t(f):f;if(f=r||f!==0?f:0,o&&l===l){for(var p=c.length;p--;)if(c[p]===l)continue e;t&&c.push(l),u.push(f)}else i(c,l,r)||(c!==u&&c.push(l),u.push(f))}return u}var __=P_,$_=pt,T_=__;function E_(e,t){return e&&e.length?T_(e,$_(t)):[]}var j_=E_;const Al=ae(j_);function Nd(e,t,r){return t===!0?Al(e,r):X(t)?Al(e,t):e}function $r(e){"@babel/helpers - typeof";return $r=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$r(e)}var M_=["ref"];function Sl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ht(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Sl(Object(r),!0).forEach(function(n){Ga(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Sl(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function C_(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Pl(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Ld(n.key),n)}}function I_(e,t,r){return t&&Pl(e.prototype,t),r&&Pl(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function k_(e,t,r){return t=Gi(t),D_(e,Rd()?Reflect.construct(t,r||[],Gi(e).constructor):t.apply(e,r))}function D_(e,t){if(t&&($r(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return N_(e)}function N_(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Rd(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Rd=function(){return!!e})()}function Gi(e){return Gi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Gi(e)}function R_(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&gu(e,t)}function gu(e,t){return gu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},gu(e,t)}function Ga(e,t,r){return t=Ld(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ld(e){var t=L_(e,"string");return $r(t)=="symbol"?t:t+""}function L_(e,t){if($r(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if($r(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function B_(e,t){if(e==null)return{};var r=F_(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function F_(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function W_(e){return e.value}function z_(e,t){if(S.isValidElement(e))return S.cloneElement(e,t);if(typeof e=="function")return S.createElement(e,t);t.ref;var r=B_(t,M_);return S.createElement(Fc,r)}var _l=1,Or=function(e){function t(){var r;C_(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=k_(this,t,[].concat(i)),Ga(r,"lastBoundingBox",{width:-1,height:-1}),r}return R_(t,e),I_(t,[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();return n.height=this.wrapperNode.offsetHeight,n.width=this.wrapperNode.offsetWidth,n}return null}},{key:"updateBBox",value:function(){var n=this.props.onBBoxUpdate,i=this.getBBox();i?(Math.abs(i.width-this.lastBoundingBox.width)>_l||Math.abs(i.height-this.lastBoundingBox.height)>_l)&&(this.lastBoundingBox.width=i.width,this.lastBoundingBox.height=i.height,n&&n(i)):(this.lastBoundingBox.width!==-1||this.lastBoundingBox.height!==-1)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,n&&n(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?ht({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(n){var i=this.props,a=i.layout,o=i.align,u=i.verticalAlign,c=i.margin,s=i.chartWidth,f=i.chartHeight,l,p;if(!n||(n.left===void 0||n.left===null)&&(n.right===void 0||n.right===null))if(o==="center"&&a==="vertical"){var h=this.getBBoxSnapshot();l={left:((s||0)-h.width)/2}}else l=o==="right"?{right:c&&c.right||0}:{left:c&&c.left||0};if(!n||(n.top===void 0||n.top===null)&&(n.bottom===void 0||n.bottom===null))if(u==="middle"){var v=this.getBBoxSnapshot();p={top:((f||0)-v.height)/2}}else p=u==="bottom"?{bottom:c&&c.bottom||0}:{top:c&&c.top||0};return ht(ht({},l),p)}},{key:"render",value:function(){var n=this,i=this.props,a=i.content,o=i.width,u=i.height,c=i.wrapperStyle,s=i.payloadUniqBy,f=i.payload,l=ht(ht({position:"absolute",width:o||"auto",height:u||"auto"},this.getDefaultPosition(c)),c);return S.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(h){n.wrapperNode=h}},z_(a,ht(ht({},this.props),{},{payload:Nd(f,s,W_)})))}}],[{key:"getWithHeight",value:function(n,i){var a=ht(ht({},this.defaultProps),n.props),o=a.layout;return o==="vertical"&&L(n.props.height)?{height:n.props.height}:o==="horizontal"?{width:n.props.width||i}:null}}])}(R.PureComponent);Ga(Or,"displayName","Legend");Ga(Or,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var $l=li,U_=zc,q_=Le,Tl=$l?$l.isConcatSpreadable:void 0;function H_(e){return q_(e)||U_(e)||!!(Tl&&e&&e[Tl])}var K_=H_,G_=Sd,V_=K_;function Bd(e,t,r,n,i){var a=-1,o=e.length;for(r||(r=V_),i||(i=[]);++a<o;){var u=e[a];t>0&&r(u)?t>1?Bd(u,t-1,r,n,i):G_(i,u):n||(i[i.length]=u)}return i}var Fd=Bd;function X_(e){return function(t,r,n){for(var i=-1,a=Object(t),o=n(t),u=o.length;u--;){var c=o[e?u:++i];if(r(a[c],c,a)===!1)break}return t}}var Y_=X_,Z_=Y_,J_=Z_(),Q_=J_,e$=Q_,t$=Ka;function r$(e,t){return e&&e$(e,t,t$)}var Wd=r$,n$=pi;function i$(e,t){return function(r,n){if(r==null)return r;if(!n$(r))return e(r,n);for(var i=r.length,a=t?i:-1,o=Object(r);(t?a--:++a<i)&&n(o[a],a,o)!==!1;);return r}}var a$=i$,o$=Wd,u$=a$,c$=u$(o$),Kc=c$,s$=Kc,l$=pi;function f$(e,t){var r=-1,n=l$(e)?Array(e.length):[];return s$(e,function(i,a,o){n[++r]=t(i,a,o)}),n}var zd=f$;function p$(e,t){var r=e.length;for(e.sort(t);r--;)e[r]=e[r].value;return e}var h$=p$,El=Yr;function d$(e,t){if(e!==t){var r=e!==void 0,n=e===null,i=e===e,a=El(e),o=t!==void 0,u=t===null,c=t===t,s=El(t);if(!u&&!s&&!a&&e>t||a&&o&&c&&!u&&!s||n&&o&&c||!r&&c||!i)return 1;if(!n&&!a&&!s&&e<t||s&&r&&i&&!n&&!a||u&&r&&i||!o&&i||!c)return-1}return 0}var v$=d$,y$=v$;function m$(e,t,r){for(var n=-1,i=e.criteria,a=t.criteria,o=i.length,u=r.length;++n<o;){var c=y$(i[n],a[n]);if(c){if(n>=u)return c;var s=r[n];return c*(s=="desc"?-1:1)}}return e.index-t.index}var g$=m$,Co=Cc,b$=Ic,x$=pt,w$=zd,O$=h$,A$=$d,S$=g$,P$=an,_$=Le;function $$(e,t,r){t.length?t=Co(t,function(a){return _$(a)?function(o){return b$(o,a.length===1?a[0]:a)}:a}):t=[P$];var n=-1;t=Co(t,A$(x$));var i=w$(e,function(a,o,u){var c=Co(t,function(s){return s(a)});return{criteria:c,index:++n,value:a}});return O$(i,function(a,o){return S$(a,o,r)})}var T$=$$;function E$(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}var j$=E$,M$=j$,jl=Math.max;function C$(e,t,r){return t=jl(t===void 0?e.length-1:t,0),function(){for(var n=arguments,i=-1,a=jl(n.length-t,0),o=Array(a);++i<a;)o[i]=n[t+i];i=-1;for(var u=Array(t+1);++i<t;)u[i]=n[i];return u[t]=r(o),M$(e,this,u)}}var I$=C$;function k$(e){return function(){return e}}var D$=k$,N$=or,R$=function(){try{var e=N$(Object,"defineProperty");return e({},"",{}),e}catch{}}(),Ud=R$,L$=D$,Ml=Ud,B$=an,F$=Ml?function(e,t){return Ml(e,"toString",{configurable:!0,enumerable:!1,value:L$(t),writable:!0})}:B$,W$=F$,z$=800,U$=16,q$=Date.now;function H$(e){var t=0,r=0;return function(){var n=q$(),i=U$-(n-r);if(r=n,i>0){if(++t>=z$)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}var K$=H$,G$=W$,V$=K$,X$=V$(G$),Y$=X$,Z$=an,J$=I$,Q$=Y$;function eT(e,t){return Q$(J$(e,t,Z$),e+"")}var tT=eT,rT=Tc,nT=pi,iT=Uc,aT=It;function oT(e,t,r){if(!aT(r))return!1;var n=typeof t;return(n=="number"?nT(r)&&iT(t,r.length):n=="string"&&t in r)?rT(r[t],e):!1}var Va=oT,uT=Fd,cT=T$,sT=tT,Cl=Va,lT=sT(function(e,t){if(e==null)return[];var r=t.length;return r>1&&Cl(e,t[0],t[1])?t=[]:r>2&&Cl(t[0],t[1],t[2])&&(t=[t[0]]),cT(e,uT(t,1),[])}),fT=lT;const Gc=ae(fT);function Mn(e){"@babel/helpers - typeof";return Mn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mn(e)}function bu(){return bu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},bu.apply(this,arguments)}function pT(e,t){return yT(e)||vT(e,t)||dT(e,t)||hT()}function hT(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function dT(e,t){if(e){if(typeof e=="string")return Il(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Il(e,t)}}function Il(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function vT(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function yT(e){if(Array.isArray(e))return e}function kl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Io(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?kl(Object(r),!0).forEach(function(n){mT(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):kl(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function mT(e,t,r){return t=gT(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function gT(e){var t=bT(e,"string");return Mn(t)=="symbol"?t:t+""}function bT(e,t){if(Mn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Mn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function xT(e){return Array.isArray(e)&&Se(e[0])&&Se(e[1])?e.join(" ~ "):e}var wT=function(t){var r=t.separator,n=r===void 0?" : ":r,i=t.contentStyle,a=i===void 0?{}:i,o=t.itemStyle,u=o===void 0?{}:o,c=t.labelStyle,s=c===void 0?{}:c,f=t.payload,l=t.formatter,p=t.itemSorter,h=t.wrapperClassName,v=t.labelClassName,d=t.label,y=t.labelFormatter,b=t.accessibilityLayer,w=b===void 0?!1:b,x=function(){if(f&&f.length){var T={padding:0,margin:0},C=(p?Gc(f,p):f).map(function(I,M){if(I.type==="none")return null;var k=Io({display:"block",paddingTop:4,paddingBottom:4,color:I.color||"#000"},u),D=I.formatter||l||xT,B=I.value,F=I.name,q=B,G=F;if(D&&q!=null&&G!=null){var z=D(B,F,I,M,f);if(Array.isArray(z)){var V=pT(z,2);q=V[0],G=V[1]}else q=z}return S.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(M),style:k},Se(G)?S.createElement("span",{className:"recharts-tooltip-item-name"},G):null,Se(G)?S.createElement("span",{className:"recharts-tooltip-item-separator"},n):null,S.createElement("span",{className:"recharts-tooltip-item-value"},q),S.createElement("span",{className:"recharts-tooltip-item-unit"},I.unit||""))});return S.createElement("ul",{className:"recharts-tooltip-item-list",style:T},C)}return null},A=Io({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},a),m=Io({margin:0},s),g=!Y(d),O=g?d:"",P=J("recharts-default-tooltip",h),_=J("recharts-tooltip-label",v);g&&y&&f!==void 0&&f!==null&&(O=y(d,f));var E=w?{role:"status","aria-live":"assertive"}:{};return S.createElement("div",bu({className:P,style:A},E),S.createElement("p",{className:_,style:m},S.isValidElement(O)?O:"".concat(O)),x())};function Cn(e){"@babel/helpers - typeof";return Cn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Cn(e)}function Ai(e,t,r){return t=OT(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function OT(e){var t=AT(e,"string");return Cn(t)=="symbol"?t:t+""}function AT(e,t){if(Cn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Cn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var pn="recharts-tooltip-wrapper",ST={visibility:"hidden"};function PT(e){var t=e.coordinate,r=e.translateX,n=e.translateY;return J(pn,Ai(Ai(Ai(Ai({},"".concat(pn,"-right"),L(r)&&t&&L(t.x)&&r>=t.x),"".concat(pn,"-left"),L(r)&&t&&L(t.x)&&r<t.x),"".concat(pn,"-bottom"),L(n)&&t&&L(t.y)&&n>=t.y),"".concat(pn,"-top"),L(n)&&t&&L(t.y)&&n<t.y))}function Dl(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,i=e.offsetTopLeft,a=e.position,o=e.reverseDirection,u=e.tooltipDimension,c=e.viewBox,s=e.viewBoxDimension;if(a&&L(a[n]))return a[n];var f=r[n]-u-i,l=r[n]+i;if(t[n])return o[n]?f:l;if(o[n]){var p=f,h=c[n];return p<h?Math.max(l,c[n]):Math.max(f,c[n])}var v=l+u,d=c[n]+s;return v>d?Math.max(f,c[n]):Math.max(l,c[n])}function _T(e){var t=e.translateX,r=e.translateY,n=e.useTranslate3d;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}function $T(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.offsetTopLeft,i=e.position,a=e.reverseDirection,o=e.tooltipBox,u=e.useTranslate3d,c=e.viewBox,s,f,l;return o.height>0&&o.width>0&&r?(f=Dl({allowEscapeViewBox:t,coordinate:r,key:"x",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.width,viewBox:c,viewBoxDimension:c.width}),l=Dl({allowEscapeViewBox:t,coordinate:r,key:"y",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.height,viewBox:c,viewBoxDimension:c.height}),s=_T({translateX:f,translateY:l,useTranslate3d:u})):s=ST,{cssProperties:s,cssClasses:PT({translateX:f,translateY:l,coordinate:r})}}function Tr(e){"@babel/helpers - typeof";return Tr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Tr(e)}function Nl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Rl(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Nl(Object(r),!0).forEach(function(n){wu(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Nl(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function TT(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ET(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Hd(n.key),n)}}function jT(e,t,r){return t&&ET(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function MT(e,t,r){return t=Vi(t),CT(e,qd()?Reflect.construct(t,r||[],Vi(e).constructor):t.apply(e,r))}function CT(e,t){if(t&&(Tr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return IT(e)}function IT(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function qd(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(qd=function(){return!!e})()}function Vi(e){return Vi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Vi(e)}function kT(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&xu(e,t)}function xu(e,t){return xu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},xu(e,t)}function wu(e,t,r){return t=Hd(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Hd(e){var t=DT(e,"string");return Tr(t)=="symbol"?t:t+""}function DT(e,t){if(Tr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Tr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Ll=1,NT=function(e){function t(){var r;TT(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=MT(this,t,[].concat(i)),wu(r,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),wu(r,"handleKeyDown",function(o){if(o.key==="Escape"){var u,c,s,f;r.setState({dismissed:!0,dismissedAtCoordinate:{x:(u=(c=r.props.coordinate)===null||c===void 0?void 0:c.x)!==null&&u!==void 0?u:0,y:(s=(f=r.props.coordinate)===null||f===void 0?void 0:f.y)!==null&&s!==void 0?s:0}})}}),r}return kT(t,e),jT(t,[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();(Math.abs(n.width-this.state.lastBoundingBox.width)>Ll||Math.abs(n.height-this.state.lastBoundingBox.height)>Ll)&&this.setState({lastBoundingBox:{width:n.width,height:n.height}})}else(this.state.lastBoundingBox.width!==-1||this.state.lastBoundingBox.height!==-1)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var n,i;this.props.active&&this.updateBBox(),this.state.dismissed&&(((n=this.props.coordinate)===null||n===void 0?void 0:n.x)!==this.state.dismissedAtCoordinate.x||((i=this.props.coordinate)===null||i===void 0?void 0:i.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,s=i.children,f=i.coordinate,l=i.hasPayload,p=i.isAnimationActive,h=i.offset,v=i.position,d=i.reverseDirection,y=i.useTranslate3d,b=i.viewBox,w=i.wrapperStyle,x=$T({allowEscapeViewBox:o,coordinate:f,offsetTopLeft:h,position:v,reverseDirection:d,tooltipBox:this.state.lastBoundingBox,useTranslate3d:y,viewBox:b}),A=x.cssClasses,m=x.cssProperties,g=Rl(Rl({transition:p&&a?"transform ".concat(u,"ms ").concat(c):void 0},m),{},{pointerEvents:"none",visibility:!this.state.dismissed&&a&&l?"visible":"hidden",position:"absolute",top:0,left:0},w);return S.createElement("div",{tabIndex:-1,className:A,style:g,ref:function(P){n.wrapperNode=P}},s)}}])}(R.PureComponent),RT=function(){return!(typeof window<"u"&&window.document&&window.document.createElement&&window.setTimeout)},ur={isSsr:RT()};function Er(e){"@babel/helpers - typeof";return Er=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Er(e)}function Bl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Fl(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Bl(Object(r),!0).forEach(function(n){Vc(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Bl(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function LT(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function BT(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Gd(n.key),n)}}function FT(e,t,r){return t&&BT(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function WT(e,t,r){return t=Xi(t),zT(e,Kd()?Reflect.construct(t,r||[],Xi(e).constructor):t.apply(e,r))}function zT(e,t){if(t&&(Er(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return UT(e)}function UT(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Kd(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Kd=function(){return!!e})()}function Xi(e){return Xi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Xi(e)}function qT(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ou(e,t)}function Ou(e,t){return Ou=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ou(e,t)}function Vc(e,t,r){return t=Gd(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Gd(e){var t=HT(e,"string");return Er(t)=="symbol"?t:t+""}function HT(e,t){if(Er(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Er(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function KT(e){return e.dataKey}function GT(e,t){return S.isValidElement(e)?S.cloneElement(e,t):typeof e=="function"?S.createElement(e,t):S.createElement(wT,t)}var dt=function(e){function t(){return LT(this,t),WT(this,t,arguments)}return qT(t,e),FT(t,[{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,s=i.content,f=i.coordinate,l=i.filterNull,p=i.isAnimationActive,h=i.offset,v=i.payload,d=i.payloadUniqBy,y=i.position,b=i.reverseDirection,w=i.useTranslate3d,x=i.viewBox,A=i.wrapperStyle,m=v??[];l&&m.length&&(m=Nd(v.filter(function(O){return O.value!=null&&(O.hide!==!0||n.props.includeHidden)}),d,KT));var g=m.length>0;return S.createElement(NT,{allowEscapeViewBox:o,animationDuration:u,animationEasing:c,isAnimationActive:p,active:a,coordinate:f,hasPayload:g,offset:h,position:y,reverseDirection:b,useTranslate3d:w,viewBox:x,wrapperStyle:A},GT(s,Fl(Fl({},this.props),{},{payload:m})))}}])}(R.PureComponent);Vc(dt,"displayName","Tooltip");Vc(dt,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!ur.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var VT=ft,XT=function(){return VT.Date.now()},YT=XT,ZT=/\s/;function JT(e){for(var t=e.length;t--&&ZT.test(e.charAt(t)););return t}var QT=JT,eE=QT,tE=/^\s+/;function rE(e){return e&&e.slice(0,eE(e)+1).replace(tE,"")}var nE=rE,iE=nE,Wl=It,aE=Yr,zl=NaN,oE=/^[-+]0x[0-9a-f]+$/i,uE=/^0b[01]+$/i,cE=/^0o[0-7]+$/i,sE=parseInt;function lE(e){if(typeof e=="number")return e;if(aE(e))return zl;if(Wl(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=Wl(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=iE(e);var r=uE.test(e);return r||cE.test(e)?sE(e.slice(2),r?2:8):oE.test(e)?zl:+e}var Vd=lE,fE=It,ko=YT,Ul=Vd,pE="Expected a function",hE=Math.max,dE=Math.min;function vE(e,t,r){var n,i,a,o,u,c,s=0,f=!1,l=!1,p=!0;if(typeof e!="function")throw new TypeError(pE);t=Ul(t)||0,fE(r)&&(f=!!r.leading,l="maxWait"in r,a=l?hE(Ul(r.maxWait)||0,t):a,p="trailing"in r?!!r.trailing:p);function h(g){var O=n,P=i;return n=i=void 0,s=g,o=e.apply(P,O),o}function v(g){return s=g,u=setTimeout(b,t),f?h(g):o}function d(g){var O=g-c,P=g-s,_=t-O;return l?dE(_,a-P):_}function y(g){var O=g-c,P=g-s;return c===void 0||O>=t||O<0||l&&P>=a}function b(){var g=ko();if(y(g))return w(g);u=setTimeout(b,d(g))}function w(g){return u=void 0,p&&n?h(g):(n=i=void 0,o)}function x(){u!==void 0&&clearTimeout(u),s=0,n=c=i=u=void 0}function A(){return u===void 0?o:w(ko())}function m(){var g=ko(),O=y(g);if(n=arguments,i=this,c=g,O){if(u===void 0)return v(c);if(l)return clearTimeout(u),u=setTimeout(b,t),h(c)}return u===void 0&&(u=setTimeout(b,t)),o}return m.cancel=x,m.flush=A,m}var yE=vE,mE=yE,gE=It,bE="Expected a function";function xE(e,t,r){var n=!0,i=!0;if(typeof e!="function")throw new TypeError(bE);return gE(r)&&(n="leading"in r?!!r.leading:n,i="trailing"in r?!!r.trailing:i),mE(e,t,{leading:n,maxWait:t,trailing:i})}var wE=xE;const Xd=ae(wE);function In(e){"@babel/helpers - typeof";return In=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},In(e)}function ql(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Si(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ql(Object(r),!0).forEach(function(n){OE(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ql(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function OE(e,t,r){return t=AE(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function AE(e){var t=SE(e,"string");return In(t)=="symbol"?t:t+""}function SE(e,t){if(In(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(In(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function PE(e,t){return EE(e)||TE(e,t)||$E(e,t)||_E()}function _E(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function $E(e,t){if(e){if(typeof e=="string")return Hl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Hl(e,t)}}function Hl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function TE(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function EE(e){if(Array.isArray(e))return e}var JW=R.forwardRef(function(e,t){var r=e.aspect,n=e.initialDimension,i=n===void 0?{width:-1,height:-1}:n,a=e.width,o=a===void 0?"100%":a,u=e.height,c=u===void 0?"100%":u,s=e.minWidth,f=s===void 0?0:s,l=e.minHeight,p=e.maxHeight,h=e.children,v=e.debounce,d=v===void 0?0:v,y=e.id,b=e.className,w=e.onResize,x=e.style,A=x===void 0?{}:x,m=R.useRef(null),g=R.useRef();g.current=w,R.useImperativeHandle(t,function(){return Object.defineProperty(m.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),m.current},configurable:!0})});var O=R.useState({containerWidth:i.width,containerHeight:i.height}),P=PE(O,2),_=P[0],E=P[1],$=R.useCallback(function(C,I){E(function(M){var k=Math.round(C),D=Math.round(I);return M.containerWidth===k&&M.containerHeight===D?M:{containerWidth:k,containerHeight:D}})},[]);R.useEffect(function(){var C=function(F){var q,G=F[0].contentRect,z=G.width,V=G.height;$(z,V),(q=g.current)===null||q===void 0||q.call(g,z,V)};d>0&&(C=Xd(C,d,{trailing:!0,leading:!1}));var I=new ResizeObserver(C),M=m.current.getBoundingClientRect(),k=M.width,D=M.height;return $(k,D),I.observe(m.current),function(){I.disconnect()}},[$,d]);var T=R.useMemo(function(){var C=_.containerWidth,I=_.containerHeight;if(C<0||I<0)return null;it(Vt(o)||Vt(c),`The width(%s) and height(%s) are both fixed numbers,
       maybe you don't need to use a ResponsiveContainer.`,o,c),it(!r||r>0,"The aspect(%s) must be greater than zero.",r);var M=Vt(o)?C:o,k=Vt(c)?I:c;r&&r>0&&(M?k=M/r:k&&(M=k*r),p&&k>p&&(k=p)),it(M>0||k>0,`The width(%s) and height(%s) of chart should be greater than 0,
       please check the style of container, or the props width(%s) and height(%s),
       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the
       height and width.`,M,k,o,c,f,l,r);var D=!Array.isArray(h)&&bt(h.type).endsWith("Chart");return S.Children.map(h,function(B){return S.isValidElement(B)?R.cloneElement(B,Si({width:M,height:k},D?{style:Si({height:"100%",width:"100%",maxHeight:k,maxWidth:M},B.props.style)}:{})):B})},[r,h,c,p,l,f,_,o]);return S.createElement("div",{id:y?"".concat(y):void 0,className:J("recharts-responsive-container",b),style:Si(Si({},A),{},{width:o,height:c,minWidth:f,minHeight:l,maxHeight:p}),ref:m},T)}),Xc=function(t){return null};Xc.displayName="Cell";function kn(e){"@babel/helpers - typeof";return kn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kn(e)}function Kl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Au(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Kl(Object(r),!0).forEach(function(n){jE(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Kl(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function jE(e,t,r){return t=ME(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ME(e){var t=CE(e,"string");return kn(t)=="symbol"?t:t+""}function CE(e,t){if(kn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(kn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var pr={widthCache:{},cacheCount:0},IE=2e3,kE={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},Gl="recharts_measurement_span";function DE(e){var t=Au({},e);return Object.keys(t).forEach(function(r){t[r]||delete t[r]}),t}var On=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t==null||ur.isSsr)return{width:0,height:0};var n=DE(r),i=JSON.stringify({text:t,copyStyle:n});if(pr.widthCache[i])return pr.widthCache[i];try{var a=document.getElementById(Gl);a||(a=document.createElement("span"),a.setAttribute("id",Gl),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=Au(Au({},kE),n);Object.assign(a.style,o),a.textContent="".concat(t);var u=a.getBoundingClientRect(),c={width:u.width,height:u.height};return pr.widthCache[i]=c,++pr.cacheCount>IE&&(pr.cacheCount=0,pr.widthCache={}),c}catch{return{width:0,height:0}}},NE=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}};function Dn(e){"@babel/helpers - typeof";return Dn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dn(e)}function Yi(e,t){return FE(e)||BE(e,t)||LE(e,t)||RE()}function RE(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function LE(e,t){if(e){if(typeof e=="string")return Vl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Vl(e,t)}}function Vl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function BE(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function FE(e){if(Array.isArray(e))return e}function WE(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Xl(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,UE(n.key),n)}}function zE(e,t,r){return t&&Xl(e.prototype,t),r&&Xl(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function UE(e){var t=qE(e,"string");return Dn(t)=="symbol"?t:t+""}function qE(e,t){if(Dn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Dn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Yl=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,Zl=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,HE=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,KE=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,Yd={cm:96/2.54,mm:96/25.4,pt:96/72,pc:96/6,in:96,Q:96/(2.54*40),px:1},GE=Object.keys(Yd),yr="NaN";function VE(e,t){return e*Yd[t]}var Pi=function(){function e(t,r){WE(this,e),this.num=t,this.unit=r,this.num=t,this.unit=r,Number.isNaN(t)&&(this.unit=""),r!==""&&!HE.test(r)&&(this.num=NaN,this.unit=""),GE.includes(r)&&(this.num=VE(t,r),this.unit="px")}return zE(e,[{key:"add",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num+r.num,this.unit)}},{key:"subtract",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num-r.num,this.unit)}},{key:"multiply",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num*r.num,this.unit||r.unit)}},{key:"divide",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num/r.num,this.unit||r.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],[{key:"parse",value:function(r){var n,i=(n=KE.exec(r))!==null&&n!==void 0?n:[],a=Yi(i,3),o=a[1],u=a[2];return new e(parseFloat(o),u??"")}}])}();function Zd(e){if(e.includes(yr))return yr;for(var t=e;t.includes("*")||t.includes("/");){var r,n=(r=Yl.exec(t))!==null&&r!==void 0?r:[],i=Yi(n,4),a=i[1],o=i[2],u=i[3],c=Pi.parse(a??""),s=Pi.parse(u??""),f=o==="*"?c.multiply(s):c.divide(s);if(f.isNaN())return yr;t=t.replace(Yl,f.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var l,p=(l=Zl.exec(t))!==null&&l!==void 0?l:[],h=Yi(p,4),v=h[1],d=h[2],y=h[3],b=Pi.parse(v??""),w=Pi.parse(y??""),x=d==="+"?b.add(w):b.subtract(w);if(x.isNaN())return yr;t=t.replace(Zl,x.toString())}return t}var Jl=/\(([^()]*)\)/;function XE(e){for(var t=e;t.includes("(");){var r=Jl.exec(t),n=Yi(r,2),i=n[1];t=t.replace(Jl,Zd(i))}return t}function YE(e){var t=e.replace(/\s+/g,"");return t=XE(t),t=Zd(t),t}function ZE(e){try{return YE(e)}catch{return yr}}function Do(e){var t=ZE(e.slice(5,-1));return t===yr?"":t}var JE=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],QE=["dx","dy","angle","className","breakAll"];function Su(){return Su=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Su.apply(this,arguments)}function Ql(e,t){if(e==null)return{};var r=ej(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function ej(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function ef(e,t){return ij(e)||nj(e,t)||rj(e,t)||tj()}function tj(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function rj(e,t){if(e){if(typeof e=="string")return tf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tf(e,t)}}function tf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function nj(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function ij(e){if(Array.isArray(e))return e}var Jd=/[ \f\n\r\t\v\u2028\u2029]+/,Qd=function(t){var r=t.children,n=t.breakAll,i=t.style;try{var a=[];Y(r)||(n?a=r.toString().split(""):a=r.toString().split(Jd));var o=a.map(function(c){return{word:c,width:On(c,i).width}}),u=n?0:On(" ",i).width;return{wordsWithComputedWidth:o,spaceWidth:u}}catch{return null}},aj=function(t,r,n,i,a){var o=t.maxLines,u=t.children,c=t.style,s=t.breakAll,f=L(o),l=u,p=function(){var M=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return M.reduce(function(k,D){var B=D.word,F=D.width,q=k[k.length-1];if(q&&(i==null||a||q.width+F+n<Number(i)))q.words.push(B),q.width+=F+n;else{var G={words:[B],width:F};k.push(G)}return k},[])},h=p(r),v=function(M){return M.reduce(function(k,D){return k.width>D.width?k:D})};if(!f)return h;for(var d="…",y=function(M){var k=l.slice(0,M),D=Qd({breakAll:s,style:c,children:k+d}).wordsWithComputedWidth,B=p(D),F=B.length>o||v(B).width>Number(i);return[F,B]},b=0,w=l.length-1,x=0,A;b<=w&&x<=l.length-1;){var m=Math.floor((b+w)/2),g=m-1,O=y(g),P=ef(O,2),_=P[0],E=P[1],$=y(m),T=ef($,1),C=T[0];if(!_&&!C&&(b=m+1),_&&C&&(w=m-1),!_&&C){A=E;break}x++}return A||h},rf=function(t){var r=Y(t)?[]:t.toString().split(Jd);return[{words:r}]},oj=function(t){var r=t.width,n=t.scaleToFit,i=t.children,a=t.style,o=t.breakAll,u=t.maxLines;if((r||n)&&!ur.isSsr){var c,s,f=Qd({breakAll:o,children:i,style:a});if(f){var l=f.wordsWithComputedWidth,p=f.spaceWidth;c=l,s=p}else return rf(i);return aj({breakAll:o,children:i,maxLines:u,style:a},c,s,r,n)}return rf(i)},nf="#808080",rr=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.lineHeight,u=o===void 0?"1em":o,c=t.capHeight,s=c===void 0?"0.71em":c,f=t.scaleToFit,l=f===void 0?!1:f,p=t.textAnchor,h=p===void 0?"start":p,v=t.verticalAnchor,d=v===void 0?"end":v,y=t.fill,b=y===void 0?nf:y,w=Ql(t,JE),x=R.useMemo(function(){return oj({breakAll:w.breakAll,children:w.children,maxLines:w.maxLines,scaleToFit:l,style:w.style,width:w.width})},[w.breakAll,w.children,w.maxLines,l,w.style,w.width]),A=w.dx,m=w.dy,g=w.angle,O=w.className,P=w.breakAll,_=Ql(w,QE);if(!Se(n)||!Se(a))return null;var E=n+(L(A)?A:0),$=a+(L(m)?m:0),T;switch(d){case"start":T=Do("calc(".concat(s,")"));break;case"middle":T=Do("calc(".concat((x.length-1)/2," * -").concat(u," + (").concat(s," / 2))"));break;default:T=Do("calc(".concat(x.length-1," * -").concat(u,")"));break}var C=[];if(l){var I=x[0].width,M=w.width;C.push("scale(".concat((L(M)?M/I:1)/I,")"))}return g&&C.push("rotate(".concat(g,", ").concat(E,", ").concat($,")")),C.length&&(_.transform=C.join(" ")),S.createElement("text",Su({},H(_,!0),{x:E,y:$,className:J("recharts-text",O),textAnchor:h,fill:b.includes("url")?nf:b}),x.map(function(k,D){var B=k.words.join(P?"":" ");return S.createElement("tspan",{x:E,dy:D===0?T:u,key:"".concat(B,"-").concat(D)},B)}))};function Ct(e,t){return e==null||t==null?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function uj(e,t){return e==null||t==null?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function Yc(e){let t,r,n;e.length!==2?(t=Ct,r=(u,c)=>Ct(e(u),c),n=(u,c)=>e(u)-c):(t=e===Ct||e===uj?e:cj,r=e,n=e);function i(u,c,s=0,f=u.length){if(s<f){if(t(c,c)!==0)return f;do{const l=s+f>>>1;r(u[l],c)<0?s=l+1:f=l}while(s<f)}return s}function a(u,c,s=0,f=u.length){if(s<f){if(t(c,c)!==0)return f;do{const l=s+f>>>1;r(u[l],c)<=0?s=l+1:f=l}while(s<f)}return s}function o(u,c,s=0,f=u.length){const l=i(u,c,s,f-1);return l>s&&n(u[l-1],c)>-n(u[l],c)?l-1:l}return{left:i,center:o,right:a}}function cj(){return 0}function ev(e){return e===null?NaN:+e}function*sj(e,t){for(let r of e)r!=null&&(r=+r)>=r&&(yield r)}const lj=Yc(Ct),hi=lj.right;Yc(ev).center;class af extends Map{constructor(t,r=hj){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:r}}),t!=null)for(const[n,i]of t)this.set(n,i)}get(t){return super.get(of(this,t))}has(t){return super.has(of(this,t))}set(t,r){return super.set(fj(this,t),r)}delete(t){return super.delete(pj(this,t))}}function of({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):r}function fj({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}function pj({_intern:e,_key:t},r){const n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}function hj(e){return e!==null&&typeof e=="object"?e.valueOf():e}function dj(e=Ct){if(e===Ct)return tv;if(typeof e!="function")throw new TypeError("compare is not a function");return(t,r)=>{const n=e(t,r);return n||n===0?n:(e(r,r)===0)-(e(t,t)===0)}}function tv(e,t){return(e==null||!(e>=e))-(t==null||!(t>=t))||(e<t?-1:e>t?1:0)}const vj=Math.sqrt(50),yj=Math.sqrt(10),mj=Math.sqrt(2);function Zi(e,t,r){const n=(t-e)/Math.max(0,r),i=Math.floor(Math.log10(n)),a=n/Math.pow(10,i),o=a>=vj?10:a>=yj?5:a>=mj?2:1;let u,c,s;return i<0?(s=Math.pow(10,-i)/o,u=Math.round(e*s),c=Math.round(t*s),u/s<e&&++u,c/s>t&&--c,s=-s):(s=Math.pow(10,i)*o,u=Math.round(e/s),c=Math.round(t/s),u*s<e&&++u,c*s>t&&--c),c<u&&.5<=r&&r<2?Zi(e,t,r*2):[u,c,s]}function Pu(e,t,r){if(t=+t,e=+e,r=+r,!(r>0))return[];if(e===t)return[e];const n=t<e,[i,a,o]=n?Zi(t,e,r):Zi(e,t,r);if(!(a>=i))return[];const u=a-i+1,c=new Array(u);if(n)if(o<0)for(let s=0;s<u;++s)c[s]=(a-s)/-o;else for(let s=0;s<u;++s)c[s]=(a-s)*o;else if(o<0)for(let s=0;s<u;++s)c[s]=(i+s)/-o;else for(let s=0;s<u;++s)c[s]=(i+s)*o;return c}function _u(e,t,r){return t=+t,e=+e,r=+r,Zi(e,t,r)[2]}function $u(e,t,r){t=+t,e=+e,r=+r;const n=t<e,i=n?_u(t,e,r):_u(e,t,r);return(n?-1:1)*(i<0?1/-i:i)}function uf(e,t){let r;for(const n of e)n!=null&&(r<n||r===void 0&&n>=n)&&(r=n);return r}function cf(e,t){let r;for(const n of e)n!=null&&(r>n||r===void 0&&n>=n)&&(r=n);return r}function rv(e,t,r=0,n=1/0,i){if(t=Math.floor(t),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(e.length-1,n)),!(r<=t&&t<=n))return e;for(i=i===void 0?tv:dj(i);n>r;){if(n-r>600){const c=n-r+1,s=t-r+1,f=Math.log(c),l=.5*Math.exp(2*f/3),p=.5*Math.sqrt(f*l*(c-l)/c)*(s-c/2<0?-1:1),h=Math.max(r,Math.floor(t-s*l/c+p)),v=Math.min(n,Math.floor(t+(c-s)*l/c+p));rv(e,t,h,v,i)}const a=e[t];let o=r,u=n;for(hn(e,r,t),i(e[n],a)>0&&hn(e,r,n);o<u;){for(hn(e,o,u),++o,--u;i(e[o],a)<0;)++o;for(;i(e[u],a)>0;)--u}i(e[r],a)===0?hn(e,r,u):(++u,hn(e,u,n)),u<=t&&(r=u+1),t<=u&&(n=u-1)}return e}function hn(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function gj(e,t,r){if(e=Float64Array.from(sj(e)),!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return cf(e);if(t>=1)return uf(e);var n,i=(n-1)*t,a=Math.floor(i),o=uf(rv(e,a).subarray(0,a+1)),u=cf(e.subarray(a+1));return o+(u-o)*(i-a)}}function bj(e,t,r=ev){if(!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e),u=+r(e[a+1],a+1,e);return o+(u-o)*(i-a)}}function xj(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=Math.max(0,Math.ceil((t-e)/r))|0,a=new Array(i);++n<i;)a[n]=e+n*r;return a}function Qe(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e);break}return this}function $t(e,t){switch(arguments.length){case 0:break;case 1:{typeof e=="function"?this.interpolator(e):this.range(e);break}default:{this.domain(e),typeof t=="function"?this.interpolator(t):this.range(t);break}}return this}const Tu=Symbol("implicit");function Zc(){var e=new af,t=[],r=[],n=Tu;function i(a){let o=e.get(a);if(o===void 0){if(n!==Tu)return n;e.set(a,o=t.push(a)-1)}return r[o%r.length]}return i.domain=function(a){if(!arguments.length)return t.slice();t=[],e=new af;for(const o of a)e.has(o)||e.set(o,t.push(o)-1);return i},i.range=function(a){return arguments.length?(r=Array.from(a),i):r.slice()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return Zc(t,r).unknown(n)},Qe.apply(i,arguments),i}function Nn(){var e=Zc().unknown(void 0),t=e.domain,r=e.range,n=0,i=1,a,o,u=!1,c=0,s=0,f=.5;delete e.unknown;function l(){var p=t().length,h=i<n,v=h?i:n,d=h?n:i;a=(d-v)/Math.max(1,p-c+s*2),u&&(a=Math.floor(a)),v+=(d-v-a*(p-c))*f,o=a*(1-c),u&&(v=Math.round(v),o=Math.round(o));var y=xj(p).map(function(b){return v+a*b});return r(h?y.reverse():y)}return e.domain=function(p){return arguments.length?(t(p),l()):t()},e.range=function(p){return arguments.length?([n,i]=p,n=+n,i=+i,l()):[n,i]},e.rangeRound=function(p){return[n,i]=p,n=+n,i=+i,u=!0,l()},e.bandwidth=function(){return o},e.step=function(){return a},e.round=function(p){return arguments.length?(u=!!p,l()):u},e.padding=function(p){return arguments.length?(c=Math.min(1,s=+p),l()):c},e.paddingInner=function(p){return arguments.length?(c=Math.min(1,p),l()):c},e.paddingOuter=function(p){return arguments.length?(s=+p,l()):s},e.align=function(p){return arguments.length?(f=Math.max(0,Math.min(1,p)),l()):f},e.copy=function(){return Nn(t(),[n,i]).round(u).paddingInner(c).paddingOuter(s).align(f)},Qe.apply(l(),arguments)}function nv(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return nv(t())},e}function An(){return nv(Nn.apply(null,arguments).paddingInner(1))}function Jc(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function iv(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function di(){}var Rn=.7,Ji=1/Rn,Ar="\\s*([+-]?\\d+)\\s*",Ln="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",ut="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",wj=/^#([0-9a-f]{3,8})$/,Oj=new RegExp(`^rgb\\(${Ar},${Ar},${Ar}\\)$`),Aj=new RegExp(`^rgb\\(${ut},${ut},${ut}\\)$`),Sj=new RegExp(`^rgba\\(${Ar},${Ar},${Ar},${Ln}\\)$`),Pj=new RegExp(`^rgba\\(${ut},${ut},${ut},${Ln}\\)$`),_j=new RegExp(`^hsl\\(${Ln},${ut},${ut}\\)$`),$j=new RegExp(`^hsla\\(${Ln},${ut},${ut},${Ln}\\)$`),sf={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};Jc(di,Bn,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:lf,formatHex:lf,formatHex8:Tj,formatHsl:Ej,formatRgb:ff,toString:ff});function lf(){return this.rgb().formatHex()}function Tj(){return this.rgb().formatHex8()}function Ej(){return av(this).formatHsl()}function ff(){return this.rgb().formatRgb()}function Bn(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=wj.exec(e))?(r=t[1].length,t=parseInt(t[1],16),r===6?pf(t):r===3?new Re(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):r===8?_i(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):r===4?_i(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=Oj.exec(e))?new Re(t[1],t[2],t[3],1):(t=Aj.exec(e))?new Re(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=Sj.exec(e))?_i(t[1],t[2],t[3],t[4]):(t=Pj.exec(e))?_i(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=_j.exec(e))?vf(t[1],t[2]/100,t[3]/100,1):(t=$j.exec(e))?vf(t[1],t[2]/100,t[3]/100,t[4]):sf.hasOwnProperty(e)?pf(sf[e]):e==="transparent"?new Re(NaN,NaN,NaN,0):null}function pf(e){return new Re(e>>16&255,e>>8&255,e&255,1)}function _i(e,t,r,n){return n<=0&&(e=t=r=NaN),new Re(e,t,r,n)}function jj(e){return e instanceof di||(e=Bn(e)),e?(e=e.rgb(),new Re(e.r,e.g,e.b,e.opacity)):new Re}function Eu(e,t,r,n){return arguments.length===1?jj(e):new Re(e,t,r,n??1)}function Re(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}Jc(Re,Eu,iv(di,{brighter(e){return e=e==null?Ji:Math.pow(Ji,e),new Re(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?Rn:Math.pow(Rn,e),new Re(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Re(Jt(this.r),Jt(this.g),Jt(this.b),Qi(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:hf,formatHex:hf,formatHex8:Mj,formatRgb:df,toString:df}));function hf(){return`#${Xt(this.r)}${Xt(this.g)}${Xt(this.b)}`}function Mj(){return`#${Xt(this.r)}${Xt(this.g)}${Xt(this.b)}${Xt((isNaN(this.opacity)?1:this.opacity)*255)}`}function df(){const e=Qi(this.opacity);return`${e===1?"rgb(":"rgba("}${Jt(this.r)}, ${Jt(this.g)}, ${Jt(this.b)}${e===1?")":`, ${e})`}`}function Qi(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function Jt(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Xt(e){return e=Jt(e),(e<16?"0":"")+e.toString(16)}function vf(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new nt(e,t,r,n)}function av(e){if(e instanceof nt)return new nt(e.h,e.s,e.l,e.opacity);if(e instanceof di||(e=Bn(e)),!e)return new nt;if(e instanceof nt)return e;e=e.rgb();var t=e.r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,u=a-i,c=(a+i)/2;return u?(t===a?o=(r-n)/u+(r<n)*6:r===a?o=(n-t)/u+2:o=(t-r)/u+4,u/=c<.5?a+i:2-a-i,o*=60):u=c>0&&c<1?0:o,new nt(o,u,c,e.opacity)}function Cj(e,t,r,n){return arguments.length===1?av(e):new nt(e,t,r,n??1)}function nt(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}Jc(nt,Cj,iv(di,{brighter(e){return e=e==null?Ji:Math.pow(Ji,e),new nt(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?Rn:Math.pow(Rn,e),new nt(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new Re(No(e>=240?e-240:e+120,i,n),No(e,i,n),No(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new nt(yf(this.h),$i(this.s),$i(this.l),Qi(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=Qi(this.opacity);return`${e===1?"hsl(":"hsla("}${yf(this.h)}, ${$i(this.s)*100}%, ${$i(this.l)*100}%${e===1?")":`, ${e})`}`}}));function yf(e){return e=(e||0)%360,e<0?e+360:e}function $i(e){return Math.max(0,Math.min(1,e||0))}function No(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}const Qc=e=>()=>e;function Ij(e,t){return function(r){return e+r*t}}function kj(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(n){return Math.pow(e+n*t,r)}}function Dj(e){return(e=+e)==1?ov:function(t,r){return r-t?kj(t,r,e):Qc(isNaN(t)?r:t)}}function ov(e,t){var r=t-e;return r?Ij(e,r):Qc(isNaN(e)?t:e)}const mf=function e(t){var r=Dj(t);function n(i,a){var o=r((i=Eu(i)).r,(a=Eu(a)).r),u=r(i.g,a.g),c=r(i.b,a.b),s=ov(i.opacity,a.opacity);return function(f){return i.r=o(f),i.g=u(f),i.b=c(f),i.opacity=s(f),i+""}}return n.gamma=e,n}(1);function Nj(e,t){t||(t=[]);var r=e?Math.min(t.length,e.length):0,n=t.slice(),i;return function(a){for(i=0;i<r;++i)n[i]=e[i]*(1-a)+t[i]*a;return n}}function Rj(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function Lj(e,t){var r=t?t.length:0,n=e?Math.min(r,e.length):0,i=new Array(n),a=new Array(r),o;for(o=0;o<n;++o)i[o]=on(e[o],t[o]);for(;o<r;++o)a[o]=t[o];return function(u){for(o=0;o<n;++o)a[o]=i[o](u);return a}}function Bj(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}function ea(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}function Fj(e,t){var r={},n={},i;(e===null||typeof e!="object")&&(e={}),(t===null||typeof t!="object")&&(t={});for(i in t)i in e?r[i]=on(e[i],t[i]):n[i]=t[i];return function(a){for(i in r)n[i]=r[i](a);return n}}var ju=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Ro=new RegExp(ju.source,"g");function Wj(e){return function(){return e}}function zj(e){return function(t){return e(t)+""}}function Uj(e,t){var r=ju.lastIndex=Ro.lastIndex=0,n,i,a,o=-1,u=[],c=[];for(e=e+"",t=t+"";(n=ju.exec(e))&&(i=Ro.exec(t));)(a=i.index)>r&&(a=t.slice(r,a),u[o]?u[o]+=a:u[++o]=a),(n=n[0])===(i=i[0])?u[o]?u[o]+=i:u[++o]=i:(u[++o]=null,c.push({i:o,x:ea(n,i)})),r=Ro.lastIndex;return r<t.length&&(a=t.slice(r),u[o]?u[o]+=a:u[++o]=a),u.length<2?c[0]?zj(c[0].x):Wj(t):(t=c.length,function(s){for(var f=0,l;f<t;++f)u[(l=c[f]).i]=l.x(s);return u.join("")})}function on(e,t){var r=typeof t,n;return t==null||r==="boolean"?Qc(t):(r==="number"?ea:r==="string"?(n=Bn(t))?(t=n,mf):Uj:t instanceof Bn?mf:t instanceof Date?Bj:Rj(t)?Nj:Array.isArray(t)?Lj:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?Fj:ea)(e,t)}function es(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function qj(e,t){t===void 0&&(t=e,e=on);for(var r=0,n=t.length-1,i=t[0],a=new Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(o){var u=Math.max(0,Math.min(n-1,Math.floor(o*=n)));return a[u](o-u)}}function Hj(e){return function(){return e}}function ta(e){return+e}var gf=[0,1];function ke(e){return e}function Mu(e,t){return(t-=e=+e)?function(r){return(r-e)/t}:Hj(isNaN(t)?NaN:.5)}function Kj(e,t){var r;return e>t&&(r=e,e=t,t=r),function(n){return Math.max(e,Math.min(t,n))}}function Gj(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=Mu(i,n),a=r(o,a)):(n=Mu(n,i),a=r(a,o)),function(u){return a(n(u))}}function Vj(e,t,r){var n=Math.min(e.length,t.length)-1,i=new Array(n),a=new Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=Mu(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(u){var c=hi(e,u,1,n)-1;return a[c](i[c](u))}}function vi(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function Xa(){var e=gf,t=gf,r=on,n,i,a,o=ke,u,c,s;function f(){var p=Math.min(e.length,t.length);return o!==ke&&(o=Kj(e[0],e[p-1])),u=p>2?Vj:Gj,c=s=null,l}function l(p){return p==null||isNaN(p=+p)?a:(c||(c=u(e.map(n),t,r)))(n(o(p)))}return l.invert=function(p){return o(i((s||(s=u(t,e.map(n),ea)))(p)))},l.domain=function(p){return arguments.length?(e=Array.from(p,ta),f()):e.slice()},l.range=function(p){return arguments.length?(t=Array.from(p),f()):t.slice()},l.rangeRound=function(p){return t=Array.from(p),r=es,f()},l.clamp=function(p){return arguments.length?(o=p?!0:ke,f()):o!==ke},l.interpolate=function(p){return arguments.length?(r=p,f()):r},l.unknown=function(p){return arguments.length?(a=p,l):a},function(p,h){return n=p,i=h,f()}}function ts(){return Xa()(ke,ke)}function Xj(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)}function ra(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function jr(e){return e=ra(Math.abs(e)),e?e[1]:NaN}function Yj(e,t){return function(r,n){for(var i=r.length,a=[],o=0,u=e[0],c=0;i>0&&u>0&&(c+u+1>n&&(u=Math.max(1,n-c)),a.push(r.substring(i-=u,i+u)),!((c+=u+1)>n));)u=e[o=(o+1)%e.length];return a.reverse().join(t)}}function Zj(e){return function(t){return t.replace(/[0-9]/g,function(r){return e[+r]})}}var Jj=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Fn(e){if(!(t=Jj.exec(e)))throw new Error("invalid format: "+e);var t;return new rs({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}Fn.prototype=rs.prototype;function rs(e){this.fill=e.fill===void 0?" ":e.fill+"",this.align=e.align===void 0?">":e.align+"",this.sign=e.sign===void 0?"-":e.sign+"",this.symbol=e.symbol===void 0?"":e.symbol+"",this.zero=!!e.zero,this.width=e.width===void 0?void 0:+e.width,this.comma=!!e.comma,this.precision=e.precision===void 0?void 0:+e.precision,this.trim=!!e.trim,this.type=e.type===void 0?"":e.type+""}rs.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function Qj(e){e:for(var t=e.length,r=1,n=-1,i;r<t;++r)switch(e[r]){case".":n=i=r;break;case"0":n===0&&(n=r),i=r;break;default:if(!+e[r])break e;n>0&&(n=0);break}return n>0?e.slice(0,n)+e.slice(i+1):e}var uv;function eM(e,t){var r=ra(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(uv=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,o=n.length;return a===o?n:a>o?n+new Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+new Array(1-a).join("0")+ra(e,Math.max(0,t+a-1))[0]}function bf(e,t){var r=ra(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+new Array(i-n.length+2).join("0")}const xf={"%":(e,t)=>(e*100).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:Xj,e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>bf(e*100,t),r:bf,s:eM,X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function wf(e){return e}var Of=Array.prototype.map,Af=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function tM(e){var t=e.grouping===void 0||e.thousands===void 0?wf:Yj(Of.call(e.grouping,Number),e.thousands+""),r=e.currency===void 0?"":e.currency[0]+"",n=e.currency===void 0?"":e.currency[1]+"",i=e.decimal===void 0?".":e.decimal+"",a=e.numerals===void 0?wf:Zj(Of.call(e.numerals,String)),o=e.percent===void 0?"%":e.percent+"",u=e.minus===void 0?"−":e.minus+"",c=e.nan===void 0?"NaN":e.nan+"";function s(l){l=Fn(l);var p=l.fill,h=l.align,v=l.sign,d=l.symbol,y=l.zero,b=l.width,w=l.comma,x=l.precision,A=l.trim,m=l.type;m==="n"?(w=!0,m="g"):xf[m]||(x===void 0&&(x=12),A=!0,m="g"),(y||p==="0"&&h==="=")&&(y=!0,p="0",h="=");var g=d==="$"?r:d==="#"&&/[boxX]/.test(m)?"0"+m.toLowerCase():"",O=d==="$"?n:/[%p]/.test(m)?o:"",P=xf[m],_=/[defgprs%]/.test(m);x=x===void 0?6:/[gprs]/.test(m)?Math.max(1,Math.min(21,x)):Math.max(0,Math.min(20,x));function E($){var T=g,C=O,I,M,k;if(m==="c")C=P($)+C,$="";else{$=+$;var D=$<0||1/$<0;if($=isNaN($)?c:P(Math.abs($),x),A&&($=Qj($)),D&&+$==0&&v!=="+"&&(D=!1),T=(D?v==="("?v:u:v==="-"||v==="("?"":v)+T,C=(m==="s"?Af[8+uv/3]:"")+C+(D&&v==="("?")":""),_){for(I=-1,M=$.length;++I<M;)if(k=$.charCodeAt(I),48>k||k>57){C=(k===46?i+$.slice(I+1):$.slice(I))+C,$=$.slice(0,I);break}}}w&&!y&&($=t($,1/0));var B=T.length+$.length+C.length,F=B<b?new Array(b-B+1).join(p):"";switch(w&&y&&($=t(F+$,F.length?b-C.length:1/0),F=""),h){case"<":$=T+$+C+F;break;case"=":$=T+F+$+C;break;case"^":$=F.slice(0,B=F.length>>1)+T+$+C+F.slice(B);break;default:$=F+T+$+C;break}return a($)}return E.toString=function(){return l+""},E}function f(l,p){var h=s((l=Fn(l),l.type="f",l)),v=Math.max(-8,Math.min(8,Math.floor(jr(p)/3)))*3,d=Math.pow(10,-v),y=Af[8+v/3];return function(b){return h(d*b)+y}}return{format:s,formatPrefix:f}}var Ti,ns,cv;rM({thousands:",",grouping:[3],currency:["$",""]});function rM(e){return Ti=tM(e),ns=Ti.format,cv=Ti.formatPrefix,Ti}function nM(e){return Math.max(0,-jr(Math.abs(e)))}function iM(e,t){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(jr(t)/3)))*3-jr(Math.abs(e)))}function aM(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,jr(t)-jr(e))+1}function sv(e,t,r,n){var i=$u(e,t,r),a;switch(n=Fn(n??",f"),n.type){case"s":{var o=Math.max(Math.abs(e),Math.abs(t));return n.precision==null&&!isNaN(a=iM(i,o))&&(n.precision=a),cv(n,o)}case"":case"e":case"g":case"p":case"r":{n.precision==null&&!isNaN(a=aM(i,Math.max(Math.abs(e),Math.abs(t))))&&(n.precision=a-(n.type==="e"));break}case"f":case"%":{n.precision==null&&!isNaN(a=nM(i))&&(n.precision=a-(n.type==="%")*2);break}}return ns(n)}function kt(e){var t=e.domain;return e.ticks=function(r){var n=t();return Pu(n[0],n[n.length-1],r??10)},e.tickFormat=function(r,n){var i=t();return sv(i[0],i[i.length-1],r??10,n)},e.nice=function(r){r==null&&(r=10);var n=t(),i=0,a=n.length-1,o=n[i],u=n[a],c,s,f=10;for(u<o&&(s=o,o=u,u=s,s=i,i=a,a=s);f-- >0;){if(s=_u(o,u,r),s===c)return n[i]=o,n[a]=u,t(n);if(s>0)o=Math.floor(o/s)*s,u=Math.ceil(u/s)*s;else if(s<0)o=Math.ceil(o*s)/s,u=Math.floor(u*s)/s;else break;c=s}return e},e}function na(){var e=ts();return e.copy=function(){return vi(e,na())},Qe.apply(e,arguments),kt(e)}function lv(e){var t;function r(n){return n==null||isNaN(n=+n)?t:n}return r.invert=r,r.domain=r.range=function(n){return arguments.length?(e=Array.from(n,ta),r):e.slice()},r.unknown=function(n){return arguments.length?(t=n,r):t},r.copy=function(){return lv(e).unknown(t)},e=arguments.length?Array.from(e,ta):[0,1],kt(r)}function fv(e,t){e=e.slice();var r=0,n=e.length-1,i=e[r],a=e[n],o;return a<i&&(o=r,r=n,n=o,o=i,i=a,a=o),e[r]=t.floor(i),e[n]=t.ceil(a),e}function Sf(e){return Math.log(e)}function Pf(e){return Math.exp(e)}function oM(e){return-Math.log(-e)}function uM(e){return-Math.exp(-e)}function cM(e){return isFinite(e)?+("1e"+e):e<0?0:e}function sM(e){return e===10?cM:e===Math.E?Math.exp:t=>Math.pow(e,t)}function lM(e){return e===Math.E?Math.log:e===10&&Math.log10||e===2&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}function _f(e){return(t,r)=>-e(-t,r)}function is(e){const t=e(Sf,Pf),r=t.domain;let n=10,i,a;function o(){return i=lM(n),a=sM(n),r()[0]<0?(i=_f(i),a=_f(a),e(oM,uM)):e(Sf,Pf),t}return t.base=function(u){return arguments.length?(n=+u,o()):n},t.domain=function(u){return arguments.length?(r(u),o()):r()},t.ticks=u=>{const c=r();let s=c[0],f=c[c.length-1];const l=f<s;l&&([s,f]=[f,s]);let p=i(s),h=i(f),v,d;const y=u==null?10:+u;let b=[];if(!(n%1)&&h-p<y){if(p=Math.floor(p),h=Math.ceil(h),s>0){for(;p<=h;++p)for(v=1;v<n;++v)if(d=p<0?v/a(-p):v*a(p),!(d<s)){if(d>f)break;b.push(d)}}else for(;p<=h;++p)for(v=n-1;v>=1;--v)if(d=p>0?v/a(-p):v*a(p),!(d<s)){if(d>f)break;b.push(d)}b.length*2<y&&(b=Pu(s,f,y))}else b=Pu(p,h,Math.min(h-p,y)).map(a);return l?b.reverse():b},t.tickFormat=(u,c)=>{if(u==null&&(u=10),c==null&&(c=n===10?"s":","),typeof c!="function"&&(!(n%1)&&(c=Fn(c)).precision==null&&(c.trim=!0),c=ns(c)),u===1/0)return c;const s=Math.max(1,n*u/t.ticks().length);return f=>{let l=f/a(Math.round(i(f)));return l*n<n-.5&&(l*=n),l<=s?c(f):""}},t.nice=()=>r(fv(r(),{floor:u=>a(Math.floor(i(u))),ceil:u=>a(Math.ceil(i(u)))})),t}function pv(){const e=is(Xa()).domain([1,10]);return e.copy=()=>vi(e,pv()).base(e.base()),Qe.apply(e,arguments),e}function $f(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function Tf(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function as(e){var t=1,r=e($f(t),Tf(t));return r.constant=function(n){return arguments.length?e($f(t=+n),Tf(t)):t},kt(r)}function hv(){var e=as(Xa());return e.copy=function(){return vi(e,hv()).constant(e.constant())},Qe.apply(e,arguments)}function Ef(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function fM(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function pM(e){return e<0?-e*e:e*e}function os(e){var t=e(ke,ke),r=1;function n(){return r===1?e(ke,ke):r===.5?e(fM,pM):e(Ef(r),Ef(1/r))}return t.exponent=function(i){return arguments.length?(r=+i,n()):r},kt(t)}function us(){var e=os(Xa());return e.copy=function(){return vi(e,us()).exponent(e.exponent())},Qe.apply(e,arguments),e}function hM(){return us.apply(null,arguments).exponent(.5)}function jf(e){return Math.sign(e)*e*e}function dM(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}function dv(){var e=ts(),t=[0,1],r=!1,n;function i(a){var o=dM(e(a));return isNaN(o)?n:r?Math.round(o):o}return i.invert=function(a){return e.invert(jf(a))},i.domain=function(a){return arguments.length?(e.domain(a),i):e.domain()},i.range=function(a){return arguments.length?(e.range((t=Array.from(a,ta)).map(jf)),i):t.slice()},i.rangeRound=function(a){return i.range(a).round(!0)},i.round=function(a){return arguments.length?(r=!!a,i):r},i.clamp=function(a){return arguments.length?(e.clamp(a),i):e.clamp()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return dv(e.domain(),t).round(r).clamp(e.clamp()).unknown(n)},Qe.apply(i,arguments),kt(i)}function vv(){var e=[],t=[],r=[],n;function i(){var o=0,u=Math.max(1,t.length);for(r=new Array(u-1);++o<u;)r[o-1]=bj(e,o/u);return a}function a(o){return o==null||isNaN(o=+o)?n:t[hi(r,o)]}return a.invertExtent=function(o){var u=t.indexOf(o);return u<0?[NaN,NaN]:[u>0?r[u-1]:e[0],u<r.length?r[u]:e[e.length-1]]},a.domain=function(o){if(!arguments.length)return e.slice();e=[];for(let u of o)u!=null&&!isNaN(u=+u)&&e.push(u);return e.sort(Ct),i()},a.range=function(o){return arguments.length?(t=Array.from(o),i()):t.slice()},a.unknown=function(o){return arguments.length?(n=o,a):n},a.quantiles=function(){return r.slice()},a.copy=function(){return vv().domain(e).range(t).unknown(n)},Qe.apply(a,arguments)}function yv(){var e=0,t=1,r=1,n=[.5],i=[0,1],a;function o(c){return c!=null&&c<=c?i[hi(n,c,0,r)]:a}function u(){var c=-1;for(n=new Array(r);++c<r;)n[c]=((c+1)*t-(c-r)*e)/(r+1);return o}return o.domain=function(c){return arguments.length?([e,t]=c,e=+e,t=+t,u()):[e,t]},o.range=function(c){return arguments.length?(r=(i=Array.from(c)).length-1,u()):i.slice()},o.invertExtent=function(c){var s=i.indexOf(c);return s<0?[NaN,NaN]:s<1?[e,n[0]]:s>=r?[n[r-1],t]:[n[s-1],n[s]]},o.unknown=function(c){return arguments.length&&(a=c),o},o.thresholds=function(){return n.slice()},o.copy=function(){return yv().domain([e,t]).range(i).unknown(a)},Qe.apply(kt(o),arguments)}function mv(){var e=[.5],t=[0,1],r,n=1;function i(a){return a!=null&&a<=a?t[hi(e,a,0,n)]:r}return i.domain=function(a){return arguments.length?(e=Array.from(a),n=Math.min(e.length,t.length-1),i):e.slice()},i.range=function(a){return arguments.length?(t=Array.from(a),n=Math.min(e.length,t.length-1),i):t.slice()},i.invertExtent=function(a){var o=t.indexOf(a);return[e[o-1],e[o]]},i.unknown=function(a){return arguments.length?(r=a,i):r},i.copy=function(){return mv().domain(e).range(t).unknown(r)},Qe.apply(i,arguments)}const Lo=new Date,Bo=new Date;function Pe(e,t,r,n){function i(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=a=>(e(a=new Date(+a)),a),i.ceil=a=>(e(a=new Date(a-1)),t(a,1),e(a),a),i.round=a=>{const o=i(a),u=i.ceil(a);return a-o<u-a?o:u},i.offset=(a,o)=>(t(a=new Date(+a),o==null?1:Math.floor(o)),a),i.range=(a,o,u)=>{const c=[];if(a=i.ceil(a),u=u==null?1:Math.floor(u),!(a<o)||!(u>0))return c;let s;do c.push(s=new Date(+a)),t(a,u),e(a);while(s<a&&a<o);return c},i.filter=a=>Pe(o=>{if(o>=o)for(;e(o),!a(o);)o.setTime(o-1)},(o,u)=>{if(o>=o)if(u<0)for(;++u<=0;)for(;t(o,-1),!a(o););else for(;--u>=0;)for(;t(o,1),!a(o););}),r&&(i.count=(a,o)=>(Lo.setTime(+a),Bo.setTime(+o),e(Lo),e(Bo),Math.floor(r(Lo,Bo))),i.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(n?o=>n(o)%a===0:o=>i.count(0,o)%a===0):i)),i}const ia=Pe(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);ia.every=e=>(e=Math.floor(e),!isFinite(e)||!(e>0)?null:e>1?Pe(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):ia);ia.range;const yt=1e3,Ze=yt*60,mt=Ze*60,Ot=mt*24,cs=Ot*7,Mf=Ot*30,Fo=Ot*365,Yt=Pe(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*yt)},(e,t)=>(t-e)/yt,e=>e.getUTCSeconds());Yt.range;const ss=Pe(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*yt)},(e,t)=>{e.setTime(+e+t*Ze)},(e,t)=>(t-e)/Ze,e=>e.getMinutes());ss.range;const ls=Pe(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*Ze)},(e,t)=>(t-e)/Ze,e=>e.getUTCMinutes());ls.range;const fs=Pe(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*yt-e.getMinutes()*Ze)},(e,t)=>{e.setTime(+e+t*mt)},(e,t)=>(t-e)/mt,e=>e.getHours());fs.range;const ps=Pe(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*mt)},(e,t)=>(t-e)/mt,e=>e.getUTCHours());ps.range;const yi=Pe(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*Ze)/Ot,e=>e.getDate()-1);yi.range;const Ya=Pe(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/Ot,e=>e.getUTCDate()-1);Ya.range;const gv=Pe(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/Ot,e=>Math.floor(e/Ot));gv.range;function cr(e){return Pe(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(t,r)=>{t.setDate(t.getDate()+r*7)},(t,r)=>(r-t-(r.getTimezoneOffset()-t.getTimezoneOffset())*Ze)/cs)}const Za=cr(0),aa=cr(1),vM=cr(2),yM=cr(3),Mr=cr(4),mM=cr(5),gM=cr(6);Za.range;aa.range;vM.range;yM.range;Mr.range;mM.range;gM.range;function sr(e){return Pe(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCDate(t.getUTCDate()+r*7)},(t,r)=>(r-t)/cs)}const Ja=sr(0),oa=sr(1),bM=sr(2),xM=sr(3),Cr=sr(4),wM=sr(5),OM=sr(6);Ja.range;oa.range;bM.range;xM.range;Cr.range;wM.range;OM.range;const hs=Pe(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());hs.range;const ds=Pe(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());ds.range;const At=Pe(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());At.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:Pe(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)});At.range;const St=Pe(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());St.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:Pe(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)});St.range;function bv(e,t,r,n,i,a){const o=[[Yt,1,yt],[Yt,5,5*yt],[Yt,15,15*yt],[Yt,30,30*yt],[a,1,Ze],[a,5,5*Ze],[a,15,15*Ze],[a,30,30*Ze],[i,1,mt],[i,3,3*mt],[i,6,6*mt],[i,12,12*mt],[n,1,Ot],[n,2,2*Ot],[r,1,cs],[t,1,Mf],[t,3,3*Mf],[e,1,Fo]];function u(s,f,l){const p=f<s;p&&([s,f]=[f,s]);const h=l&&typeof l.range=="function"?l:c(s,f,l),v=h?h.range(s,+f+1):[];return p?v.reverse():v}function c(s,f,l){const p=Math.abs(f-s)/l,h=Yc(([,,y])=>y).right(o,p);if(h===o.length)return e.every($u(s/Fo,f/Fo,l));if(h===0)return ia.every(Math.max($u(s,f,l),1));const[v,d]=o[p/o[h-1][2]<o[h][2]/p?h-1:h];return v.every(d)}return[u,c]}const[AM,SM]=bv(St,ds,Ja,gv,ps,ls),[PM,_M]=bv(At,hs,Za,yi,fs,ss);function Wo(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function zo(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function dn(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}function $M(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,u=e.months,c=e.shortMonths,s=vn(i),f=yn(i),l=vn(a),p=yn(a),h=vn(o),v=yn(o),d=vn(u),y=yn(u),b=vn(c),w=yn(c),x={a:D,A:B,b:F,B:q,c:null,d:Rf,e:Rf,f:YM,g:oC,G:cC,H:GM,I:VM,j:XM,L:xv,m:ZM,M:JM,p:G,q:z,Q:Ff,s:Wf,S:QM,u:eC,U:tC,V:rC,w:nC,W:iC,x:null,X:null,y:aC,Y:uC,Z:sC,"%":Bf},A={a:V,A:le,b:ye,B:Be,c:null,d:Lf,e:Lf,f:hC,g:AC,G:PC,H:lC,I:fC,j:pC,L:Ov,m:dC,M:vC,p:Lt,q:De,Q:Ff,s:Wf,S:yC,u:mC,U:gC,V:bC,w:xC,W:wC,x:null,X:null,y:OC,Y:SC,Z:_C,"%":Bf},m={a:E,A:$,b:T,B:C,c:I,d:Df,e:Df,f:UM,g:kf,G:If,H:Nf,I:Nf,j:BM,L:zM,m:LM,M:FM,p:_,q:RM,Q:HM,s:KM,S:WM,u:CM,U:IM,V:kM,w:MM,W:DM,x:M,X:k,y:kf,Y:If,Z:NM,"%":qM};x.x=g(r,x),x.X=g(n,x),x.c=g(t,x),A.x=g(r,A),A.X=g(n,A),A.c=g(t,A);function g(W,Z){return function(Q){var N=[],de=-1,ee=0,be=W.length,xe,Ne,Tt;for(Q instanceof Date||(Q=new Date(+Q));++de<be;)W.charCodeAt(de)===37&&(N.push(W.slice(ee,de)),(Ne=Cf[xe=W.charAt(++de)])!=null?xe=W.charAt(++de):Ne=xe==="e"?" ":"0",(Tt=Z[xe])&&(xe=Tt(Q,Ne)),N.push(xe),ee=de+1);return N.push(W.slice(ee,de)),N.join("")}}function O(W,Z){return function(Q){var N=dn(1900,void 0,1),de=P(N,W,Q+="",0),ee,be;if(de!=Q.length)return null;if("Q"in N)return new Date(N.Q);if("s"in N)return new Date(N.s*1e3+("L"in N?N.L:0));if(Z&&!("Z"in N)&&(N.Z=0),"p"in N&&(N.H=N.H%12+N.p*12),N.m===void 0&&(N.m="q"in N?N.q:0),"V"in N){if(N.V<1||N.V>53)return null;"w"in N||(N.w=1),"Z"in N?(ee=zo(dn(N.y,0,1)),be=ee.getUTCDay(),ee=be>4||be===0?oa.ceil(ee):oa(ee),ee=Ya.offset(ee,(N.V-1)*7),N.y=ee.getUTCFullYear(),N.m=ee.getUTCMonth(),N.d=ee.getUTCDate()+(N.w+6)%7):(ee=Wo(dn(N.y,0,1)),be=ee.getDay(),ee=be>4||be===0?aa.ceil(ee):aa(ee),ee=yi.offset(ee,(N.V-1)*7),N.y=ee.getFullYear(),N.m=ee.getMonth(),N.d=ee.getDate()+(N.w+6)%7)}else("W"in N||"U"in N)&&("w"in N||(N.w="u"in N?N.u%7:"W"in N?1:0),be="Z"in N?zo(dn(N.y,0,1)).getUTCDay():Wo(dn(N.y,0,1)).getDay(),N.m=0,N.d="W"in N?(N.w+6)%7+N.W*7-(be+5)%7:N.w+N.U*7-(be+6)%7);return"Z"in N?(N.H+=N.Z/100|0,N.M+=N.Z%100,zo(N)):Wo(N)}}function P(W,Z,Q,N){for(var de=0,ee=Z.length,be=Q.length,xe,Ne;de<ee;){if(N>=be)return-1;if(xe=Z.charCodeAt(de++),xe===37){if(xe=Z.charAt(de++),Ne=m[xe in Cf?Z.charAt(de++):xe],!Ne||(N=Ne(W,Q,N))<0)return-1}else if(xe!=Q.charCodeAt(N++))return-1}return N}function _(W,Z,Q){var N=s.exec(Z.slice(Q));return N?(W.p=f.get(N[0].toLowerCase()),Q+N[0].length):-1}function E(W,Z,Q){var N=h.exec(Z.slice(Q));return N?(W.w=v.get(N[0].toLowerCase()),Q+N[0].length):-1}function $(W,Z,Q){var N=l.exec(Z.slice(Q));return N?(W.w=p.get(N[0].toLowerCase()),Q+N[0].length):-1}function T(W,Z,Q){var N=b.exec(Z.slice(Q));return N?(W.m=w.get(N[0].toLowerCase()),Q+N[0].length):-1}function C(W,Z,Q){var N=d.exec(Z.slice(Q));return N?(W.m=y.get(N[0].toLowerCase()),Q+N[0].length):-1}function I(W,Z,Q){return P(W,t,Z,Q)}function M(W,Z,Q){return P(W,r,Z,Q)}function k(W,Z,Q){return P(W,n,Z,Q)}function D(W){return o[W.getDay()]}function B(W){return a[W.getDay()]}function F(W){return c[W.getMonth()]}function q(W){return u[W.getMonth()]}function G(W){return i[+(W.getHours()>=12)]}function z(W){return 1+~~(W.getMonth()/3)}function V(W){return o[W.getUTCDay()]}function le(W){return a[W.getUTCDay()]}function ye(W){return c[W.getUTCMonth()]}function Be(W){return u[W.getUTCMonth()]}function Lt(W){return i[+(W.getUTCHours()>=12)]}function De(W){return 1+~~(W.getUTCMonth()/3)}return{format:function(W){var Z=g(W+="",x);return Z.toString=function(){return W},Z},parse:function(W){var Z=O(W+="",!1);return Z.toString=function(){return W},Z},utcFormat:function(W){var Z=g(W+="",A);return Z.toString=function(){return W},Z},utcParse:function(W){var Z=O(W+="",!0);return Z.toString=function(){return W},Z}}}var Cf={"-":"",_:" ",0:"0"},Te=/^\s*\d+/,TM=/^%/,EM=/[\\^$*+?|[\]().{}]/g;function re(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?new Array(r-a+1).join(t)+i:i)}function jM(e){return e.replace(EM,"\\$&")}function vn(e){return new RegExp("^(?:"+e.map(jM).join("|")+")","i")}function yn(e){return new Map(e.map((t,r)=>[t.toLowerCase(),r]))}function MM(e,t,r){var n=Te.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function CM(e,t,r){var n=Te.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function IM(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function kM(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function DM(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function If(e,t,r){var n=Te.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function kf(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function NM(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function RM(e,t,r){var n=Te.exec(t.slice(r,r+1));return n?(e.q=n[0]*3-3,r+n[0].length):-1}function LM(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function Df(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function BM(e,t,r){var n=Te.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function Nf(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function FM(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function WM(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function zM(e,t,r){var n=Te.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function UM(e,t,r){var n=Te.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function qM(e,t,r){var n=TM.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function HM(e,t,r){var n=Te.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function KM(e,t,r){var n=Te.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function Rf(e,t){return re(e.getDate(),t,2)}function GM(e,t){return re(e.getHours(),t,2)}function VM(e,t){return re(e.getHours()%12||12,t,2)}function XM(e,t){return re(1+yi.count(At(e),e),t,3)}function xv(e,t){return re(e.getMilliseconds(),t,3)}function YM(e,t){return xv(e,t)+"000"}function ZM(e,t){return re(e.getMonth()+1,t,2)}function JM(e,t){return re(e.getMinutes(),t,2)}function QM(e,t){return re(e.getSeconds(),t,2)}function eC(e){var t=e.getDay();return t===0?7:t}function tC(e,t){return re(Za.count(At(e)-1,e),t,2)}function wv(e){var t=e.getDay();return t>=4||t===0?Mr(e):Mr.ceil(e)}function rC(e,t){return e=wv(e),re(Mr.count(At(e),e)+(At(e).getDay()===4),t,2)}function nC(e){return e.getDay()}function iC(e,t){return re(aa.count(At(e)-1,e),t,2)}function aC(e,t){return re(e.getFullYear()%100,t,2)}function oC(e,t){return e=wv(e),re(e.getFullYear()%100,t,2)}function uC(e,t){return re(e.getFullYear()%1e4,t,4)}function cC(e,t){var r=e.getDay();return e=r>=4||r===0?Mr(e):Mr.ceil(e),re(e.getFullYear()%1e4,t,4)}function sC(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+re(t/60|0,"0",2)+re(t%60,"0",2)}function Lf(e,t){return re(e.getUTCDate(),t,2)}function lC(e,t){return re(e.getUTCHours(),t,2)}function fC(e,t){return re(e.getUTCHours()%12||12,t,2)}function pC(e,t){return re(1+Ya.count(St(e),e),t,3)}function Ov(e,t){return re(e.getUTCMilliseconds(),t,3)}function hC(e,t){return Ov(e,t)+"000"}function dC(e,t){return re(e.getUTCMonth()+1,t,2)}function vC(e,t){return re(e.getUTCMinutes(),t,2)}function yC(e,t){return re(e.getUTCSeconds(),t,2)}function mC(e){var t=e.getUTCDay();return t===0?7:t}function gC(e,t){return re(Ja.count(St(e)-1,e),t,2)}function Av(e){var t=e.getUTCDay();return t>=4||t===0?Cr(e):Cr.ceil(e)}function bC(e,t){return e=Av(e),re(Cr.count(St(e),e)+(St(e).getUTCDay()===4),t,2)}function xC(e){return e.getUTCDay()}function wC(e,t){return re(oa.count(St(e)-1,e),t,2)}function OC(e,t){return re(e.getUTCFullYear()%100,t,2)}function AC(e,t){return e=Av(e),re(e.getUTCFullYear()%100,t,2)}function SC(e,t){return re(e.getUTCFullYear()%1e4,t,4)}function PC(e,t){var r=e.getUTCDay();return e=r>=4||r===0?Cr(e):Cr.ceil(e),re(e.getUTCFullYear()%1e4,t,4)}function _C(){return"+0000"}function Bf(){return"%"}function Ff(e){return+e}function Wf(e){return Math.floor(+e/1e3)}var hr,Sv,Pv;$C({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function $C(e){return hr=$M(e),Sv=hr.format,hr.parse,Pv=hr.utcFormat,hr.utcParse,hr}function TC(e){return new Date(e)}function EC(e){return e instanceof Date?+e:+new Date(+e)}function vs(e,t,r,n,i,a,o,u,c,s){var f=ts(),l=f.invert,p=f.domain,h=s(".%L"),v=s(":%S"),d=s("%I:%M"),y=s("%I %p"),b=s("%a %d"),w=s("%b %d"),x=s("%B"),A=s("%Y");function m(g){return(c(g)<g?h:u(g)<g?v:o(g)<g?d:a(g)<g?y:n(g)<g?i(g)<g?b:w:r(g)<g?x:A)(g)}return f.invert=function(g){return new Date(l(g))},f.domain=function(g){return arguments.length?p(Array.from(g,EC)):p().map(TC)},f.ticks=function(g){var O=p();return e(O[0],O[O.length-1],g??10)},f.tickFormat=function(g,O){return O==null?m:s(O)},f.nice=function(g){var O=p();return(!g||typeof g.range!="function")&&(g=t(O[0],O[O.length-1],g??10)),g?p(fv(O,g)):f},f.copy=function(){return vi(f,vs(e,t,r,n,i,a,o,u,c,s))},f}function jC(){return Qe.apply(vs(PM,_M,At,hs,Za,yi,fs,ss,Yt,Sv).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function MC(){return Qe.apply(vs(AM,SM,St,ds,Ja,Ya,ps,ls,Yt,Pv).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function Qa(){var e=0,t=1,r,n,i,a,o=ke,u=!1,c;function s(l){return l==null||isNaN(l=+l)?c:o(i===0?.5:(l=(a(l)-r)*i,u?Math.max(0,Math.min(1,l)):l))}s.domain=function(l){return arguments.length?([e,t]=l,r=a(e=+e),n=a(t=+t),i=r===n?0:1/(n-r),s):[e,t]},s.clamp=function(l){return arguments.length?(u=!!l,s):u},s.interpolator=function(l){return arguments.length?(o=l,s):o};function f(l){return function(p){var h,v;return arguments.length?([h,v]=p,o=l(h,v),s):[o(0),o(1)]}}return s.range=f(on),s.rangeRound=f(es),s.unknown=function(l){return arguments.length?(c=l,s):c},function(l){return a=l,r=l(e),n=l(t),i=r===n?0:1/(n-r),s}}function Dt(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function _v(){var e=kt(Qa()(ke));return e.copy=function(){return Dt(e,_v())},$t.apply(e,arguments)}function $v(){var e=is(Qa()).domain([1,10]);return e.copy=function(){return Dt(e,$v()).base(e.base())},$t.apply(e,arguments)}function Tv(){var e=as(Qa());return e.copy=function(){return Dt(e,Tv()).constant(e.constant())},$t.apply(e,arguments)}function ys(){var e=os(Qa());return e.copy=function(){return Dt(e,ys()).exponent(e.exponent())},$t.apply(e,arguments)}function CC(){return ys.apply(null,arguments).exponent(.5)}function Ev(){var e=[],t=ke;function r(n){if(n!=null&&!isNaN(n=+n))return t((hi(e,n,1)-1)/(e.length-1))}return r.domain=function(n){if(!arguments.length)return e.slice();e=[];for(let i of n)i!=null&&!isNaN(i=+i)&&e.push(i);return e.sort(Ct),r},r.interpolator=function(n){return arguments.length?(t=n,r):t},r.range=function(){return e.map((n,i)=>t(i/(e.length-1)))},r.quantiles=function(n){return Array.from({length:n+1},(i,a)=>gj(e,a/n))},r.copy=function(){return Ev(t).domain(e)},$t.apply(r,arguments)}function eo(){var e=0,t=.5,r=1,n=1,i,a,o,u,c,s=ke,f,l=!1,p;function h(d){return isNaN(d=+d)?p:(d=.5+((d=+f(d))-a)*(n*d<n*a?u:c),s(l?Math.max(0,Math.min(1,d)):d))}h.domain=function(d){return arguments.length?([e,t,r]=d,i=f(e=+e),a=f(t=+t),o=f(r=+r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,h):[e,t,r]},h.clamp=function(d){return arguments.length?(l=!!d,h):l},h.interpolator=function(d){return arguments.length?(s=d,h):s};function v(d){return function(y){var b,w,x;return arguments.length?([b,w,x]=y,s=qj(d,[b,w,x]),h):[s(0),s(.5),s(1)]}}return h.range=v(on),h.rangeRound=v(es),h.unknown=function(d){return arguments.length?(p=d,h):p},function(d){return f=d,i=d(e),a=d(t),o=d(r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,h}}function jv(){var e=kt(eo()(ke));return e.copy=function(){return Dt(e,jv())},$t.apply(e,arguments)}function Mv(){var e=is(eo()).domain([.1,1,10]);return e.copy=function(){return Dt(e,Mv()).base(e.base())},$t.apply(e,arguments)}function Cv(){var e=as(eo());return e.copy=function(){return Dt(e,Cv()).constant(e.constant())},$t.apply(e,arguments)}function ms(){var e=os(eo());return e.copy=function(){return Dt(e,ms()).exponent(e.exponent())},$t.apply(e,arguments)}function IC(){return ms.apply(null,arguments).exponent(.5)}const zf=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:Nn,scaleDiverging:jv,scaleDivergingLog:Mv,scaleDivergingPow:ms,scaleDivergingSqrt:IC,scaleDivergingSymlog:Cv,scaleIdentity:lv,scaleImplicit:Tu,scaleLinear:na,scaleLog:pv,scaleOrdinal:Zc,scalePoint:An,scalePow:us,scaleQuantile:vv,scaleQuantize:yv,scaleRadial:dv,scaleSequential:_v,scaleSequentialLog:$v,scaleSequentialPow:ys,scaleSequentialQuantile:Ev,scaleSequentialSqrt:CC,scaleSequentialSymlog:Tv,scaleSqrt:hM,scaleSymlog:hv,scaleThreshold:mv,scaleTime:jC,scaleUtc:MC,tickFormat:sv},Symbol.toStringTag,{value:"Module"}));var kC=Yr;function DC(e,t,r){for(var n=-1,i=e.length;++n<i;){var a=e[n],o=t(a);if(o!=null&&(u===void 0?o===o&&!kC(o):r(o,u)))var u=o,c=a}return c}var to=DC;function NC(e,t){return e>t}var Iv=NC,RC=to,LC=Iv,BC=an;function FC(e){return e&&e.length?RC(e,BC,LC):void 0}var WC=FC;const ro=ae(WC);function zC(e,t){return e<t}var kv=zC,UC=to,qC=kv,HC=an;function KC(e){return e&&e.length?UC(e,HC,qC):void 0}var GC=KC;const no=ae(GC);var VC=Cc,XC=pt,YC=zd,ZC=Le;function JC(e,t){var r=ZC(e)?VC:YC;return r(e,XC(t))}var QC=JC,eI=Fd,tI=QC;function rI(e,t){return eI(tI(e,t),1)}var nI=rI;const iI=ae(nI);var aI=Hc;function oI(e,t){return aI(e,t)}var uI=oI;const mi=ae(uI);var un=1e9,cI={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},bs,he=!0,Je="[DecimalError] ",Qt=Je+"Invalid argument: ",gs=Je+"Exponent out of range: ",cn=Math.floor,Kt=Math.pow,sI=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,qe,_e=1e7,pe=7,Dv=9007199254740991,ua=cn(Dv/pe),U={};U.absoluteValue=U.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e};U.comparedTo=U.cmp=function(e){var t,r,n,i,a=this;if(e=new a.constructor(e),a.s!==e.s)return a.s||-e.s;if(a.e!==e.e)return a.e>e.e^a.s<0?1:-1;for(n=a.d.length,i=e.d.length,t=0,r=n<i?n:i;t<r;++t)if(a.d[t]!==e.d[t])return a.d[t]>e.d[t]^a.s<0?1:-1;return n===i?0:n>i^a.s<0?1:-1};U.decimalPlaces=U.dp=function(){var e=this,t=e.d.length-1,r=(t-e.e)*pe;if(t=e.d[t],t)for(;t%10==0;t/=10)r--;return r<0?0:r};U.dividedBy=U.div=function(e){return xt(this,new this.constructor(e))};U.dividedToIntegerBy=U.idiv=function(e){var t=this,r=t.constructor;return oe(xt(t,new r(e),0,1),r.precision)};U.equals=U.eq=function(e){return!this.cmp(e)};U.exponent=function(){return ge(this)};U.greaterThan=U.gt=function(e){return this.cmp(e)>0};U.greaterThanOrEqualTo=U.gte=function(e){return this.cmp(e)>=0};U.isInteger=U.isint=function(){return this.e>this.d.length-2};U.isNegative=U.isneg=function(){return this.s<0};U.isPositive=U.ispos=function(){return this.s>0};U.isZero=function(){return this.s===0};U.lessThan=U.lt=function(e){return this.cmp(e)<0};U.lessThanOrEqualTo=U.lte=function(e){return this.cmp(e)<1};U.logarithm=U.log=function(e){var t,r=this,n=r.constructor,i=n.precision,a=i+5;if(e===void 0)e=new n(10);else if(e=new n(e),e.s<1||e.eq(qe))throw Error(Je+"NaN");if(r.s<1)throw Error(Je+(r.s?"NaN":"-Infinity"));return r.eq(qe)?new n(0):(he=!1,t=xt(Wn(r,a),Wn(e,a),a),he=!0,oe(t,i))};U.minus=U.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Lv(t,e):Nv(t,(e.s=-e.s,e))};U.modulo=U.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(e=new n(e),!e.s)throw Error(Je+"NaN");return r.s?(he=!1,t=xt(r,e,0,1).times(e),he=!0,r.minus(t)):oe(new n(r),i)};U.naturalExponential=U.exp=function(){return Rv(this)};U.naturalLogarithm=U.ln=function(){return Wn(this)};U.negated=U.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e};U.plus=U.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Nv(t,e):Lv(t,(e.s=-e.s,e))};U.precision=U.sd=function(e){var t,r,n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(Qt+e);if(t=ge(i)+1,n=i.d.length-1,r=n*pe+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r};U.squareRoot=U.sqrt=function(){var e,t,r,n,i,a,o,u=this,c=u.constructor;if(u.s<1){if(!u.s)return new c(0);throw Error(Je+"NaN")}for(e=ge(u),he=!1,i=Math.sqrt(+u),i==0||i==1/0?(t=ot(u.d),(t.length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=cn((e+1)/2)-(e<0||e%2),i==1/0?t="5e"+e:(t=i.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new c(t)):n=new c(i.toString()),r=c.precision,i=o=r+3;;)if(a=n,n=a.plus(xt(u,a,o+2)).times(.5),ot(a.d).slice(0,o)===(t=ot(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&t=="4999"){if(oe(a,r+1,0),a.times(a).eq(u)){n=a;break}}else if(t!="9999")break;o+=4}return he=!0,oe(n,r)};U.times=U.mul=function(e){var t,r,n,i,a,o,u,c,s,f=this,l=f.constructor,p=f.d,h=(e=new l(e)).d;if(!f.s||!e.s)return new l(0);for(e.s*=f.s,r=f.e+e.e,c=p.length,s=h.length,c<s&&(a=p,p=h,h=a,o=c,c=s,s=o),a=[],o=c+s,n=o;n--;)a.push(0);for(n=s;--n>=0;){for(t=0,i=c+n;i>n;)u=a[i]+h[n]*p[i-n-1]+t,a[i--]=u%_e|0,t=u/_e|0;a[i]=(a[i]+t)%_e|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,he?oe(e,l.precision):e};U.toDecimalPlaces=U.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(st(e,0,un),t===void 0?t=n.rounding:st(t,0,8),oe(r,e+ge(r)+1,t))};U.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=nr(n,!0):(st(e,0,un),t===void 0?t=i.rounding:st(t,0,8),n=oe(new i(n),e+1,t),r=nr(n,!0,e+1)),r};U.toFixed=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?nr(i):(st(e,0,un),t===void 0?t=a.rounding:st(t,0,8),n=oe(new a(i),e+ge(i)+1,t),r=nr(n.abs(),!1,e+ge(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};U.toInteger=U.toint=function(){var e=this,t=e.constructor;return oe(new t(e),ge(e)+1,t.rounding)};U.toNumber=function(){return+this};U.toPower=U.pow=function(e){var t,r,n,i,a,o,u=this,c=u.constructor,s=12,f=+(e=new c(e));if(!e.s)return new c(qe);if(u=new c(u),!u.s){if(e.s<1)throw Error(Je+"Infinity");return u}if(u.eq(qe))return u;if(n=c.precision,e.eq(qe))return oe(u,n);if(t=e.e,r=e.d.length-1,o=t>=r,a=u.s,o){if((r=f<0?-f:f)<=Dv){for(i=new c(qe),t=Math.ceil(n/pe+4),he=!1;r%2&&(i=i.times(u),qf(i.d,t)),r=cn(r/2),r!==0;)u=u.times(u),qf(u.d,t);return he=!0,e.s<0?new c(qe).div(i):oe(i,n)}}else if(a<0)throw Error(Je+"NaN");return a=a<0&&e.d[Math.max(t,r)]&1?-1:1,u.s=1,he=!1,i=e.times(Wn(u,n+s)),he=!0,i=Rv(i),i.s=a,i};U.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?(r=ge(i),n=nr(i,r<=a.toExpNeg||r>=a.toExpPos)):(st(e,1,un),t===void 0?t=a.rounding:st(t,0,8),i=oe(new a(i),e,t),r=ge(i),n=nr(i,e<=r||r<=a.toExpNeg,e)),n};U.toSignificantDigits=U.tosd=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(st(e,1,un),t===void 0?t=n.rounding:st(t,0,8)),oe(new n(r),e,t)};U.toString=U.valueOf=U.val=U.toJSON=U[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=ge(e),r=e.constructor;return nr(e,t<=r.toExpNeg||t>=r.toExpPos)};function Nv(e,t){var r,n,i,a,o,u,c,s,f=e.constructor,l=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),he?oe(t,l):t;if(c=e.d,s=t.d,o=e.e,i=t.e,c=c.slice(),a=o-i,a){for(a<0?(n=c,a=-a,u=s.length):(n=s,i=o,u=c.length),o=Math.ceil(l/pe),u=o>u?o+1:u+1,a>u&&(a=u,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for(u=c.length,a=s.length,u-a<0&&(a=u,n=s,s=c,c=n),r=0;a;)r=(c[--a]=c[a]+s[a]+r)/_e|0,c[a]%=_e;for(r&&(c.unshift(r),++i),u=c.length;c[--u]==0;)c.pop();return t.d=c,t.e=i,he?oe(t,l):t}function st(e,t,r){if(e!==~~e||e<t||e>r)throw Error(Qt+e)}function ot(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)n=e[t]+"",r=pe-n.length,r&&(a+=Et(r)),a+=n;o=e[t],n=o+"",r=pe-n.length,r&&(a+=Et(r))}else if(o===0)return"0";for(;o%10===0;)o/=10;return a+o}var xt=function(){function e(n,i){var a,o=0,u=n.length;for(n=n.slice();u--;)a=n[u]*i+o,n[u]=a%_e|0,o=a/_e|0;return o&&n.unshift(o),n}function t(n,i,a,o){var u,c;if(a!=o)c=a>o?1:-1;else for(u=c=0;u<a;u++)if(n[u]!=i[u]){c=n[u]>i[u]?1:-1;break}return c}function r(n,i,a){for(var o=0;a--;)n[a]-=o,o=n[a]<i[a]?1:0,n[a]=o*_e+n[a]-i[a];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,a,o){var u,c,s,f,l,p,h,v,d,y,b,w,x,A,m,g,O,P,_=n.constructor,E=n.s==i.s?1:-1,$=n.d,T=i.d;if(!n.s)return new _(n);if(!i.s)throw Error(Je+"Division by zero");for(c=n.e-i.e,O=T.length,m=$.length,h=new _(E),v=h.d=[],s=0;T[s]==($[s]||0);)++s;if(T[s]>($[s]||0)&&--c,a==null?w=a=_.precision:o?w=a+(ge(n)-ge(i))+1:w=a,w<0)return new _(0);if(w=w/pe+2|0,s=0,O==1)for(f=0,T=T[0],w++;(s<m||f)&&w--;s++)x=f*_e+($[s]||0),v[s]=x/T|0,f=x%T|0;else{for(f=_e/(T[0]+1)|0,f>1&&(T=e(T,f),$=e($,f),O=T.length,m=$.length),A=O,d=$.slice(0,O),y=d.length;y<O;)d[y++]=0;P=T.slice(),P.unshift(0),g=T[0],T[1]>=_e/2&&++g;do f=0,u=t(T,d,O,y),u<0?(b=d[0],O!=y&&(b=b*_e+(d[1]||0)),f=b/g|0,f>1?(f>=_e&&(f=_e-1),l=e(T,f),p=l.length,y=d.length,u=t(l,d,p,y),u==1&&(f--,r(l,O<p?P:T,p))):(f==0&&(u=f=1),l=T.slice()),p=l.length,p<y&&l.unshift(0),r(d,l,y),u==-1&&(y=d.length,u=t(T,d,O,y),u<1&&(f++,r(d,O<y?P:T,y))),y=d.length):u===0&&(f++,d=[0]),v[s++]=f,u&&d[0]?d[y++]=$[A]||0:(d=[$[A]],y=1);while((A++<m||d[0]!==void 0)&&w--)}return v[0]||v.shift(),h.e=c,oe(h,o?a+ge(h)+1:a)}}();function Rv(e,t){var r,n,i,a,o,u,c=0,s=0,f=e.constructor,l=f.precision;if(ge(e)>16)throw Error(gs+ge(e));if(!e.s)return new f(qe);for(he=!1,u=l,o=new f(.03125);e.abs().gte(.1);)e=e.times(o),s+=5;for(n=Math.log(Kt(2,s))/Math.LN10*2+5|0,u+=n,r=i=a=new f(qe),f.precision=u;;){if(i=oe(i.times(e),u),r=r.times(++c),o=a.plus(xt(i,r,u)),ot(o.d).slice(0,u)===ot(a.d).slice(0,u)){for(;s--;)a=oe(a.times(a),u);return f.precision=l,t==null?(he=!0,oe(a,l)):a}a=o}}function ge(e){for(var t=e.e*pe,r=e.d[0];r>=10;r/=10)t++;return t}function Uo(e,t,r){if(t>e.LN10.sd())throw he=!0,r&&(e.precision=r),Error(Je+"LN10 precision limit exceeded");return oe(new e(e.LN10),t)}function Et(e){for(var t="";e--;)t+="0";return t}function Wn(e,t){var r,n,i,a,o,u,c,s,f,l=1,p=10,h=e,v=h.d,d=h.constructor,y=d.precision;if(h.s<1)throw Error(Je+(h.s?"NaN":"-Infinity"));if(h.eq(qe))return new d(0);if(t==null?(he=!1,s=y):s=t,h.eq(10))return t==null&&(he=!0),Uo(d,s);if(s+=p,d.precision=s,r=ot(v),n=r.charAt(0),a=ge(h),Math.abs(a)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)h=h.times(e),r=ot(h.d),n=r.charAt(0),l++;a=ge(h),n>1?(h=new d("0."+r),a++):h=new d(n+"."+r.slice(1))}else return c=Uo(d,s+2,y).times(a+""),h=Wn(new d(n+"."+r.slice(1)),s-p).plus(c),d.precision=y,t==null?(he=!0,oe(h,y)):h;for(u=o=h=xt(h.minus(qe),h.plus(qe),s),f=oe(h.times(h),s),i=3;;){if(o=oe(o.times(f),s),c=u.plus(xt(o,new d(i),s)),ot(c.d).slice(0,s)===ot(u.d).slice(0,s))return u=u.times(2),a!==0&&(u=u.plus(Uo(d,s+2,y).times(a+""))),u=xt(u,new d(l),s),d.precision=y,t==null?(he=!0,oe(u,y)):u;u=c,i+=2}}function Uf(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;)++n;for(i=t.length;t.charCodeAt(i-1)===48;)--i;if(t=t.slice(n,i),t){if(i-=n,r=r-n-1,e.e=cn(r/pe),e.d=[],n=(r+1)%pe,r<0&&(n+=pe),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=pe;n<i;)e.d.push(+t.slice(n,n+=pe));t=t.slice(n),n=pe-t.length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),he&&(e.e>ua||e.e<-ua))throw Error(gs+r)}else e.s=0,e.e=0,e.d=[0];return e}function oe(e,t,r){var n,i,a,o,u,c,s,f,l=e.d;for(o=1,a=l[0];a>=10;a/=10)o++;if(n=t-o,n<0)n+=pe,i=t,s=l[f=0];else{if(f=Math.ceil((n+1)/pe),a=l.length,f>=a)return e;for(s=a=l[f],o=1;a>=10;a/=10)o++;n%=pe,i=n-pe+o}if(r!==void 0&&(a=Kt(10,o-i-1),u=s/a%10|0,c=t<0||l[f+1]!==void 0||s%a,c=r<4?(u||c)&&(r==0||r==(e.s<0?3:2)):u>5||u==5&&(r==4||c||r==6&&(n>0?i>0?s/Kt(10,o-i):0:l[f-1])%10&1||r==(e.s<0?8:7))),t<1||!l[0])return c?(a=ge(e),l.length=1,t=t-a-1,l[0]=Kt(10,(pe-t%pe)%pe),e.e=cn(-t/pe)||0):(l.length=1,l[0]=e.e=e.s=0),e;if(n==0?(l.length=f,a=1,f--):(l.length=f+1,a=Kt(10,pe-n),l[f]=i>0?(s/Kt(10,o-i)%Kt(10,i)|0)*a:0),c)for(;;)if(f==0){(l[0]+=a)==_e&&(l[0]=1,++e.e);break}else{if(l[f]+=a,l[f]!=_e)break;l[f--]=0,a=1}for(n=l.length;l[--n]===0;)l.pop();if(he&&(e.e>ua||e.e<-ua))throw Error(gs+ge(e));return e}function Lv(e,t){var r,n,i,a,o,u,c,s,f,l,p=e.constructor,h=p.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new p(e),he?oe(t,h):t;if(c=e.d,l=t.d,n=t.e,s=e.e,c=c.slice(),o=s-n,o){for(f=o<0,f?(r=c,o=-o,u=l.length):(r=l,n=s,u=c.length),i=Math.max(Math.ceil(h/pe),u)+2,o>i&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for(i=c.length,u=l.length,f=i<u,f&&(u=i),i=0;i<u;i++)if(c[i]!=l[i]){f=c[i]<l[i];break}o=0}for(f&&(r=c,c=l,l=r,t.s=-t.s),u=c.length,i=l.length-u;i>0;--i)c[u++]=0;for(i=l.length;i>o;){if(c[--i]<l[i]){for(a=i;a&&c[--a]===0;)c[a]=_e-1;--c[a],c[i]+=_e}c[i]-=l[i]}for(;c[--u]===0;)c.pop();for(;c[0]===0;c.shift())--n;return c[0]?(t.d=c,t.e=n,he?oe(t,h):t):new p(0)}function nr(e,t,r){var n,i=ge(e),a=ot(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+Et(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+Et(-i-1)+a,r&&(n=r-o)>0&&(a+=Et(n))):i>=o?(a+=Et(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+Et(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=Et(n))),e.s<0?"-"+a:a}function qf(e,t){if(e.length>t)return e.length=t,!0}function Bv(e){var t,r,n;function i(a){var o=this;if(!(o instanceof i))return new i(a);if(o.constructor=i,a instanceof i){o.s=a.s,o.e=a.e,o.d=(a=a.d)?a.slice():a;return}if(typeof a=="number"){if(a*0!==0)throw Error(Qt+a);if(a>0)o.s=1;else if(a<0)a=-a,o.s=-1;else{o.s=0,o.e=0,o.d=[0];return}if(a===~~a&&a<1e7){o.e=0,o.d=[a];return}return Uf(o,a.toString())}else if(typeof a!="string")throw Error(Qt+a);if(a.charCodeAt(0)===45?(a=a.slice(1),o.s=-1):o.s=1,sI.test(a))Uf(o,a);else throw Error(Qt+a)}if(i.prototype=U,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=Bv,i.config=i.set=lI,e===void 0&&(e={}),e)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function lI(e){if(!e||typeof e!="object")throw Error(Je+"Object expected");var t,r,n,i=["precision",1,un,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if((n=e[r=i[t]])!==void 0)if(cn(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(Qt+r+": "+n);if((n=e[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(Qt+r+": "+n);return this}var bs=Bv(cI);qe=new bs(1);const ie=bs;function fI(e){return vI(e)||dI(e)||hI(e)||pI()}function pI(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function hI(e,t){if(e){if(typeof e=="string")return Cu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Cu(e,t)}}function dI(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function vI(e){if(Array.isArray(e))return Cu(e)}function Cu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var yI=function(t){return t},Fv={},Wv=function(t){return t===Fv},Hf=function(t){return function r(){return arguments.length===0||arguments.length===1&&Wv(arguments.length<=0?void 0:arguments[0])?r:t.apply(void 0,arguments)}},mI=function e(t,r){return t===1?r:Hf(function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];var o=i.filter(function(u){return u!==Fv}).length;return o>=t?r.apply(void 0,i):e(t-o,Hf(function(){for(var u=arguments.length,c=new Array(u),s=0;s<u;s++)c[s]=arguments[s];var f=i.map(function(l){return Wv(l)?c.shift():l});return r.apply(void 0,fI(f).concat(c))}))})},io=function(t){return mI(t.length,t)},Iu=function(t,r){for(var n=[],i=t;i<r;++i)n[i-t]=i;return n},gI=io(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(r){return t[r]}).map(e)}),bI=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];if(!r.length)return yI;var i=r.reverse(),a=i[0],o=i.slice(1);return function(){return o.reduce(function(u,c){return c(u)},a.apply(void 0,arguments))}},ku=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},zv=function(t){var r=null,n=null;return function(){for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return r&&a.every(function(u,c){return u===r[c]})||(r=a,n=t.apply(void 0,a)),n}};function xI(e){var t;return e===0?t=1:t=Math.floor(new ie(e).abs().log(10).toNumber())+1,t}function wI(e,t,r){for(var n=new ie(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}var OI=io(function(e,t,r){var n=+e,i=+t;return n+r*(i-n)}),AI=io(function(e,t,r){var n=t-+e;return n=n||1/0,(r-e)/n}),SI=io(function(e,t,r){var n=t-+e;return n=n||1/0,Math.max(0,Math.min(1,(r-e)/n))});const ao={rangeStep:wI,getDigitCount:xI,interpolateNumber:OI,uninterpolateNumber:AI,uninterpolateTruncation:SI};function Du(e){return $I(e)||_I(e)||Uv(e)||PI()}function PI(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function _I(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function $I(e){if(Array.isArray(e))return Nu(e)}function zn(e,t){return jI(e)||EI(e,t)||Uv(e,t)||TI()}function TI(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Uv(e,t){if(e){if(typeof e=="string")return Nu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Nu(e,t)}}function Nu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function EI(e,t){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(e)))){var r=[],n=!0,i=!1,a=void 0;try{for(var o=e[Symbol.iterator](),u;!(n=(u=o.next()).done)&&(r.push(u.value),!(t&&r.length===t));n=!0);}catch(c){i=!0,a=c}finally{try{!n&&o.return!=null&&o.return()}finally{if(i)throw a}}return r}}function jI(e){if(Array.isArray(e))return e}function qv(e){var t=zn(e,2),r=t[0],n=t[1],i=r,a=n;return r>n&&(i=n,a=r),[i,a]}function Hv(e,t,r){if(e.lte(0))return new ie(0);var n=ao.getDigitCount(e.toNumber()),i=new ie(10).pow(n),a=e.div(i),o=n!==1?.05:.1,u=new ie(Math.ceil(a.div(o).toNumber())).add(r).mul(o),c=u.mul(i);return t?c:new ie(Math.ceil(c))}function MI(e,t,r){var n=1,i=new ie(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new ie(10).pow(ao.getDigitCount(e)-1),i=new ie(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new ie(Math.floor(e)))}else e===0?i=new ie(Math.floor((t-1)/2)):r||(i=new ie(Math.floor(e)));var o=Math.floor((t-1)/2),u=bI(gI(function(c){return i.add(new ie(c-o).mul(n)).toNumber()}),Iu);return u(0,t)}function Kv(e,t,r,n){var i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new ie(0),tickMin:new ie(0),tickMax:new ie(0)};var a=Hv(new ie(t).sub(e).div(r-1),n,i),o;e<=0&&t>=0?o=new ie(0):(o=new ie(e).add(t).div(2),o=o.sub(new ie(o).mod(a)));var u=Math.ceil(o.sub(e).div(a).toNumber()),c=Math.ceil(new ie(t).sub(o).div(a).toNumber()),s=u+c+1;return s>r?Kv(e,t,r,n,i+1):(s<r&&(c=t>0?c+(r-s):c,u=t>0?u:u+(r-s)),{step:a,tickMin:o.sub(new ie(u).mul(a)),tickMax:o.add(new ie(c).mul(a))})}function CI(e){var t=zn(e,2),r=t[0],n=t[1],i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:6,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Math.max(i,2),u=qv([r,n]),c=zn(u,2),s=c[0],f=c[1];if(s===-1/0||f===1/0){var l=f===1/0?[s].concat(Du(Iu(0,i-1).map(function(){return 1/0}))):[].concat(Du(Iu(0,i-1).map(function(){return-1/0})),[f]);return r>n?ku(l):l}if(s===f)return MI(s,i,a);var p=Kv(s,f,o,a),h=p.step,v=p.tickMin,d=p.tickMax,y=ao.rangeStep(v,d.add(new ie(.1).mul(h)),h);return r>n?ku(y):y}function II(e,t){var r=zn(e,2),n=r[0],i=r[1],a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=qv([n,i]),u=zn(o,2),c=u[0],s=u[1];if(c===-1/0||s===1/0)return[n,i];if(c===s)return[c];var f=Math.max(t,2),l=Hv(new ie(s).sub(c).div(f-1),a,0),p=[].concat(Du(ao.rangeStep(new ie(c),new ie(s).sub(new ie(.99).mul(l)),l)),[s]);return n>i?ku(p):p}var kI=zv(CI),DI=zv(II),NI="Invariant failed";function ir(e,t){throw new Error(NI)}var RI=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function Ir(e){"@babel/helpers - typeof";return Ir=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ir(e)}function ca(){return ca=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ca.apply(this,arguments)}function LI(e,t){return zI(e)||WI(e,t)||FI(e,t)||BI()}function BI(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function FI(e,t){if(e){if(typeof e=="string")return Kf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Kf(e,t)}}function Kf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function WI(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function zI(e){if(Array.isArray(e))return e}function UI(e,t){if(e==null)return{};var r=qI(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function qI(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function HI(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function KI(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Xv(n.key),n)}}function GI(e,t,r){return t&&KI(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function VI(e,t,r){return t=sa(t),XI(e,Gv()?Reflect.construct(t,r||[],sa(e).constructor):t.apply(e,r))}function XI(e,t){if(t&&(Ir(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return YI(e)}function YI(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Gv(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Gv=function(){return!!e})()}function sa(e){return sa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},sa(e)}function ZI(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ru(e,t)}function Ru(e,t){return Ru=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ru(e,t)}function Vv(e,t,r){return t=Xv(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Xv(e){var t=JI(e,"string");return Ir(t)=="symbol"?t:t+""}function JI(e,t){if(Ir(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ir(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var gi=function(e){function t(){return HI(this,t),VI(this,t,arguments)}return ZI(t,e),GI(t,[{key:"render",value:function(){var n=this.props,i=n.offset,a=n.layout,o=n.width,u=n.dataKey,c=n.data,s=n.dataPointFormatter,f=n.xAxis,l=n.yAxis,p=UI(n,RI),h=H(p,!1);this.props.direction==="x"&&f.type!=="number"&&ir();var v=c.map(function(d){var y=s(d,u),b=y.x,w=y.y,x=y.value,A=y.errorVal;if(!A)return null;var m=[],g,O;if(Array.isArray(A)){var P=LI(A,2);g=P[0],O=P[1]}else g=O=A;if(a==="vertical"){var _=f.scale,E=w+i,$=E+o,T=E-o,C=_(x-g),I=_(x+O);m.push({x1:I,y1:$,x2:I,y2:T}),m.push({x1:C,y1:E,x2:I,y2:E}),m.push({x1:C,y1:$,x2:C,y2:T})}else if(a==="horizontal"){var M=l.scale,k=b+i,D=k-o,B=k+o,F=M(x-g),q=M(x+O);m.push({x1:D,y1:q,x2:B,y2:q}),m.push({x1:k,y1:F,x2:k,y2:q}),m.push({x1:D,y1:F,x2:B,y2:F})}return S.createElement(te,ca({className:"recharts-errorBar",key:"bar-".concat(m.map(function(G){return"".concat(G.x1,"-").concat(G.x2,"-").concat(G.y1,"-").concat(G.y2)}))},h),m.map(function(G){return S.createElement("line",ca({},G,{key:"line-".concat(G.x1,"-").concat(G.x2,"-").concat(G.y1,"-").concat(G.y2)}))}))});return S.createElement(te,{className:"recharts-errorBars"},v)}}])}(S.Component);Vv(gi,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"});Vv(gi,"displayName","ErrorBar");function Un(e){"@babel/helpers - typeof";return Un=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Un(e)}function Gf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Wt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Gf(Object(r),!0).forEach(function(n){QI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Gf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function QI(e,t,r){return t=ek(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ek(e){var t=tk(e,"string");return Un(t)=="symbol"?t:t+""}function tk(e,t){if(Un(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Un(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Yv=function(t){var r=t.children,n=t.formattedGraphicalItems,i=t.legendWidth,a=t.legendContent,o=ze(r,Or);if(!o)return null;var u=Or.defaultProps,c=u!==void 0?Wt(Wt({},u),o.props):{},s;return o.props&&o.props.payload?s=o.props&&o.props.payload:a==="children"?s=(n||[]).reduce(function(f,l){var p=l.item,h=l.props,v=h.sectors||h.data||[];return f.concat(v.map(function(d){return{type:o.props.iconType||p.props.legendType,value:d.name,color:d.fill,payload:d}}))},[]):s=(n||[]).map(function(f){var l=f.item,p=l.type.defaultProps,h=p!==void 0?Wt(Wt({},p),l.props):{},v=h.dataKey,d=h.name,y=h.legendType,b=h.hide;return{inactive:b,dataKey:v,type:c.iconType||y||"square",color:xs(l),value:d||v,payload:h}}),Wt(Wt(Wt({},c),Or.getWithHeight(o,i)),{},{payload:s,item:o})};function qn(e){"@babel/helpers - typeof";return qn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},qn(e)}function Vf(e){return ak(e)||ik(e)||nk(e)||rk()}function rk(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function nk(e,t){if(e){if(typeof e=="string")return Lu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Lu(e,t)}}function ik(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function ak(e){if(Array.isArray(e))return Lu(e)}function Lu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Xf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ve(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Xf(Object(r),!0).forEach(function(n){Sr(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Xf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Sr(e,t,r){return t=ok(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ok(e){var t=uk(e,"string");return qn(t)=="symbol"?t:t+""}function uk(e,t){if(qn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(qn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Ae(e,t,r){return Y(e)||Y(t)?r:Se(t)?He(e,t,r):X(t)?t(e):r}function Sn(e,t,r,n){var i=iI(e,function(u){return Ae(u,t)});if(r==="number"){var a=i.filter(function(u){return L(u)||parseFloat(u)});return a.length?[no(a),ro(a)]:[1/0,-1/0]}var o=n?i.filter(function(u){return!Y(u)}):i;return o.map(function(u){return Se(u)||u instanceof Date?u:""})}var ck=function(t){var r,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],i=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0,o=-1,u=(r=n==null?void 0:n.length)!==null&&r!==void 0?r:0;if(u<=1)return 0;if(a&&a.axisType==="angleAxis"&&Math.abs(Math.abs(a.range[1]-a.range[0])-360)<=1e-6)for(var c=a.range,s=0;s<u;s++){var f=s>0?i[s-1].coordinate:i[u-1].coordinate,l=i[s].coordinate,p=s>=u-1?i[0].coordinate:i[s+1].coordinate,h=void 0;if(Ce(l-f)!==Ce(p-l)){var v=[];if(Ce(p-l)===Ce(c[1]-c[0])){h=p;var d=l+c[1]-c[0];v[0]=Math.min(d,(d+f)/2),v[1]=Math.max(d,(d+f)/2)}else{h=f;var y=p+c[1]-c[0];v[0]=Math.min(l,(y+l)/2),v[1]=Math.max(l,(y+l)/2)}var b=[Math.min(l,(h+l)/2),Math.max(l,(h+l)/2)];if(t>b[0]&&t<=b[1]||t>=v[0]&&t<=v[1]){o=i[s].index;break}}else{var w=Math.min(f,p),x=Math.max(f,p);if(t>(w+l)/2&&t<=(x+l)/2){o=i[s].index;break}}}else for(var A=0;A<u;A++)if(A===0&&t<=(n[A].coordinate+n[A+1].coordinate)/2||A>0&&A<u-1&&t>(n[A].coordinate+n[A-1].coordinate)/2&&t<=(n[A].coordinate+n[A+1].coordinate)/2||A===u-1&&t>(n[A].coordinate+n[A-1].coordinate)/2){o=n[A].index;break}return o},xs=function(t){var r,n=t,i=n.type.displayName,a=(r=t.type)!==null&&r!==void 0&&r.defaultProps?ve(ve({},t.type.defaultProps),t.props):t.props,o=a.stroke,u=a.fill,c;switch(i){case"Line":c=o;break;case"Area":case"Radar":c=o&&o!=="none"?o:u;break;default:c=u;break}return c},sk=function(t){var r=t.barSize,n=t.totalSize,i=t.stackGroups,a=i===void 0?{}:i;if(!a)return{};for(var o={},u=Object.keys(a),c=0,s=u.length;c<s;c++)for(var f=a[u[c]].stackGroups,l=Object.keys(f),p=0,h=l.length;p<h;p++){var v=f[l[p]],d=v.items,y=v.cateAxisId,b=d.filter(function(O){return bt(O.type).indexOf("Bar")>=0});if(b&&b.length){var w=b[0].type.defaultProps,x=w!==void 0?ve(ve({},w),b[0].props):b[0].props,A=x.barSize,m=x[y];o[m]||(o[m]=[]);var g=Y(A)?r:A;o[m].push({item:b[0],stackList:b.slice(1),barSize:Y(g)?void 0:Ie(g,n,0)})}}return o},lk=function(t){var r=t.barGap,n=t.barCategoryGap,i=t.bandSize,a=t.sizeList,o=a===void 0?[]:a,u=t.maxBarSize,c=o.length;if(c<1)return null;var s=Ie(r,i,0,!0),f,l=[];if(o[0].barSize===+o[0].barSize){var p=!1,h=i/c,v=o.reduce(function(A,m){return A+m.barSize||0},0);v+=(c-1)*s,v>=i&&(v-=(c-1)*s,s=0),v>=i&&h>0&&(p=!0,h*=.9,v=c*h);var d=(i-v)/2>>0,y={offset:d-s,size:0};f=o.reduce(function(A,m){var g={item:m.item,position:{offset:y.offset+y.size+s,size:p?h:m.barSize}},O=[].concat(Vf(A),[g]);return y=O[O.length-1].position,m.stackList&&m.stackList.length&&m.stackList.forEach(function(P){O.push({item:P,position:y})}),O},l)}else{var b=Ie(n,i,0,!0);i-2*b-(c-1)*s<=0&&(s=0);var w=(i-2*b-(c-1)*s)/c;w>1&&(w>>=0);var x=u===+u?Math.min(w,u):w;f=o.reduce(function(A,m,g){var O=[].concat(Vf(A),[{item:m.item,position:{offset:b+(w+s)*g+(w-x)/2,size:x}}]);return m.stackList&&m.stackList.length&&m.stackList.forEach(function(P){O.push({item:P,position:O[O.length-1].position})}),O},l)}return f},fk=function(t,r,n,i){var a=n.children,o=n.width,u=n.margin,c=o-(u.left||0)-(u.right||0),s=Yv({children:a,legendWidth:c});if(s){var f=i||{},l=f.width,p=f.height,h=s.align,v=s.verticalAlign,d=s.layout;if((d==="vertical"||d==="horizontal"&&v==="middle")&&h!=="center"&&L(t[h]))return ve(ve({},t),{},Sr({},h,t[h]+(l||0)));if((d==="horizontal"||d==="vertical"&&h==="center")&&v!=="middle"&&L(t[v]))return ve(ve({},t),{},Sr({},v,t[v]+(p||0)))}return t},pk=function(t,r,n){return Y(r)?!0:t==="horizontal"?r==="yAxis":t==="vertical"||n==="x"?r==="xAxis":n==="y"?r==="yAxis":!0},Zv=function(t,r,n,i,a){var o=r.props.children,u=Ke(o,gi).filter(function(s){return pk(i,a,s.props.direction)});if(u&&u.length){var c=u.map(function(s){return s.props.dataKey});return t.reduce(function(s,f){var l=Ae(f,n);if(Y(l))return s;var p=Array.isArray(l)?[no(l),ro(l)]:[l,l],h=c.reduce(function(v,d){var y=Ae(f,d,0),b=p[0]-Math.abs(Array.isArray(y)?y[0]:y),w=p[1]+Math.abs(Array.isArray(y)?y[1]:y);return[Math.min(b,v[0]),Math.max(w,v[1])]},[1/0,-1/0]);return[Math.min(h[0],s[0]),Math.max(h[1],s[1])]},[1/0,-1/0])}return null},hk=function(t,r,n,i,a){var o=r.map(function(u){return Zv(t,u,n,a,i)}).filter(function(u){return!Y(u)});return o&&o.length?o.reduce(function(u,c){return[Math.min(u[0],c[0]),Math.max(u[1],c[1])]},[1/0,-1/0]):null},Jv=function(t,r,n,i,a){var o=r.map(function(c){var s=c.props.dataKey;return n==="number"&&s&&Zv(t,c,s,i)||Sn(t,s,n,a)});if(n==="number")return o.reduce(function(c,s){return[Math.min(c[0],s[0]),Math.max(c[1],s[1])]},[1/0,-1/0]);var u={};return o.reduce(function(c,s){for(var f=0,l=s.length;f<l;f++)u[s[f]]||(u[s[f]]=!0,c.push(s[f]));return c},[])},Qv=function(t,r){return t==="horizontal"&&r==="xAxis"||t==="vertical"&&r==="yAxis"||t==="centric"&&r==="angleAxis"||t==="radial"&&r==="radiusAxis"},ey=function(t,r,n,i){if(i)return t.map(function(c){return c.coordinate});var a,o,u=t.map(function(c){return c.coordinate===r&&(a=!0),c.coordinate===n&&(o=!0),c.coordinate});return a||u.push(r),o||u.push(n),u},gt=function(t,r,n){if(!t)return null;var i=t.scale,a=t.duplicateDomain,o=t.type,u=t.range,c=t.realScaleType==="scaleBand"?i.bandwidth()/2:2,s=(r||n)&&o==="category"&&i.bandwidth?i.bandwidth()/c:0;if(s=t.axisType==="angleAxis"&&(u==null?void 0:u.length)>=2?Ce(u[0]-u[1])*2*s:s,r&&(t.ticks||t.niceTicks)){var f=(t.ticks||t.niceTicks).map(function(l){var p=a?a.indexOf(l):l;return{coordinate:i(p)+s,value:l,offset:s}});return f.filter(function(l){return!fi(l.coordinate)})}return t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(l,p){return{coordinate:i(l)+s,value:l,index:p,offset:s}}):i.ticks&&!n?i.ticks(t.tickCount).map(function(l){return{coordinate:i(l)+s,value:l,offset:s}}):i.domain().map(function(l,p){return{coordinate:i(l)+s,value:a?a[l]:l,index:p,offset:s}})},qo=new WeakMap,Ei=function(t,r){if(typeof r!="function")return t;qo.has(t)||qo.set(t,new WeakMap);var n=qo.get(t);if(n.has(r))return n.get(r);var i=function(){t.apply(void 0,arguments),r.apply(void 0,arguments)};return n.set(r,i),i},ty=function(t,r,n){var i=t.scale,a=t.type,o=t.layout,u=t.axisType;if(i==="auto")return o==="radial"&&u==="radiusAxis"?{scale:Nn(),realScaleType:"band"}:o==="radial"&&u==="angleAxis"?{scale:na(),realScaleType:"linear"}:a==="category"&&r&&(r.indexOf("LineChart")>=0||r.indexOf("AreaChart")>=0||r.indexOf("ComposedChart")>=0&&!n)?{scale:An(),realScaleType:"point"}:a==="category"?{scale:Nn(),realScaleType:"band"}:{scale:na(),realScaleType:"linear"};if(er(i)){var c="scale".concat(za(i));return{scale:(zf[c]||An)(),realScaleType:zf[c]?c:"point"}}return X(i)?{scale:i}:{scale:An(),realScaleType:"point"}},Yf=1e-4,ry=function(t){var r=t.domain();if(!(!r||r.length<=2)){var n=r.length,i=t.range(),a=Math.min(i[0],i[1])-Yf,o=Math.max(i[0],i[1])+Yf,u=t(r[0]),c=t(r[n-1]);(u<a||u>o||c<a||c>o)&&t.domain([r[0],r[n-1]])}},dk=function(t,r){if(!t)return null;for(var n=0,i=t.length;n<i;n++)if(t[n].item===r)return t[n].position;return null},vk=function(t,r){if(!r||r.length!==2||!L(r[0])||!L(r[1]))return t;var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]),a=[t[0],t[1]];return(!L(t[0])||t[0]<n)&&(a[0]=n),(!L(t[1])||t[1]>i)&&(a[1]=i),a[0]>i&&(a[0]=i),a[1]<n&&(a[1]=n),a},yk=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0,u=0;u<r;++u){var c=fi(t[u][n][1])?t[u][n][0]:t[u][n][1];c>=0?(t[u][n][0]=a,t[u][n][1]=a+c,a=t[u][n][1]):(t[u][n][0]=o,t[u][n][1]=o+c,o=t[u][n][1])}},mk=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0;o<r;++o){var u=fi(t[o][n][1])?t[o][n][0]:t[o][n][1];u>=0?(t[o][n][0]=a,t[o][n][1]=a+u,a=t[o][n][1]):(t[o][n][0]=0,t[o][n][1]=0)}},gk={sign:yk,expand:Rw,none:Pr,silhouette:Lw,wiggle:Bw,positive:mk},bk=function(t,r,n){var i=r.map(function(u){return u.props.dataKey}),a=gk[n],o=Nw().keys(i).value(function(u,c){return+Ae(u,c,0)}).order(su).offset(a);return o(t)},xk=function(t,r,n,i,a,o){if(!t)return null;var u=o?r.reverse():r,c={},s=u.reduce(function(l,p){var h,v=(h=p.type)!==null&&h!==void 0&&h.defaultProps?ve(ve({},p.type.defaultProps),p.props):p.props,d=v.stackId,y=v.hide;if(y)return l;var b=v[n],w=l[b]||{hasStack:!1,stackGroups:{}};if(Se(d)){var x=w.stackGroups[d]||{numericAxisId:n,cateAxisId:i,items:[]};x.items.push(p),w.hasStack=!0,w.stackGroups[d]=x}else w.stackGroups[tn("_stackId_")]={numericAxisId:n,cateAxisId:i,items:[p]};return ve(ve({},l),{},Sr({},b,w))},c),f={};return Object.keys(s).reduce(function(l,p){var h=s[p];if(h.hasStack){var v={};h.stackGroups=Object.keys(h.stackGroups).reduce(function(d,y){var b=h.stackGroups[y];return ve(ve({},d),{},Sr({},y,{numericAxisId:n,cateAxisId:i,items:b.items,stackedData:bk(t,b.items,a)}))},v)}return ve(ve({},l),{},Sr({},p,h))},f)},ny=function(t,r){var n=r.realScaleType,i=r.type,a=r.tickCount,o=r.originalDomain,u=r.allowDecimals,c=n||r.scale;if(c!=="auto"&&c!=="linear")return null;if(a&&i==="number"&&o&&(o[0]==="auto"||o[1]==="auto")){var s=t.domain();if(!s.length)return null;var f=kI(s,a,u);return t.domain([no(f),ro(f)]),{niceTicks:f}}if(a&&i==="number"){var l=t.domain(),p=DI(l,a,u);return{niceTicks:p}}return null};function Zf(e){var t=e.axis,r=e.ticks,n=e.bandSize,i=e.entry,a=e.index,o=e.dataKey;if(t.type==="category"){if(!t.allowDuplicatedCategory&&t.dataKey&&!Y(i[t.dataKey])){var u=Ni(r,"value",i[t.dataKey]);if(u)return u.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var c=Ae(i,Y(o)?t.dataKey:o);return Y(c)?null:t.scale(c)}var Jf=function(t){var r=t.axis,n=t.ticks,i=t.offset,a=t.bandSize,o=t.entry,u=t.index;if(r.type==="category")return n[u]?n[u].coordinate+i:null;var c=Ae(o,r.dataKey,r.domain[u]);return Y(c)?null:r.scale(c)-a/2+i},wk=function(t){var r=t.numericAxis,n=r.scale.domain();if(r.type==="number"){var i=Math.min(n[0],n[1]),a=Math.max(n[0],n[1]);return i<=0&&a>=0?0:a<0?a:i}return n[0]},Ok=function(t,r){var n,i=(n=t.type)!==null&&n!==void 0&&n.defaultProps?ve(ve({},t.type.defaultProps),t.props):t.props,a=i.stackId;if(Se(a)){var o=r[a];if(o){var u=o.items.indexOf(t);return u>=0?o.stackedData[u]:null}}return null},Ak=function(t){return t.reduce(function(r,n){return[no(n.concat([r[0]]).filter(L)),ro(n.concat([r[1]]).filter(L))]},[1/0,-1/0])},iy=function(t,r,n){return Object.keys(t).reduce(function(i,a){var o=t[a],u=o.stackedData,c=u.reduce(function(s,f){var l=Ak(f.slice(r,n+1));return[Math.min(s[0],l[0]),Math.max(s[1],l[1])]},[1/0,-1/0]);return[Math.min(c[0],i[0]),Math.max(c[1],i[1])]},[1/0,-1/0]).map(function(i){return i===1/0||i===-1/0?0:i})},Qf=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,ep=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Bu=function(t,r,n){if(X(t))return t(r,n);if(!Array.isArray(t))return r;var i=[];if(L(t[0]))i[0]=n?t[0]:Math.min(t[0],r[0]);else if(Qf.test(t[0])){var a=+Qf.exec(t[0])[1];i[0]=r[0]-a}else X(t[0])?i[0]=t[0](r[0]):i[0]=r[0];if(L(t[1]))i[1]=n?t[1]:Math.max(t[1],r[1]);else if(ep.test(t[1])){var o=+ep.exec(t[1])[1];i[1]=r[1]+o}else X(t[1])?i[1]=t[1](r[1]):i[1]=r[1];return i},la=function(t,r,n){if(t&&t.scale&&t.scale.bandwidth){var i=t.scale.bandwidth();if(!n||i>0)return i}if(t&&r&&r.length>=2){for(var a=Gc(r,function(l){return l.coordinate}),o=1/0,u=1,c=a.length;u<c;u++){var s=a[u],f=a[u-1];o=Math.min((s.coordinate||0)-(f.coordinate||0),o)}return o===1/0?0:o}return n?void 0:0},tp=function(t,r,n){return!t||!t.length||mi(t,He(n,"type.defaultProps.domain"))?r:t},ay=function(t,r){var n=t.type.defaultProps?ve(ve({},t.type.defaultProps),t.props):t.props,i=n.dataKey,a=n.name,o=n.unit,u=n.formatter,c=n.tooltipType,s=n.chartType,f=n.hide;return ve(ve({},H(t,!1)),{},{dataKey:i,unit:o,formatter:u,name:a||i,color:xs(t),value:Ae(r,i),type:c,payload:r,chartType:s,hide:f})};function Hn(e){"@babel/helpers - typeof";return Hn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Hn(e)}function rp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function vt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?rp(Object(r),!0).forEach(function(n){oy(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function oy(e,t,r){return t=Sk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Sk(e){var t=Pk(e,"string");return Hn(t)=="symbol"?t:t+""}function Pk(e,t){if(Hn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Hn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function _k(e,t){return jk(e)||Ek(e,t)||Tk(e,t)||$k()}function $k(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Tk(e,t){if(e){if(typeof e=="string")return np(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return np(e,t)}}function np(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Ek(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function jk(e){if(Array.isArray(e))return e}var fa=Math.PI/180,Mk=function(t){return t*180/Math.PI},se=function(t,r,n,i){return{x:t+Math.cos(-fa*i)*n,y:r+Math.sin(-fa*i)*n}},uy=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(n.left||0)-(n.right||0)),Math.abs(r-(n.top||0)-(n.bottom||0)))/2},Ck=function(t,r,n,i,a){var o=t.width,u=t.height,c=t.startAngle,s=t.endAngle,f=Ie(t.cx,o,o/2),l=Ie(t.cy,u,u/2),p=uy(o,u,n),h=Ie(t.innerRadius,p,0),v=Ie(t.outerRadius,p,p*.8),d=Object.keys(r);return d.reduce(function(y,b){var w=r[b],x=w.domain,A=w.reversed,m;if(Y(w.range))i==="angleAxis"?m=[c,s]:i==="radiusAxis"&&(m=[h,v]),A&&(m=[m[1],m[0]]);else{m=w.range;var g=m,O=_k(g,2);c=O[0],s=O[1]}var P=ty(w,a),_=P.realScaleType,E=P.scale;E.domain(x).range(m),ry(E);var $=ny(E,vt(vt({},w),{},{realScaleType:_})),T=vt(vt(vt({},w),$),{},{range:m,radius:v,realScaleType:_,scale:E,cx:f,cy:l,innerRadius:h,outerRadius:v,startAngle:c,endAngle:s});return vt(vt({},y),{},oy({},b,T))},{})},Ik=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return Math.sqrt(Math.pow(n-a,2)+Math.pow(i-o,2))},kk=function(t,r){var n=t.x,i=t.y,a=r.cx,o=r.cy,u=Ik({x:n,y:i},{x:a,y:o});if(u<=0)return{radius:u};var c=(n-a)/u,s=Math.acos(c);return i>o&&(s=2*Math.PI-s),{radius:u,angle:Mk(s),angleInRadian:s}},Dk=function(t){var r=t.startAngle,n=t.endAngle,i=Math.floor(r/360),a=Math.floor(n/360),o=Math.min(i,a);return{startAngle:r-o*360,endAngle:n-o*360}},Nk=function(t,r){var n=r.startAngle,i=r.endAngle,a=Math.floor(n/360),o=Math.floor(i/360),u=Math.min(a,o);return t+u*360},ip=function(t,r){var n=t.x,i=t.y,a=kk({x:n,y:i},r),o=a.radius,u=a.angle,c=r.innerRadius,s=r.outerRadius;if(o<c||o>s)return!1;if(o===0)return!0;var f=Dk(r),l=f.startAngle,p=f.endAngle,h=u,v;if(l<=p){for(;h>p;)h-=360;for(;h<l;)h+=360;v=h>=l&&h<=p}else{for(;h>l;)h-=360;for(;h<p;)h+=360;v=h>=p&&h<=l}return v?vt(vt({},r),{},{radius:o,angle:Nk(h,r)}):null},cy=function(t){return!R.isValidElement(t)&&!X(t)&&typeof t!="boolean"?t.className:""};function Kn(e){"@babel/helpers - typeof";return Kn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Kn(e)}var Rk=["offset"];function Lk(e){return zk(e)||Wk(e)||Fk(e)||Bk()}function Bk(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Fk(e,t){if(e){if(typeof e=="string")return Fu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Fu(e,t)}}function Wk(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function zk(e){if(Array.isArray(e))return Fu(e)}function Fu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Uk(e,t){if(e==null)return{};var r=qk(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function qk(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function ap(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Oe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ap(Object(r),!0).forEach(function(n){Hk(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ap(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Hk(e,t,r){return t=Kk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Kk(e){var t=Gk(e,"string");return Kn(t)=="symbol"?t:t+""}function Gk(e,t){if(Kn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Kn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Gn(){return Gn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Gn.apply(this,arguments)}var Vk=function(t){var r=t.value,n=t.formatter,i=Y(t.children)?r:t.children;return X(n)?n(i):i},Xk=function(t,r){var n=Ce(r-t),i=Math.min(Math.abs(r-t),360);return n*i},Yk=function(t,r,n){var i=t.position,a=t.viewBox,o=t.offset,u=t.className,c=a,s=c.cx,f=c.cy,l=c.innerRadius,p=c.outerRadius,h=c.startAngle,v=c.endAngle,d=c.clockWise,y=(l+p)/2,b=Xk(h,v),w=b>=0?1:-1,x,A;i==="insideStart"?(x=h+w*o,A=d):i==="insideEnd"?(x=v-w*o,A=!d):i==="end"&&(x=v+w*o,A=d),A=b<=0?A:!A;var m=se(s,f,y,x),g=se(s,f,y,x+(A?1:-1)*359),O="M".concat(m.x,",").concat(m.y,`
    A`).concat(y,",").concat(y,",0,1,").concat(A?0:1,`,
    `).concat(g.x,",").concat(g.y),P=Y(t.id)?tn("recharts-radial-line-"):t.id;return S.createElement("text",Gn({},n,{dominantBaseline:"central",className:J("recharts-radial-bar-label",u)}),S.createElement("defs",null,S.createElement("path",{id:P,d:O})),S.createElement("textPath",{xlinkHref:"#".concat(P)},r))},Zk=function(t){var r=t.viewBox,n=t.offset,i=t.position,a=r,o=a.cx,u=a.cy,c=a.innerRadius,s=a.outerRadius,f=a.startAngle,l=a.endAngle,p=(f+l)/2;if(i==="outside"){var h=se(o,u,s+n,p),v=h.x,d=h.y;return{x:v,y:d,textAnchor:v>=o?"start":"end",verticalAnchor:"middle"}}if(i==="center")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"middle"};if(i==="centerTop")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"start"};if(i==="centerBottom")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"end"};var y=(c+s)/2,b=se(o,u,y,p),w=b.x,x=b.y;return{x:w,y:x,textAnchor:"middle",verticalAnchor:"middle"}},Jk=function(t){var r=t.viewBox,n=t.parentViewBox,i=t.offset,a=t.position,o=r,u=o.x,c=o.y,s=o.width,f=o.height,l=f>=0?1:-1,p=l*i,h=l>0?"end":"start",v=l>0?"start":"end",d=s>=0?1:-1,y=d*i,b=d>0?"end":"start",w=d>0?"start":"end";if(a==="top"){var x={x:u+s/2,y:c-l*i,textAnchor:"middle",verticalAnchor:h};return Oe(Oe({},x),n?{height:Math.max(c-n.y,0),width:s}:{})}if(a==="bottom"){var A={x:u+s/2,y:c+f+p,textAnchor:"middle",verticalAnchor:v};return Oe(Oe({},A),n?{height:Math.max(n.y+n.height-(c+f),0),width:s}:{})}if(a==="left"){var m={x:u-y,y:c+f/2,textAnchor:b,verticalAnchor:"middle"};return Oe(Oe({},m),n?{width:Math.max(m.x-n.x,0),height:f}:{})}if(a==="right"){var g={x:u+s+y,y:c+f/2,textAnchor:w,verticalAnchor:"middle"};return Oe(Oe({},g),n?{width:Math.max(n.x+n.width-g.x,0),height:f}:{})}var O=n?{width:s,height:f}:{};return a==="insideLeft"?Oe({x:u+y,y:c+f/2,textAnchor:w,verticalAnchor:"middle"},O):a==="insideRight"?Oe({x:u+s-y,y:c+f/2,textAnchor:b,verticalAnchor:"middle"},O):a==="insideTop"?Oe({x:u+s/2,y:c+p,textAnchor:"middle",verticalAnchor:v},O):a==="insideBottom"?Oe({x:u+s/2,y:c+f-p,textAnchor:"middle",verticalAnchor:h},O):a==="insideTopLeft"?Oe({x:u+y,y:c+p,textAnchor:w,verticalAnchor:v},O):a==="insideTopRight"?Oe({x:u+s-y,y:c+p,textAnchor:b,verticalAnchor:v},O):a==="insideBottomLeft"?Oe({x:u+y,y:c+f-p,textAnchor:w,verticalAnchor:h},O):a==="insideBottomRight"?Oe({x:u+s-y,y:c+f-p,textAnchor:b,verticalAnchor:h},O):Zr(a)&&(L(a.x)||Vt(a.x))&&(L(a.y)||Vt(a.y))?Oe({x:u+Ie(a.x,s),y:c+Ie(a.y,f),textAnchor:"end",verticalAnchor:"end"},O):Oe({x:u+s/2,y:c+f/2,textAnchor:"middle",verticalAnchor:"middle"},O)},Qk=function(t){return"cx"in t&&L(t.cx)};function $e(e){var t=e.offset,r=t===void 0?5:t,n=Uk(e,Rk),i=Oe({offset:r},n),a=i.viewBox,o=i.position,u=i.value,c=i.children,s=i.content,f=i.className,l=f===void 0?"":f,p=i.textBreakAll;if(!a||Y(u)&&Y(c)&&!R.isValidElement(s)&&!X(s))return null;if(R.isValidElement(s))return R.cloneElement(s,i);var h;if(X(s)){if(h=R.createElement(s,i),R.isValidElement(h))return h}else h=Vk(i);var v=Qk(a),d=H(i,!0);if(v&&(o==="insideStart"||o==="insideEnd"||o==="end"))return Yk(i,h,d);var y=v?Zk(i):Jk(i);return S.createElement(rr,Gn({className:J("recharts-label",l)},d,y,{breakAll:p}),h)}$e.displayName="Label";var sy=function(t){var r=t.cx,n=t.cy,i=t.angle,a=t.startAngle,o=t.endAngle,u=t.r,c=t.radius,s=t.innerRadius,f=t.outerRadius,l=t.x,p=t.y,h=t.top,v=t.left,d=t.width,y=t.height,b=t.clockWise,w=t.labelViewBox;if(w)return w;if(L(d)&&L(y)){if(L(l)&&L(p))return{x:l,y:p,width:d,height:y};if(L(h)&&L(v))return{x:h,y:v,width:d,height:y}}return L(l)&&L(p)?{x:l,y:p,width:0,height:0}:L(r)&&L(n)?{cx:r,cy:n,startAngle:a||i||0,endAngle:o||i||0,innerRadius:s||0,outerRadius:f||c||u||0,clockWise:b}:t.viewBox?t.viewBox:{}},e2=function(t,r){return t?t===!0?S.createElement($e,{key:"label-implicit",viewBox:r}):Se(t)?S.createElement($e,{key:"label-implicit",viewBox:r,value:t}):R.isValidElement(t)?t.type===$e?R.cloneElement(t,{key:"label-implicit",viewBox:r}):S.createElement($e,{key:"label-implicit",content:t,viewBox:r}):X(t)?S.createElement($e,{key:"label-implicit",content:t,viewBox:r}):Zr(t)?S.createElement($e,Gn({viewBox:r},t,{key:"label-implicit"})):null:null},t2=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!t||!t.children&&n&&!t.label)return null;var i=t.children,a=sy(t),o=Ke(i,$e).map(function(c,s){return R.cloneElement(c,{viewBox:r||a,key:"label-".concat(s)})});if(!n)return o;var u=e2(t.label,r||a);return[u].concat(Lk(o))};$e.parseViewBox=sy;$e.renderCallByParent=t2;function r2(e){var t=e==null?0:e.length;return t?e[t-1]:void 0}var n2=r2;const i2=ae(n2);function Vn(e){"@babel/helpers - typeof";return Vn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Vn(e)}var a2=["valueAccessor"],o2=["data","dataKey","clockWise","id","textBreakAll"];function u2(e){return f2(e)||l2(e)||s2(e)||c2()}function c2(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function s2(e,t){if(e){if(typeof e=="string")return Wu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Wu(e,t)}}function l2(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function f2(e){if(Array.isArray(e))return Wu(e)}function Wu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function pa(){return pa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},pa.apply(this,arguments)}function op(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function up(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?op(Object(r),!0).forEach(function(n){p2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):op(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function p2(e,t,r){return t=h2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function h2(e){var t=d2(e,"string");return Vn(t)=="symbol"?t:t+""}function d2(e,t){if(Vn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Vn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function cp(e,t){if(e==null)return{};var r=v2(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function v2(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var y2=function(t){return Array.isArray(t.value)?i2(t.value):t.value};function wt(e){var t=e.valueAccessor,r=t===void 0?y2:t,n=cp(e,a2),i=n.data,a=n.dataKey,o=n.clockWise,u=n.id,c=n.textBreakAll,s=cp(n,o2);return!i||!i.length?null:S.createElement(te,{className:"recharts-label-list"},i.map(function(f,l){var p=Y(a)?r(f,l):Ae(f&&f.payload,a),h=Y(u)?{}:{id:"".concat(u,"-").concat(l)};return S.createElement($e,pa({},H(f,!0),s,h,{parentViewBox:f.parentViewBox,value:p,textBreakAll:c,viewBox:$e.parseViewBox(Y(o)?f:up(up({},f),{},{clockWise:o})),key:"label-".concat(l),index:l}))}))}wt.displayName="LabelList";function m2(e,t){return e?e===!0?S.createElement(wt,{key:"labelList-implicit",data:t}):S.isValidElement(e)||X(e)?S.createElement(wt,{key:"labelList-implicit",data:t,content:e}):Zr(e)?S.createElement(wt,pa({data:t},e,{key:"labelList-implicit"})):null:null}function g2(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!e||!e.children&&r&&!e.label)return null;var n=e.children,i=Ke(n,wt).map(function(o,u){return R.cloneElement(o,{data:t,key:"labelList-".concat(u)})});if(!r)return i;var a=m2(e.label,t);return[a].concat(u2(i))}wt.renderCallByParent=g2;function Xn(e){"@babel/helpers - typeof";return Xn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Xn(e)}function zu(){return zu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},zu.apply(this,arguments)}function sp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function lp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?sp(Object(r),!0).forEach(function(n){b2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function b2(e,t,r){return t=x2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function x2(e){var t=w2(e,"string");return Xn(t)=="symbol"?t:t+""}function w2(e,t){if(Xn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Xn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var O2=function(t,r){var n=Ce(r-t),i=Math.min(Math.abs(r-t),359.999);return n*i},ji=function(t){var r=t.cx,n=t.cy,i=t.radius,a=t.angle,o=t.sign,u=t.isExternal,c=t.cornerRadius,s=t.cornerIsExternal,f=c*(u?1:-1)+i,l=Math.asin(c/f)/fa,p=s?a:a+o*l,h=se(r,n,f,p),v=se(r,n,i,p),d=s?a-o*l:a,y=se(r,n,f*Math.cos(l*fa),d);return{center:h,circleTangency:v,lineTangency:y,theta:l}},ly=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.startAngle,u=t.endAngle,c=O2(o,u),s=o+c,f=se(r,n,a,o),l=se(r,n,a,s),p="M ".concat(f.x,",").concat(f.y,`
    A `).concat(a,",").concat(a,`,0,
    `).concat(+(Math.abs(c)>180),",").concat(+(o>s),`,
    `).concat(l.x,",").concat(l.y,`
  `);if(i>0){var h=se(r,n,i,o),v=se(r,n,i,s);p+="L ".concat(v.x,",").concat(v.y,`
            A `).concat(i,",").concat(i,`,0,
            `).concat(+(Math.abs(c)>180),",").concat(+(o<=s),`,
            `).concat(h.x,",").concat(h.y," Z")}else p+="L ".concat(r,",").concat(n," Z");return p},A2=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.cornerRadius,u=t.forceCornerRadius,c=t.cornerIsExternal,s=t.startAngle,f=t.endAngle,l=Ce(f-s),p=ji({cx:r,cy:n,radius:a,angle:s,sign:l,cornerRadius:o,cornerIsExternal:c}),h=p.circleTangency,v=p.lineTangency,d=p.theta,y=ji({cx:r,cy:n,radius:a,angle:f,sign:-l,cornerRadius:o,cornerIsExternal:c}),b=y.circleTangency,w=y.lineTangency,x=y.theta,A=c?Math.abs(s-f):Math.abs(s-f)-d-x;if(A<0)return u?"M ".concat(v.x,",").concat(v.y,`
        a`).concat(o,",").concat(o,",0,0,1,").concat(o*2,`,0
        a`).concat(o,",").concat(o,",0,0,1,").concat(-o*2,`,0
      `):ly({cx:r,cy:n,innerRadius:i,outerRadius:a,startAngle:s,endAngle:f});var m="M ".concat(v.x,",").concat(v.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(h.x,",").concat(h.y,`
    A`).concat(a,",").concat(a,",0,").concat(+(A>180),",").concat(+(l<0),",").concat(b.x,",").concat(b.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(w.x,",").concat(w.y,`
  `);if(i>0){var g=ji({cx:r,cy:n,radius:i,angle:s,sign:l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),O=g.circleTangency,P=g.lineTangency,_=g.theta,E=ji({cx:r,cy:n,radius:i,angle:f,sign:-l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),$=E.circleTangency,T=E.lineTangency,C=E.theta,I=c?Math.abs(s-f):Math.abs(s-f)-_-C;if(I<0&&o===0)return"".concat(m,"L").concat(r,",").concat(n,"Z");m+="L".concat(T.x,",").concat(T.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat($.x,",").concat($.y,`
      A`).concat(i,",").concat(i,",0,").concat(+(I>180),",").concat(+(l>0),",").concat(O.x,",").concat(O.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(P.x,",").concat(P.y,"Z")}else m+="L".concat(r,",").concat(n,"Z");return m},S2={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},fy=function(t){var r=lp(lp({},S2),t),n=r.cx,i=r.cy,a=r.innerRadius,o=r.outerRadius,u=r.cornerRadius,c=r.forceCornerRadius,s=r.cornerIsExternal,f=r.startAngle,l=r.endAngle,p=r.className;if(o<a||f===l)return null;var h=J("recharts-sector",p),v=o-a,d=Ie(u,v,0,!0),y;return d>0&&Math.abs(f-l)<360?y=A2({cx:n,cy:i,innerRadius:a,outerRadius:o,cornerRadius:Math.min(d,v/2),forceCornerRadius:c,cornerIsExternal:s,startAngle:f,endAngle:l}):y=ly({cx:n,cy:i,innerRadius:a,outerRadius:o,startAngle:f,endAngle:l}),S.createElement("path",zu({},H(r,!0),{className:h,d:y,role:"img"}))};function Yn(e){"@babel/helpers - typeof";return Yn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Yn(e)}function Uu(){return Uu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Uu.apply(this,arguments)}function fp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function pp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?fp(Object(r),!0).forEach(function(n){P2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function P2(e,t,r){return t=_2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _2(e){var t=$2(e,"string");return Yn(t)=="symbol"?t:t+""}function $2(e,t){if(Yn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Yn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var hp={curveBasisClosed:Pw,curveBasisOpen:_w,curveBasis:Sw,curveBumpX:lw,curveBumpY:fw,curveLinearClosed:$w,curveLinear:qa,curveMonotoneX:Tw,curveMonotoneY:Ew,curveNatural:jw,curveStep:Mw,curveStepAfter:Iw,curveStepBefore:Cw},Mi=function(t){return t.x===+t.x&&t.y===+t.y},mn=function(t){return t.x},gn=function(t){return t.y},T2=function(t,r){if(X(t))return t;var n="curve".concat(za(t));return(n==="curveMonotone"||n==="curveBump")&&r?hp["".concat(n).concat(r==="vertical"?"Y":"X")]:hp[n]||qa},E2=function(t){var r=t.type,n=r===void 0?"linear":r,i=t.points,a=i===void 0?[]:i,o=t.baseLine,u=t.layout,c=t.connectNulls,s=c===void 0?!1:c,f=T2(n,u),l=s?a.filter(function(d){return Mi(d)}):a,p;if(Array.isArray(o)){var h=s?o.filter(function(d){return Mi(d)}):o,v=l.map(function(d,y){return pp(pp({},d),{},{base:h[y]})});return u==="vertical"?p=wi().y(gn).x1(mn).x0(function(d){return d.base.x}):p=wi().x(mn).y1(gn).y0(function(d){return d.base.y}),p.defined(Mi).curve(f),p(v)}return u==="vertical"&&L(o)?p=wi().y(gn).x1(mn).x0(o):L(o)?p=wi().x(mn).y1(gn).y0(o):p=ad().x(mn).y(gn),p.defined(Mi).curve(f),p(l)},ha=function(t){var r=t.className,n=t.points,i=t.path,a=t.pathRef;if((!n||!n.length)&&!i)return null;var o=n&&n.length?E2(t):i;return R.createElement("path",Uu({},H(t,!1),Ri(t),{className:J("recharts-curve",r),d:o,ref:a}))},py={exports:{}},j2="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",M2=j2,C2=M2;function hy(){}function dy(){}dy.resetWarningCache=hy;var I2=function(){function e(n,i,a,o,u,c){if(c!==C2){var s=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}e.isRequired=e;function t(){return e}var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:dy,resetWarningCache:hy};return r.PropTypes=r,r};py.exports=I2();var k2=py.exports;const ne=ae(k2);var D2=Object.getOwnPropertyNames,N2=Object.getOwnPropertySymbols,R2=Object.prototype.hasOwnProperty;function dp(e,t){return function(n,i,a){return e(n,i,a)&&t(n,i,a)}}function Ci(e){return function(r,n,i){if(!r||!n||typeof r!="object"||typeof n!="object")return e(r,n,i);var a=i.cache,o=a.get(r),u=a.get(n);if(o&&u)return o===n&&u===r;a.set(r,n),a.set(n,r);var c=e(r,n,i);return a.delete(r),a.delete(n),c}}function vp(e){return D2(e).concat(N2(e))}var L2=Object.hasOwn||function(e,t){return R2.call(e,t)};function lr(e,t){return e===t||!e&&!t&&e!==e&&t!==t}var B2="__v",F2="__o",W2="_owner",yp=Object.getOwnPropertyDescriptor,mp=Object.keys;function z2(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function U2(e,t){return lr(e.getTime(),t.getTime())}function q2(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function H2(e,t){return e===t}function gp(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.entries(),o,u,c=0;(o=a.next())&&!o.done;){for(var s=t.entries(),f=!1,l=0;(u=s.next())&&!u.done;){if(i[l]){l++;continue}var p=o.value,h=u.value;if(r.equals(p[0],h[0],c,l,e,t,r)&&r.equals(p[1],h[1],p[0],h[0],e,t,r)){f=i[l]=!0;break}l++}if(!f)return!1;c++}return!0}var K2=lr;function G2(e,t,r){var n=mp(e),i=n.length;if(mp(t).length!==i)return!1;for(;i-- >0;)if(!vy(e,t,r,n[i]))return!1;return!0}function bn(e,t,r){var n=vp(e),i=n.length;if(vp(t).length!==i)return!1;for(var a,o,u;i-- >0;)if(a=n[i],!vy(e,t,r,a)||(o=yp(e,a),u=yp(t,a),(o||u)&&(!o||!u||o.configurable!==u.configurable||o.enumerable!==u.enumerable||o.writable!==u.writable)))return!1;return!0}function V2(e,t){return lr(e.valueOf(),t.valueOf())}function X2(e,t){return e.source===t.source&&e.flags===t.flags}function bp(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.values(),o,u;(o=a.next())&&!o.done;){for(var c=t.values(),s=!1,f=0;(u=c.next())&&!u.done;){if(!i[f]&&r.equals(o.value,u.value,o.value,u.value,e,t,r)){s=i[f]=!0;break}f++}if(!s)return!1}return!0}function Y2(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}function Z2(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function vy(e,t,r,n){return(n===W2||n===F2||n===B2)&&(e.$$typeof||t.$$typeof)?!0:L2(t,n)&&r.equals(e[n],t[n],n,n,e,t,r)}var J2="[object Arguments]",Q2="[object Boolean]",eD="[object Date]",tD="[object Error]",rD="[object Map]",nD="[object Number]",iD="[object Object]",aD="[object RegExp]",oD="[object Set]",uD="[object String]",cD="[object URL]",sD=Array.isArray,xp=typeof ArrayBuffer=="function"&&ArrayBuffer.isView?ArrayBuffer.isView:null,wp=Object.assign,lD=Object.prototype.toString.call.bind(Object.prototype.toString);function fD(e){var t=e.areArraysEqual,r=e.areDatesEqual,n=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,o=e.areNumbersEqual,u=e.areObjectsEqual,c=e.arePrimitiveWrappersEqual,s=e.areRegExpsEqual,f=e.areSetsEqual,l=e.areTypedArraysEqual,p=e.areUrlsEqual;return function(v,d,y){if(v===d)return!0;if(v==null||d==null)return!1;var b=typeof v;if(b!==typeof d)return!1;if(b!=="object")return b==="number"?o(v,d,y):b==="function"?i(v,d,y):!1;var w=v.constructor;if(w!==d.constructor)return!1;if(w===Object)return u(v,d,y);if(sD(v))return t(v,d,y);if(xp!=null&&xp(v))return l(v,d,y);if(w===Date)return r(v,d,y);if(w===RegExp)return s(v,d,y);if(w===Map)return a(v,d,y);if(w===Set)return f(v,d,y);var x=lD(v);return x===eD?r(v,d,y):x===aD?s(v,d,y):x===rD?a(v,d,y):x===oD?f(v,d,y):x===iD?typeof v.then!="function"&&typeof d.then!="function"&&u(v,d,y):x===cD?p(v,d,y):x===tD?n(v,d,y):x===J2?u(v,d,y):x===Q2||x===nD||x===uD?c(v,d,y):!1}}function pD(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,i={areArraysEqual:n?bn:z2,areDatesEqual:U2,areErrorsEqual:q2,areFunctionsEqual:H2,areMapsEqual:n?dp(gp,bn):gp,areNumbersEqual:K2,areObjectsEqual:n?bn:G2,arePrimitiveWrappersEqual:V2,areRegExpsEqual:X2,areSetsEqual:n?dp(bp,bn):bp,areTypedArraysEqual:n?bn:Y2,areUrlsEqual:Z2};if(r&&(i=wp({},i,r(i))),t){var a=Ci(i.areArraysEqual),o=Ci(i.areMapsEqual),u=Ci(i.areObjectsEqual),c=Ci(i.areSetsEqual);i=wp({},i,{areArraysEqual:a,areMapsEqual:o,areObjectsEqual:u,areSetsEqual:c})}return i}function hD(e){return function(t,r,n,i,a,o,u){return e(t,r,u)}}function dD(e){var t=e.circular,r=e.comparator,n=e.createState,i=e.equals,a=e.strict;if(n)return function(c,s){var f=n(),l=f.cache,p=l===void 0?t?new WeakMap:void 0:l,h=f.meta;return r(c,s,{cache:p,equals:i,meta:h,strict:a})};if(t)return function(c,s){return r(c,s,{cache:new WeakMap,equals:i,meta:void 0,strict:a})};var o={cache:void 0,equals:i,meta:void 0,strict:a};return function(c,s){return r(c,s,o)}}var vD=Nt();Nt({strict:!0});Nt({circular:!0});Nt({circular:!0,strict:!0});Nt({createInternalComparator:function(){return lr}});Nt({strict:!0,createInternalComparator:function(){return lr}});Nt({circular:!0,createInternalComparator:function(){return lr}});Nt({circular:!0,createInternalComparator:function(){return lr},strict:!0});function Nt(e){e===void 0&&(e={});var t=e.circular,r=t===void 0?!1:t,n=e.createInternalComparator,i=e.createState,a=e.strict,o=a===void 0?!1:a,u=pD(e),c=fD(u),s=n?n(c):hD(c);return dD({circular:r,comparator:c,createState:i,equals:s,strict:o})}function yD(e){typeof requestAnimationFrame<"u"&&requestAnimationFrame(e)}function Op(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=-1,n=function i(a){r<0&&(r=a),a-r>t?(e(a),r=-1):yD(i)};requestAnimationFrame(n)}function qu(e){"@babel/helpers - typeof";return qu=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},qu(e)}function mD(e){return wD(e)||xD(e)||bD(e)||gD()}function gD(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function bD(e,t){if(e){if(typeof e=="string")return Ap(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ap(e,t)}}function Ap(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function xD(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function wD(e){if(Array.isArray(e))return e}function OD(){var e={},t=function(){return null},r=!1,n=function i(a){if(!r){if(Array.isArray(a)){if(!a.length)return;var o=a,u=mD(o),c=u[0],s=u.slice(1);if(typeof c=="number"){Op(i.bind(null,s),c);return}i(c),Op(i.bind(null,s));return}qu(a)==="object"&&(e=a,t(e)),typeof a=="function"&&a()}};return{stop:function(){r=!0},start:function(a){r=!1,n(a)},subscribe:function(a){return t=a,function(){t=function(){return null}}}}}function Zn(e){"@babel/helpers - typeof";return Zn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Zn(e)}function Sp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Pp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Sp(Object(r),!0).forEach(function(n){yy(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Sp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function yy(e,t,r){return t=AD(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function AD(e){var t=SD(e,"string");return Zn(t)==="symbol"?t:String(t)}function SD(e,t){if(Zn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Zn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var PD=function(t,r){return[Object.keys(t),Object.keys(r)].reduce(function(n,i){return n.filter(function(a){return i.includes(a)})})},_D=function(t){return t},$D=function(t){return t.replace(/([A-Z])/g,function(r){return"-".concat(r.toLowerCase())})},Pn=function(t,r){return Object.keys(r).reduce(function(n,i){return Pp(Pp({},n),{},yy({},i,t(i,r[i])))},{})},_p=function(t,r,n){return t.map(function(i){return"".concat($D(i)," ").concat(r,"ms ").concat(n)}).join(",")};function TD(e,t){return MD(e)||jD(e,t)||my(e,t)||ED()}function ED(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function jD(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function MD(e){if(Array.isArray(e))return e}function CD(e){return DD(e)||kD(e)||my(e)||ID()}function ID(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function my(e,t){if(e){if(typeof e=="string")return Hu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Hu(e,t)}}function kD(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function DD(e){if(Array.isArray(e))return Hu(e)}function Hu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var da=1e-4,gy=function(t,r){return[0,3*t,3*r-6*t,3*t-3*r+1]},by=function(t,r){return t.map(function(n,i){return n*Math.pow(r,i)}).reduce(function(n,i){return n+i})},$p=function(t,r){return function(n){var i=gy(t,r);return by(i,n)}},ND=function(t,r){return function(n){var i=gy(t,r),a=[].concat(CD(i.map(function(o,u){return o*u}).slice(1)),[0]);return by(a,n)}},Tp=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0],a=r[1],o=r[2],u=r[3];if(r.length===1)switch(r[0]){case"linear":i=0,a=0,o=1,u=1;break;case"ease":i=.25,a=.1,o=.25,u=1;break;case"ease-in":i=.42,a=0,o=1,u=1;break;case"ease-out":i=.42,a=0,o=.58,u=1;break;case"ease-in-out":i=0,a=0,o=.58,u=1;break;default:{var c=r[0].split("(");if(c[0]==="cubic-bezier"&&c[1].split(")")[0].split(",").length===4){var s=c[1].split(")")[0].split(",").map(function(y){return parseFloat(y)}),f=TD(s,4);i=f[0],a=f[1],o=f[2],u=f[3]}}}var l=$p(i,o),p=$p(a,u),h=ND(i,o),v=function(b){return b>1?1:b<0?0:b},d=function(b){for(var w=b>1?1:b,x=w,A=0;A<8;++A){var m=l(x)-w,g=h(x);if(Math.abs(m-w)<da||g<da)return p(x);x=v(x-m/g)}return p(x)};return d.isStepper=!1,d},RD=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=t.stiff,n=r===void 0?100:r,i=t.damping,a=i===void 0?8:i,o=t.dt,u=o===void 0?17:o,c=function(f,l,p){var h=-(f-l)*n,v=p*a,d=p+(h-v)*u/1e3,y=p*u/1e3+f;return Math.abs(y-l)<da&&Math.abs(d)<da?[l,0]:[y,d]};return c.isStepper=!0,c.dt=u,c},LD=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0];if(typeof i=="string")switch(i){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return Tp(i);case"spring":return RD();default:if(i.split("(")[0]==="cubic-bezier")return Tp(i)}return typeof i=="function"?i:null};function Jn(e){"@babel/helpers - typeof";return Jn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Jn(e)}function Ep(e){return WD(e)||FD(e)||xy(e)||BD()}function BD(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function FD(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function WD(e){if(Array.isArray(e))return Gu(e)}function jp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ee(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?jp(Object(r),!0).forEach(function(n){Ku(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):jp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ku(e,t,r){return t=zD(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zD(e){var t=UD(e,"string");return Jn(t)==="symbol"?t:String(t)}function UD(e,t){if(Jn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Jn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function qD(e,t){return GD(e)||KD(e,t)||xy(e,t)||HD()}function HD(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function xy(e,t){if(e){if(typeof e=="string")return Gu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Gu(e,t)}}function Gu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function KD(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function GD(e){if(Array.isArray(e))return e}var va=function(t,r,n){return t+(r-t)*n},Vu=function(t){var r=t.from,n=t.to;return r!==n},VD=function e(t,r,n){var i=Pn(function(a,o){if(Vu(o)){var u=t(o.from,o.to,o.velocity),c=qD(u,2),s=c[0],f=c[1];return Ee(Ee({},o),{},{from:s,velocity:f})}return o},r);return n<1?Pn(function(a,o){return Vu(o)?Ee(Ee({},o),{},{velocity:va(o.velocity,i[a].velocity,n),from:va(o.from,i[a].from,n)}):o},r):e(t,i,n-1)};const XD=function(e,t,r,n,i){var a=PD(e,t),o=a.reduce(function(y,b){return Ee(Ee({},y),{},Ku({},b,[e[b],t[b]]))},{}),u=a.reduce(function(y,b){return Ee(Ee({},y),{},Ku({},b,{from:e[b],velocity:0,to:t[b]}))},{}),c=-1,s,f,l=function(){return null},p=function(){return Pn(function(b,w){return w.from},u)},h=function(){return!Object.values(u).filter(Vu).length},v=function(b){s||(s=b);var w=b-s,x=w/r.dt;u=VD(r,u,x),i(Ee(Ee(Ee({},e),t),p())),s=b,h()||(c=requestAnimationFrame(l))},d=function(b){f||(f=b);var w=(b-f)/n,x=Pn(function(m,g){return va.apply(void 0,Ep(g).concat([r(w)]))},o);if(i(Ee(Ee(Ee({},e),t),x)),w<1)c=requestAnimationFrame(l);else{var A=Pn(function(m,g){return va.apply(void 0,Ep(g).concat([r(1)]))},o);i(Ee(Ee(Ee({},e),t),A))}};return l=r.isStepper?v:d,function(){return requestAnimationFrame(l),function(){cancelAnimationFrame(c)}}};function kr(e){"@babel/helpers - typeof";return kr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kr(e)}var YD=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function ZD(e,t){if(e==null)return{};var r=JD(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function JD(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function Ho(e){return rN(e)||tN(e)||eN(e)||QD()}function QD(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function eN(e,t){if(e){if(typeof e=="string")return Xu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Xu(e,t)}}function tN(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function rN(e){if(Array.isArray(e))return Xu(e)}function Xu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Mp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function et(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Mp(Object(r),!0).forEach(function(n){wn(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Mp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function wn(e,t,r){return t=wy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function nN(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function iN(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,wy(n.key),n)}}function aN(e,t,r){return t&&iN(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function wy(e){var t=oN(e,"string");return kr(t)==="symbol"?t:String(t)}function oN(e,t){if(kr(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(kr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function uN(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Yu(e,t)}function Yu(e,t){return Yu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Yu(e,t)}function cN(e){var t=sN();return function(){var n=ya(e),i;if(t){var a=ya(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return Zu(this,i)}}function Zu(e,t){if(t&&(kr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Ju(e)}function Ju(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function sN(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function ya(e){return ya=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ya(e)}var lt=function(e){uN(r,e);var t=cN(r);function r(n,i){var a;nN(this,r),a=t.call(this,n,i);var o=a.props,u=o.isActive,c=o.attributeName,s=o.from,f=o.to,l=o.steps,p=o.children,h=o.duration;if(a.handleStyleChange=a.handleStyleChange.bind(Ju(a)),a.changeStyle=a.changeStyle.bind(Ju(a)),!u||h<=0)return a.state={style:{}},typeof p=="function"&&(a.state={style:f}),Zu(a);if(l&&l.length)a.state={style:l[0].style};else if(s){if(typeof p=="function")return a.state={style:s},Zu(a);a.state={style:c?wn({},c,s):s}}else a.state={style:{}};return a}return aN(r,[{key:"componentDidMount",value:function(){var i=this.props,a=i.isActive,o=i.canBegin;this.mounted=!0,!(!a||!o)&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(i){var a=this.props,o=a.isActive,u=a.canBegin,c=a.attributeName,s=a.shouldReAnimate,f=a.to,l=a.from,p=this.state.style;if(u){if(!o){var h={style:c?wn({},c,f):f};this.state&&p&&(c&&p[c]!==f||!c&&p!==f)&&this.setState(h);return}if(!(vD(i.to,f)&&i.canBegin&&i.isActive)){var v=!i.canBegin||!i.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var d=v||s?l:i.to;if(this.state&&p){var y={style:c?wn({},c,d):d};(c&&p[c]!==d||!c&&p!==d)&&this.setState(y)}this.runAnimation(et(et({},this.props),{},{from:d,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var i=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),i&&i()}},{key:"handleStyleChange",value:function(i){this.changeStyle(i)}},{key:"changeStyle",value:function(i){this.mounted&&this.setState({style:i})}},{key:"runJSAnimation",value:function(i){var a=this,o=i.from,u=i.to,c=i.duration,s=i.easing,f=i.begin,l=i.onAnimationEnd,p=i.onAnimationStart,h=XD(o,u,LD(s),c,this.changeStyle),v=function(){a.stopJSAnimation=h()};this.manager.start([p,f,v,c,l])}},{key:"runStepAnimation",value:function(i){var a=this,o=i.steps,u=i.begin,c=i.onAnimationStart,s=o[0],f=s.style,l=s.duration,p=l===void 0?0:l,h=function(d,y,b){if(b===0)return d;var w=y.duration,x=y.easing,A=x===void 0?"ease":x,m=y.style,g=y.properties,O=y.onAnimationEnd,P=b>0?o[b-1]:y,_=g||Object.keys(m);if(typeof A=="function"||A==="spring")return[].concat(Ho(d),[a.runJSAnimation.bind(a,{from:P.style,to:m,duration:w,easing:A}),w]);var E=_p(_,w,A),$=et(et(et({},P.style),m),{},{transition:E});return[].concat(Ho(d),[$,w,O]).filter(_D)};return this.manager.start([c].concat(Ho(o.reduce(h,[f,Math.max(p,u)])),[i.onAnimationEnd]))}},{key:"runAnimation",value:function(i){this.manager||(this.manager=OD());var a=i.begin,o=i.duration,u=i.attributeName,c=i.to,s=i.easing,f=i.onAnimationStart,l=i.onAnimationEnd,p=i.steps,h=i.children,v=this.manager;if(this.unSubscribe=v.subscribe(this.handleStyleChange),typeof s=="function"||typeof h=="function"||s==="spring"){this.runJSAnimation(i);return}if(p.length>1){this.runStepAnimation(i);return}var d=u?wn({},u,c):c,y=_p(Object.keys(d),o,s);v.start([f,a,et(et({},d),{},{transition:y}),o,l])}},{key:"render",value:function(){var i=this.props,a=i.children;i.begin;var o=i.duration;i.attributeName,i.easing;var u=i.isActive;i.steps,i.from,i.to,i.canBegin,i.onAnimationEnd,i.shouldReAnimate,i.onAnimationReStart;var c=ZD(i,YD),s=R.Children.count(a),f=this.state.style;if(typeof a=="function")return a(f);if(!u||s===0||o<=0)return a;var l=function(h){var v=h.props,d=v.style,y=d===void 0?{}:d,b=v.className,w=R.cloneElement(h,et(et({},c),{},{style:et(et({},y),f),className:b}));return w};return s===1?l(R.Children.only(a)):S.createElement("div",null,R.Children.map(a,function(p){return l(p)}))}}]),r}(R.PureComponent);lt.displayName="Animate";lt.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}};lt.propTypes={from:ne.oneOfType([ne.object,ne.string]),to:ne.oneOfType([ne.object,ne.string]),attributeName:ne.string,duration:ne.number,begin:ne.number,easing:ne.oneOfType([ne.string,ne.func]),steps:ne.arrayOf(ne.shape({duration:ne.number.isRequired,style:ne.object.isRequired,easing:ne.oneOfType([ne.oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),ne.func]),properties:ne.arrayOf("string"),onAnimationEnd:ne.func})),children:ne.oneOfType([ne.node,ne.func]),isActive:ne.bool,canBegin:ne.bool,onAnimationEnd:ne.func,shouldReAnimate:ne.bool,onAnimationStart:ne.func,onAnimationReStart:ne.func};function Qn(e){"@babel/helpers - typeof";return Qn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qn(e)}function ma(){return ma=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ma.apply(this,arguments)}function lN(e,t){return dN(e)||hN(e,t)||pN(e,t)||fN()}function fN(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function pN(e,t){if(e){if(typeof e=="string")return Cp(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Cp(e,t)}}function Cp(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function hN(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function dN(e){if(Array.isArray(e))return e}function Ip(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function kp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ip(Object(r),!0).forEach(function(n){vN(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ip(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function vN(e,t,r){return t=yN(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yN(e){var t=mN(e,"string");return Qn(t)=="symbol"?t:t+""}function mN(e,t){if(Qn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Qn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Dp=function(t,r,n,i,a){var o=Math.min(Math.abs(n)/2,Math.abs(i)/2),u=i>=0?1:-1,c=n>=0?1:-1,s=i>=0&&n>=0||i<0&&n<0?1:0,f;if(o>0&&a instanceof Array){for(var l=[0,0,0,0],p=0,h=4;p<h;p++)l[p]=a[p]>o?o:a[p];f="M".concat(t,",").concat(r+u*l[0]),l[0]>0&&(f+="A ".concat(l[0],",").concat(l[0],",0,0,").concat(s,",").concat(t+c*l[0],",").concat(r)),f+="L ".concat(t+n-c*l[1],",").concat(r),l[1]>0&&(f+="A ".concat(l[1],",").concat(l[1],",0,0,").concat(s,`,
        `).concat(t+n,",").concat(r+u*l[1])),f+="L ".concat(t+n,",").concat(r+i-u*l[2]),l[2]>0&&(f+="A ".concat(l[2],",").concat(l[2],",0,0,").concat(s,`,
        `).concat(t+n-c*l[2],",").concat(r+i)),f+="L ".concat(t+c*l[3],",").concat(r+i),l[3]>0&&(f+="A ".concat(l[3],",").concat(l[3],",0,0,").concat(s,`,
        `).concat(t,",").concat(r+i-u*l[3])),f+="Z"}else if(o>0&&a===+a&&a>0){var v=Math.min(o,a);f="M ".concat(t,",").concat(r+u*v,`
            A `).concat(v,",").concat(v,",0,0,").concat(s,",").concat(t+c*v,",").concat(r,`
            L `).concat(t+n-c*v,",").concat(r,`
            A `).concat(v,",").concat(v,",0,0,").concat(s,",").concat(t+n,",").concat(r+u*v,`
            L `).concat(t+n,",").concat(r+i-u*v,`
            A `).concat(v,",").concat(v,",0,0,").concat(s,",").concat(t+n-c*v,",").concat(r+i,`
            L `).concat(t+c*v,",").concat(r+i,`
            A `).concat(v,",").concat(v,",0,0,").concat(s,",").concat(t,",").concat(r+i-u*v," Z")}else f="M ".concat(t,",").concat(r," h ").concat(n," v ").concat(i," h ").concat(-n," Z");return f},gN=function(t,r){if(!t||!r)return!1;var n=t.x,i=t.y,a=r.x,o=r.y,u=r.width,c=r.height;if(Math.abs(u)>0&&Math.abs(c)>0){var s=Math.min(a,a+u),f=Math.max(a,a+u),l=Math.min(o,o+c),p=Math.max(o,o+c);return n>=s&&n<=f&&i>=l&&i<=p}return!1},bN={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},ws=function(t){var r=kp(kp({},bN),t),n=R.useRef(),i=R.useState(-1),a=lN(i,2),o=a[0],u=a[1];R.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var A=n.current.getTotalLength();A&&u(A)}catch{}},[]);var c=r.x,s=r.y,f=r.width,l=r.height,p=r.radius,h=r.className,v=r.animationEasing,d=r.animationDuration,y=r.animationBegin,b=r.isAnimationActive,w=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||l!==+l||f===0||l===0)return null;var x=J("recharts-rectangle",h);return w?S.createElement(lt,{canBegin:o>0,from:{width:f,height:l,x:c,y:s},to:{width:f,height:l,x:c,y:s},duration:d,animationEasing:v,isActive:w},function(A){var m=A.width,g=A.height,O=A.x,P=A.y;return S.createElement(lt,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:d,isActive:b,easing:v},S.createElement("path",ma({},H(r,!0),{className:x,d:Dp(O,P,m,g,p),ref:n})))}):S.createElement("path",ma({},H(r,!0),{className:x,d:Dp(c,s,f,l,p)}))},xN=["points","className","baseLinePoints","connectNulls"];function mr(){return mr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},mr.apply(this,arguments)}function wN(e,t){if(e==null)return{};var r=ON(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function ON(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Np(e){return _N(e)||PN(e)||SN(e)||AN()}function AN(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function SN(e,t){if(e){if(typeof e=="string")return Qu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Qu(e,t)}}function PN(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function _N(e){if(Array.isArray(e))return Qu(e)}function Qu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Rp=function(t){return t&&t.x===+t.x&&t.y===+t.y},$N=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],r=[[]];return t.forEach(function(n){Rp(n)?r[r.length-1].push(n):r[r.length-1].length>0&&r.push([])}),Rp(t[0])&&r[r.length-1].push(t[0]),r[r.length-1].length<=0&&(r=r.slice(0,-1)),r},_n=function(t,r){var n=$N(t);r&&(n=[n.reduce(function(a,o){return[].concat(Np(a),Np(o))},[])]);var i=n.map(function(a){return a.reduce(function(o,u,c){return"".concat(o).concat(c===0?"M":"L").concat(u.x,",").concat(u.y)},"")}).join("");return n.length===1?"".concat(i,"Z"):i},TN=function(t,r,n){var i=_n(t,n);return"".concat(i.slice(-1)==="Z"?i.slice(0,-1):i,"L").concat(_n(r.reverse(),n).slice(1))},EN=function(t){var r=t.points,n=t.className,i=t.baseLinePoints,a=t.connectNulls,o=wN(t,xN);if(!r||!r.length)return null;var u=J("recharts-polygon",n);if(i&&i.length){var c=o.stroke&&o.stroke!=="none",s=TN(r,i,a);return S.createElement("g",{className:u},S.createElement("path",mr({},H(o,!0),{fill:s.slice(-1)==="Z"?o.fill:"none",stroke:"none",d:s})),c?S.createElement("path",mr({},H(o,!0),{fill:"none",d:_n(r,a)})):null,c?S.createElement("path",mr({},H(o,!0),{fill:"none",d:_n(i,a)})):null)}var f=_n(r,a);return S.createElement("path",mr({},H(o,!0),{fill:f.slice(-1)==="Z"?o.fill:"none",className:u,d:f}))};function ec(){return ec=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ec.apply(this,arguments)}var oo=function(t){var r=t.cx,n=t.cy,i=t.r,a=t.className,o=J("recharts-dot",a);return r===+r&&n===+n&&i===+i?R.createElement("circle",ec({},H(t,!1),Ri(t),{className:o,cx:r,cy:n,r:i})):null};function ei(e){"@babel/helpers - typeof";return ei=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ei(e)}var jN=["x","y","top","left","width","height","className"];function tc(){return tc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},tc.apply(this,arguments)}function Lp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function MN(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Lp(Object(r),!0).forEach(function(n){CN(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Lp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function CN(e,t,r){return t=IN(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function IN(e){var t=kN(e,"string");return ei(t)=="symbol"?t:t+""}function kN(e,t){if(ei(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ei(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function DN(e,t){if(e==null)return{};var r=NN(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function NN(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var RN=function(t,r,n,i,a,o){return"M".concat(t,",").concat(a,"v").concat(i,"M").concat(o,",").concat(r,"h").concat(n)},LN=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.top,u=o===void 0?0:o,c=t.left,s=c===void 0?0:c,f=t.width,l=f===void 0?0:f,p=t.height,h=p===void 0?0:p,v=t.className,d=DN(t,jN),y=MN({x:n,y:a,top:u,left:s,width:l,height:h},d);return!L(n)||!L(a)||!L(l)||!L(h)||!L(u)||!L(s)?null:S.createElement("path",tc({},H(y,!0),{className:J("recharts-cross",v),d:RN(n,a,l,h,u,s)}))},BN=to,FN=Iv,WN=pt;function zN(e,t){return e&&e.length?BN(e,WN(t),FN):void 0}var UN=zN;const qN=ae(UN);var HN=to,KN=pt,GN=kv;function VN(e,t){return e&&e.length?HN(e,KN(t),GN):void 0}var XN=VN;const YN=ae(XN);var ZN=["cx","cy","angle","ticks","axisLine"],JN=["ticks","tick","angle","tickFormatter","stroke"];function Dr(e){"@babel/helpers - typeof";return Dr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dr(e)}function $n(){return $n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$n.apply(this,arguments)}function Bp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function zt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Bp(Object(r),!0).forEach(function(n){uo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Bp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Fp(e,t){if(e==null)return{};var r=QN(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function QN(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function eR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Wp(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Ay(n.key),n)}}function tR(e,t,r){return t&&Wp(e.prototype,t),r&&Wp(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function rR(e,t,r){return t=ga(t),nR(e,Oy()?Reflect.construct(t,r||[],ga(e).constructor):t.apply(e,r))}function nR(e,t){if(t&&(Dr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return iR(e)}function iR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Oy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Oy=function(){return!!e})()}function ga(e){return ga=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ga(e)}function aR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&rc(e,t)}function rc(e,t){return rc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},rc(e,t)}function uo(e,t,r){return t=Ay(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ay(e){var t=oR(e,"string");return Dr(t)=="symbol"?t:t+""}function oR(e,t){if(Dr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Dr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var co=function(e){function t(){return eR(this,t),rR(this,t,arguments)}return aR(t,e),tR(t,[{key:"getTickValueCoord",value:function(n){var i=n.coordinate,a=this.props,o=a.angle,u=a.cx,c=a.cy;return se(u,c,i,o)}},{key:"getTickTextAnchor",value:function(){var n=this.props.orientation,i;switch(n){case"left":i="end";break;case"right":i="start";break;default:i="middle";break}return i}},{key:"getViewBox",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.angle,u=n.ticks,c=qN(u,function(f){return f.coordinate||0}),s=YN(u,function(f){return f.coordinate||0});return{cx:i,cy:a,startAngle:o,endAngle:o,innerRadius:s.coordinate||0,outerRadius:c.coordinate||0}}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.angle,u=n.ticks,c=n.axisLine,s=Fp(n,ZN),f=u.reduce(function(v,d){return[Math.min(v[0],d.coordinate),Math.max(v[1],d.coordinate)]},[1/0,-1/0]),l=se(i,a,f[0],o),p=se(i,a,f[1],o),h=zt(zt(zt({},H(s,!1)),{},{fill:"none"},H(c,!1)),{},{x1:l.x,y1:l.y,x2:p.x,y2:p.y});return S.createElement("line",$n({className:"recharts-polar-radius-axis-line"},h))}},{key:"renderTicks",value:function(){var n=this,i=this.props,a=i.ticks,o=i.tick,u=i.angle,c=i.tickFormatter,s=i.stroke,f=Fp(i,JN),l=this.getTickTextAnchor(),p=H(f,!1),h=H(o,!1),v=a.map(function(d,y){var b=n.getTickValueCoord(d),w=zt(zt(zt(zt({textAnchor:l,transform:"rotate(".concat(90-u,", ").concat(b.x,", ").concat(b.y,")")},p),{},{stroke:"none",fill:s},h),{},{index:y},b),{},{payload:d});return S.createElement(te,$n({className:J("recharts-polar-radius-axis-tick",cy(o)),key:"tick-".concat(d.coordinate)},tr(n.props,d,y)),t.renderTickItem(o,w,c?c(d.value,y):d.value))});return S.createElement(te,{className:"recharts-polar-radius-axis-ticks"},v)}},{key:"render",value:function(){var n=this.props,i=n.ticks,a=n.axisLine,o=n.tick;return!i||!i.length?null:S.createElement(te,{className:J("recharts-polar-radius-axis",this.props.className)},a&&this.renderAxisLine(),o&&this.renderTicks(),$e.renderCallByParent(this.props,this.getViewBox()))}}],[{key:"renderTickItem",value:function(n,i,a){var o;return S.isValidElement(n)?o=S.cloneElement(n,i):X(n)?o=n(i):o=S.createElement(rr,$n({},i,{className:"recharts-polar-radius-axis-tick-value"}),a),o}}])}(R.PureComponent);uo(co,"displayName","PolarRadiusAxis");uo(co,"axisType","radiusAxis");uo(co,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});function Nr(e){"@babel/helpers - typeof";return Nr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nr(e)}function Gt(){return Gt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Gt.apply(this,arguments)}function zp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ut(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?zp(Object(r),!0).forEach(function(n){so(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):zp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function uR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Up(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Py(n.key),n)}}function cR(e,t,r){return t&&Up(e.prototype,t),r&&Up(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function sR(e,t,r){return t=ba(t),lR(e,Sy()?Reflect.construct(t,r||[],ba(e).constructor):t.apply(e,r))}function lR(e,t){if(t&&(Nr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return fR(e)}function fR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Sy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Sy=function(){return!!e})()}function ba(e){return ba=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ba(e)}function pR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&nc(e,t)}function nc(e,t){return nc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},nc(e,t)}function so(e,t,r){return t=Py(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Py(e){var t=hR(e,"string");return Nr(t)=="symbol"?t:t+""}function hR(e,t){if(Nr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Nr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var dR=Math.PI/180,qp=1e-5,lo=function(e){function t(){return uR(this,t),sR(this,t,arguments)}return pR(t,e),cR(t,[{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.cx,o=i.cy,u=i.radius,c=i.orientation,s=i.tickSize,f=s||8,l=se(a,o,u,n.coordinate),p=se(a,o,u+(c==="inner"?-1:1)*f,n.coordinate);return{x1:l.x,y1:l.y,x2:p.x,y2:p.y}}},{key:"getTickTextAnchor",value:function(n){var i=this.props.orientation,a=Math.cos(-n.coordinate*dR),o;return a>qp?o=i==="outer"?"start":"end":a<-qp?o=i==="outer"?"end":"start":o="middle",o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.radius,u=n.axisLine,c=n.axisLineType,s=Ut(Ut({},H(this.props,!1)),{},{fill:"none"},H(u,!1));if(c==="circle")return S.createElement(oo,Gt({className:"recharts-polar-angle-axis-line"},s,{cx:i,cy:a,r:o}));var f=this.props.ticks,l=f.map(function(p){return se(i,a,o,p.coordinate)});return S.createElement(EN,Gt({className:"recharts-polar-angle-axis-line"},s,{points:l}))}},{key:"renderTicks",value:function(){var n=this,i=this.props,a=i.ticks,o=i.tick,u=i.tickLine,c=i.tickFormatter,s=i.stroke,f=H(this.props,!1),l=H(o,!1),p=Ut(Ut({},f),{},{fill:"none"},H(u,!1)),h=a.map(function(v,d){var y=n.getTickLineCoord(v),b=n.getTickTextAnchor(v),w=Ut(Ut(Ut({textAnchor:b},f),{},{stroke:"none",fill:s},l),{},{index:d,payload:v,x:y.x2,y:y.y2});return S.createElement(te,Gt({className:J("recharts-polar-angle-axis-tick",cy(o)),key:"tick-".concat(v.coordinate)},tr(n.props,v,d)),u&&S.createElement("line",Gt({className:"recharts-polar-angle-axis-tick-line"},p,y)),o&&t.renderTickItem(o,w,c?c(v.value,d):v.value))});return S.createElement(te,{className:"recharts-polar-angle-axis-ticks"},h)}},{key:"render",value:function(){var n=this.props,i=n.ticks,a=n.radius,o=n.axisLine;return a<=0||!i||!i.length?null:S.createElement(te,{className:J("recharts-polar-angle-axis",this.props.className)},o&&this.renderAxisLine(),this.renderTicks())}}],[{key:"renderTickItem",value:function(n,i,a){var o;return S.isValidElement(n)?o=S.cloneElement(n,i):X(n)?o=n(i):o=S.createElement(rr,Gt({},i,{className:"recharts-polar-angle-axis-tick-value"}),a),o}}])}(R.PureComponent);so(lo,"displayName","PolarAngleAxis");so(lo,"axisType","angleAxis");so(lo,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var vR=Ed,yR=vR(Object.getPrototypeOf,Object),mR=yR,gR=Pt,bR=mR,xR=_t,wR="[object Object]",OR=Function.prototype,AR=Object.prototype,_y=OR.toString,SR=AR.hasOwnProperty,PR=_y.call(Object);function _R(e){if(!xR(e)||gR(e)!=wR)return!1;var t=bR(e);if(t===null)return!0;var r=SR.call(t,"constructor")&&t.constructor;return typeof r=="function"&&r instanceof r&&_y.call(r)==PR}var $R=_R;const TR=ae($R);var ER=Pt,jR=_t,MR="[object Boolean]";function CR(e){return e===!0||e===!1||jR(e)&&ER(e)==MR}var IR=CR;const kR=ae(IR);function ti(e){"@babel/helpers - typeof";return ti=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ti(e)}function xa(){return xa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},xa.apply(this,arguments)}function DR(e,t){return BR(e)||LR(e,t)||RR(e,t)||NR()}function NR(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function RR(e,t){if(e){if(typeof e=="string")return Hp(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Hp(e,t)}}function Hp(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function LR(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function BR(e){if(Array.isArray(e))return e}function Kp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Gp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Kp(Object(r),!0).forEach(function(n){FR(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Kp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function FR(e,t,r){return t=WR(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function WR(e){var t=zR(e,"string");return ti(t)=="symbol"?t:t+""}function zR(e,t){if(ti(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ti(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Vp=function(t,r,n,i,a){var o=n-i,u;return u="M ".concat(t,",").concat(r),u+="L ".concat(t+n,",").concat(r),u+="L ".concat(t+n-o/2,",").concat(r+a),u+="L ".concat(t+n-o/2-i,",").concat(r+a),u+="L ".concat(t,",").concat(r," Z"),u},UR={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},qR=function(t){var r=Gp(Gp({},UR),t),n=R.useRef(),i=R.useState(-1),a=DR(i,2),o=a[0],u=a[1];R.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var x=n.current.getTotalLength();x&&u(x)}catch{}},[]);var c=r.x,s=r.y,f=r.upperWidth,l=r.lowerWidth,p=r.height,h=r.className,v=r.animationEasing,d=r.animationDuration,y=r.animationBegin,b=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||l!==+l||p!==+p||f===0&&l===0||p===0)return null;var w=J("recharts-trapezoid",h);return b?S.createElement(lt,{canBegin:o>0,from:{upperWidth:0,lowerWidth:0,height:p,x:c,y:s},to:{upperWidth:f,lowerWidth:l,height:p,x:c,y:s},duration:d,animationEasing:v,isActive:b},function(x){var A=x.upperWidth,m=x.lowerWidth,g=x.height,O=x.x,P=x.y;return S.createElement(lt,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:d,easing:v},S.createElement("path",xa({},H(r,!0),{className:w,d:Vp(O,P,A,m,g),ref:n})))}):S.createElement("g",null,S.createElement("path",xa({},H(r,!0),{className:w,d:Vp(c,s,f,l,p)})))},HR=["option","shapeType","propTransformer","activeClassName","isActive"];function ri(e){"@babel/helpers - typeof";return ri=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ri(e)}function KR(e,t){if(e==null)return{};var r=GR(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function GR(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Xp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function wa(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Xp(Object(r),!0).forEach(function(n){VR(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Xp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function VR(e,t,r){return t=XR(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function XR(e){var t=YR(e,"string");return ri(t)=="symbol"?t:t+""}function YR(e,t){if(ri(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ri(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function ZR(e,t){return wa(wa({},t),e)}function JR(e,t){return e==="symbols"}function Yp(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return S.createElement(ws,r);case"trapezoid":return S.createElement(qR,r);case"sector":return S.createElement(fy,r);case"symbols":if(JR(t))return S.createElement(Bc,r);break;default:return null}}function QR(e){return R.isValidElement(e)?e.props:e}function $y(e){var t=e.option,r=e.shapeType,n=e.propTransformer,i=n===void 0?ZR:n,a=e.activeClassName,o=a===void 0?"recharts-active-shape":a,u=e.isActive,c=KR(e,HR),s;if(R.isValidElement(t))s=R.cloneElement(t,wa(wa({},c),QR(t)));else if(X(t))s=t(c);else if(TR(t)&&!kR(t)){var f=i(t,c);s=S.createElement(Yp,{shapeType:r,elementProps:f})}else{var l=c;s=S.createElement(Yp,{shapeType:r,elementProps:l})}return u?S.createElement(te,{className:o},s):s}function fo(e,t){return t!=null&&"trapezoids"in e.props}function po(e,t){return t!=null&&"sectors"in e.props}function ni(e,t){return t!=null&&"points"in e.props}function eL(e,t){var r,n,i=e.x===(t==null||(r=t.labelViewBox)===null||r===void 0?void 0:r.x)||e.x===t.x,a=e.y===(t==null||(n=t.labelViewBox)===null||n===void 0?void 0:n.y)||e.y===t.y;return i&&a}function tL(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function rL(e,t){var r=e.x===t.x,n=e.y===t.y,i=e.z===t.z;return r&&n&&i}function nL(e,t){var r;return fo(e,t)?r=eL:po(e,t)?r=tL:ni(e,t)&&(r=rL),r}function iL(e,t){var r;return fo(e,t)?r="trapezoids":po(e,t)?r="sectors":ni(e,t)&&(r="points"),r}function aL(e,t){if(fo(e,t)){var r;return(r=t.tooltipPayload)===null||r===void 0||(r=r[0])===null||r===void 0||(r=r.payload)===null||r===void 0?void 0:r.payload}if(po(e,t)){var n;return(n=t.tooltipPayload)===null||n===void 0||(n=n[0])===null||n===void 0||(n=n.payload)===null||n===void 0?void 0:n.payload}return ni(e,t)?t.payload:{}}function oL(e){var t=e.activeTooltipItem,r=e.graphicalItem,n=e.itemData,i=iL(r,t),a=aL(r,t),o=n.filter(function(c,s){var f=mi(a,c),l=r.props[i].filter(function(v){var d=nL(r,t);return d(v,t)}),p=r.props[i].indexOf(l[l.length-1]),h=s===p;return f&&h}),u=n.indexOf(o[o.length-1]);return u}var Di;function Rr(e){"@babel/helpers - typeof";return Rr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rr(e)}function gr(){return gr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},gr.apply(this,arguments)}function Zp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ue(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Zp(Object(r),!0).forEach(function(n){Ye(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Zp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function uL(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Jp(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Ey(n.key),n)}}function cL(e,t,r){return t&&Jp(e.prototype,t),r&&Jp(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function sL(e,t,r){return t=Oa(t),lL(e,Ty()?Reflect.construct(t,r||[],Oa(e).constructor):t.apply(e,r))}function lL(e,t){if(t&&(Rr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return fL(e)}function fL(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ty(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Ty=function(){return!!e})()}function Oa(e){return Oa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Oa(e)}function pL(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ic(e,t)}function ic(e,t){return ic=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},ic(e,t)}function Ye(e,t,r){return t=Ey(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ey(e){var t=hL(e,"string");return Rr(t)=="symbol"?t:t+""}function hL(e,t){if(Rr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Rr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Rt=function(e){function t(r){var n;return uL(this,t),n=sL(this,t,[r]),Ye(n,"pieRef",null),Ye(n,"sectorRefs",[]),Ye(n,"id",tn("recharts-pie-")),Ye(n,"handleAnimationEnd",function(){var i=n.props.onAnimationEnd;n.setState({isAnimationFinished:!0}),X(i)&&i()}),Ye(n,"handleAnimationStart",function(){var i=n.props.onAnimationStart;n.setState({isAnimationFinished:!1}),X(i)&&i()}),n.state={isAnimationFinished:!r.isAnimationActive,prevIsAnimationActive:r.isAnimationActive,prevAnimationId:r.animationId,sectorToFocus:0},n}return pL(t,e),cL(t,[{key:"isActiveIndex",value:function(n){var i=this.props.activeIndex;return Array.isArray(i)?i.indexOf(n)!==-1:n===i}},{key:"hasActiveIndex",value:function(){var n=this.props.activeIndex;return Array.isArray(n)?n.length!==0:n||n===0}},{key:"renderLabels",value:function(n){var i=this.props.isAnimationActive;if(i&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.label,u=a.labelLine,c=a.dataKey,s=a.valueKey,f=H(this.props,!1),l=H(o,!1),p=H(u,!1),h=o&&o.offsetRadius||20,v=n.map(function(d,y){var b=(d.startAngle+d.endAngle)/2,w=se(d.cx,d.cy,d.outerRadius+h,b),x=ue(ue(ue(ue({},f),d),{},{stroke:"none"},l),{},{index:y,textAnchor:t.getTextAnchor(w.x,d.cx)},w),A=ue(ue(ue(ue({},f),d),{},{fill:"none",stroke:d.fill},p),{},{index:y,points:[se(d.cx,d.cy,d.outerRadius,b),w]}),m=c;return Y(c)&&Y(s)?m="value":Y(c)&&(m=s),S.createElement(te,{key:"label-".concat(d.startAngle,"-").concat(d.endAngle,"-").concat(d.midAngle,"-").concat(y)},u&&t.renderLabelLineItem(u,A,"line"),t.renderLabelItem(o,x,Ae(d,m)))});return S.createElement(te,{className:"recharts-pie-labels"},v)}},{key:"renderSectorsStatically",value:function(n){var i=this,a=this.props,o=a.activeShape,u=a.blendStroke,c=a.inactiveShape;return n.map(function(s,f){if((s==null?void 0:s.startAngle)===0&&(s==null?void 0:s.endAngle)===0&&n.length!==1)return null;var l=i.isActiveIndex(f),p=c&&i.hasActiveIndex()?c:null,h=l?o:p,v=ue(ue({},s),{},{stroke:u?s.fill:s.stroke,tabIndex:-1});return S.createElement(te,gr({ref:function(y){y&&!i.sectorRefs.includes(y)&&i.sectorRefs.push(y)},tabIndex:-1,className:"recharts-pie-sector"},tr(i.props,s,f),{key:"sector-".concat(s==null?void 0:s.startAngle,"-").concat(s==null?void 0:s.endAngle,"-").concat(s.midAngle,"-").concat(f)}),S.createElement($y,gr({option:h,isActive:l,shapeType:"sector"},v)))})}},{key:"renderSectorsWithAnimation",value:function(){var n=this,i=this.props,a=i.sectors,o=i.isAnimationActive,u=i.animationBegin,c=i.animationDuration,s=i.animationEasing,f=i.animationId,l=this.state,p=l.prevSectors,h=l.prevIsAnimationActive;return S.createElement(lt,{begin:u,duration:c,isActive:o,easing:s,from:{t:0},to:{t:1},key:"pie-".concat(f,"-").concat(h),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(v){var d=v.t,y=[],b=a&&a[0],w=b.startAngle;return a.forEach(function(x,A){var m=p&&p[A],g=A>0?He(x,"paddingAngle",0):0;if(m){var O=Ue(m.endAngle-m.startAngle,x.endAngle-x.startAngle),P=ue(ue({},x),{},{startAngle:w+g,endAngle:w+O(d)+g});y.push(P),w=P.endAngle}else{var _=x.endAngle,E=x.startAngle,$=Ue(0,_-E),T=$(d),C=ue(ue({},x),{},{startAngle:w+g,endAngle:w+T+g});y.push(C),w=C.endAngle}}),S.createElement(te,null,n.renderSectorsStatically(y))})}},{key:"attachKeyboardHandlers",value:function(n){var i=this;n.onkeydown=function(a){if(!a.altKey)switch(a.key){case"ArrowLeft":{var o=++i.state.sectorToFocus%i.sectorRefs.length;i.sectorRefs[o].focus(),i.setState({sectorToFocus:o});break}case"ArrowRight":{var u=--i.state.sectorToFocus<0?i.sectorRefs.length-1:i.state.sectorToFocus%i.sectorRefs.length;i.sectorRefs[u].focus(),i.setState({sectorToFocus:u});break}case"Escape":{i.sectorRefs[i.state.sectorToFocus].blur(),i.setState({sectorToFocus:0});break}}}}},{key:"renderSectors",value:function(){var n=this.props,i=n.sectors,a=n.isAnimationActive,o=this.state.prevSectors;return a&&i&&i.length&&(!o||!mi(o,i))?this.renderSectorsWithAnimation():this.renderSectorsStatically(i)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var n=this,i=this.props,a=i.hide,o=i.sectors,u=i.className,c=i.label,s=i.cx,f=i.cy,l=i.innerRadius,p=i.outerRadius,h=i.isAnimationActive,v=this.state.isAnimationFinished;if(a||!o||!o.length||!L(s)||!L(f)||!L(l)||!L(p))return null;var d=J("recharts-pie",u);return S.createElement(te,{tabIndex:this.props.rootTabIndex,className:d,ref:function(b){n.pieRef=b}},this.renderSectors(),c&&this.renderLabels(o),$e.renderCallByParent(this.props,null,!1),(!h||v)&&wt.renderCallByParent(this.props,o,!1))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return i.prevIsAnimationActive!==n.isAnimationActive?{prevIsAnimationActive:n.isAnimationActive,prevAnimationId:n.animationId,curSectors:n.sectors,prevSectors:[],isAnimationFinished:!0}:n.isAnimationActive&&n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curSectors:n.sectors,prevSectors:i.curSectors,isAnimationFinished:!0}:n.sectors!==i.curSectors?{curSectors:n.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(n,i){return n>i?"start":n<i?"end":"middle"}},{key:"renderLabelLineItem",value:function(n,i,a){if(S.isValidElement(n))return S.cloneElement(n,i);if(X(n))return n(i);var o=J("recharts-pie-label-line",typeof n!="boolean"?n.className:"");return S.createElement(ha,gr({},i,{key:a,type:"linear",className:o}))}},{key:"renderLabelItem",value:function(n,i,a){if(S.isValidElement(n))return S.cloneElement(n,i);var o=a;if(X(n)&&(o=n(i),S.isValidElement(o)))return o;var u=J("recharts-pie-label-text",typeof n!="boolean"&&!X(n)?n.className:"");return S.createElement(rr,gr({},i,{alignmentBaseline:"middle",className:u}),o)}}])}(R.PureComponent);Di=Rt;Ye(Rt,"displayName","Pie");Ye(Rt,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!ur.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0});Ye(Rt,"parseDeltaAngle",function(e,t){var r=Ce(t-e),n=Math.min(Math.abs(t-e),360);return r*n});Ye(Rt,"getRealPieData",function(e){var t=e.data,r=e.children,n=H(e,!1),i=Ke(r,Xc);return t&&t.length?t.map(function(a,o){return ue(ue(ue({payload:a},n),a),i&&i[o]&&i[o].props)}):i&&i.length?i.map(function(a){return ue(ue({},n),a.props)}):[]});Ye(Rt,"parseCoordinateOfPie",function(e,t){var r=t.top,n=t.left,i=t.width,a=t.height,o=uy(i,a),u=n+Ie(e.cx,i,i/2),c=r+Ie(e.cy,a,a/2),s=Ie(e.innerRadius,o,0),f=Ie(e.outerRadius,o,o*.8),l=e.maxRadius||Math.sqrt(i*i+a*a)/2;return{cx:u,cy:c,innerRadius:s,outerRadius:f,maxRadius:l}});Ye(Rt,"getComposedData",function(e){var t=e.item,r=e.offset,n=t.type.defaultProps!==void 0?ue(ue({},t.type.defaultProps),t.props):t.props,i=Di.getRealPieData(n);if(!i||!i.length)return null;var a=n.cornerRadius,o=n.startAngle,u=n.endAngle,c=n.paddingAngle,s=n.dataKey,f=n.nameKey,l=n.valueKey,p=n.tooltipType,h=Math.abs(n.minAngle),v=Di.parseCoordinateOfPie(n,r),d=Di.parseDeltaAngle(o,u),y=Math.abs(d),b=s;Y(s)&&Y(l)?(it(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),b="value"):Y(s)&&(it(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),b=l);var w=i.filter(function(P){return Ae(P,b,0)!==0}).length,x=(y>=360?w:w-1)*c,A=y-w*h-x,m=i.reduce(function(P,_){var E=Ae(_,b,0);return P+(L(E)?E:0)},0),g;if(m>0){var O;g=i.map(function(P,_){var E=Ae(P,b,0),$=Ae(P,f,_),T=(L(E)?E:0)/m,C;_?C=O.endAngle+Ce(d)*c*(E!==0?1:0):C=o;var I=C+Ce(d)*((E!==0?h:0)+T*A),M=(C+I)/2,k=(v.innerRadius+v.outerRadius)/2,D=[{name:$,value:E,payload:P,dataKey:b,type:p}],B=se(v.cx,v.cy,k,M);return O=ue(ue(ue({percent:T,cornerRadius:a,name:$,tooltipPayload:D,midAngle:M,middleRadius:k,tooltipPosition:B},P),v),{},{value:Ae(P,b),startAngle:C,endAngle:I,payload:P,paddingAngle:Ce(d)*c}),O})}return ue(ue({},v),{},{sectors:g,data:i})});var dL=Math.ceil,vL=Math.max;function yL(e,t,r,n){for(var i=-1,a=vL(dL((t-e)/(r||1)),0),o=Array(a);a--;)o[n?a:++i]=e,e+=r;return o}var mL=yL,gL=Vd,Qp=1/0,bL=17976931348623157e292;function xL(e){if(!e)return e===0?e:0;if(e=gL(e),e===Qp||e===-Qp){var t=e<0?-1:1;return t*bL}return e===e?e:0}var jy=xL,wL=mL,OL=Va,Ko=jy;function AL(e){return function(t,r,n){return n&&typeof n!="number"&&OL(t,r,n)&&(r=n=void 0),t=Ko(t),r===void 0?(r=t,t=0):r=Ko(r),n=n===void 0?t<r?1:-1:Ko(n),wL(t,r,n,e)}}var SL=AL,PL=SL,_L=PL(),$L=_L;const Aa=ae($L);function ii(e){"@babel/helpers - typeof";return ii=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ii(e)}function eh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function th(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?eh(Object(r),!0).forEach(function(n){My(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function My(e,t,r){return t=TL(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function TL(e){var t=EL(e,"string");return ii(t)=="symbol"?t:t+""}function EL(e,t){if(ii(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ii(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var jL=["Webkit","Moz","O","ms"],ML=function(t,r){var n=t.replace(/(\w)/,function(a){return a.toUpperCase()}),i=jL.reduce(function(a,o){return th(th({},a),{},My({},o+n,r))},{});return i[t]=r,i};function Lr(e){"@babel/helpers - typeof";return Lr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Lr(e)}function Sa(){return Sa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Sa.apply(this,arguments)}function rh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Go(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?rh(Object(r),!0).forEach(function(n){We(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function CL(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function nh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Iy(n.key),n)}}function IL(e,t,r){return t&&nh(e.prototype,t),r&&nh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function kL(e,t,r){return t=Pa(t),DL(e,Cy()?Reflect.construct(t,r||[],Pa(e).constructor):t.apply(e,r))}function DL(e,t){if(t&&(Lr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return NL(e)}function NL(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Cy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Cy=function(){return!!e})()}function Pa(e){return Pa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Pa(e)}function RL(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ac(e,t)}function ac(e,t){return ac=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},ac(e,t)}function We(e,t,r){return t=Iy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Iy(e){var t=LL(e,"string");return Lr(t)=="symbol"?t:t+""}function LL(e,t){if(Lr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Lr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var BL=function(t){var r=t.data,n=t.startIndex,i=t.endIndex,a=t.x,o=t.width,u=t.travellerWidth;if(!r||!r.length)return{};var c=r.length,s=An().domain(Aa(0,c)).range([a,a+o-u]),f=s.domain().map(function(l){return s(l)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:s(n),endX:s(i),scale:s,scaleValues:f}},ih=function(t){return t.changedTouches&&!!t.changedTouches.length},Br=function(e){function t(r){var n;return CL(this,t),n=kL(this,t,[r]),We(n,"handleDrag",function(i){n.leaveTimer&&(clearTimeout(n.leaveTimer),n.leaveTimer=null),n.state.isTravellerMoving?n.handleTravellerMove(i):n.state.isSlideMoving&&n.handleSlideDrag(i)}),We(n,"handleTouchMove",function(i){i.changedTouches!=null&&i.changedTouches.length>0&&n.handleDrag(i.changedTouches[0])}),We(n,"handleDragEnd",function(){n.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var i=n.props,a=i.endIndex,o=i.onDragEnd,u=i.startIndex;o==null||o({endIndex:a,startIndex:u})}),n.detachDragEndListener()}),We(n,"handleLeaveWrapper",function(){(n.state.isTravellerMoving||n.state.isSlideMoving)&&(n.leaveTimer=window.setTimeout(n.handleDragEnd,n.props.leaveTimeOut))}),We(n,"handleEnterSlideOrTraveller",function(){n.setState({isTextActive:!0})}),We(n,"handleLeaveSlideOrTraveller",function(){n.setState({isTextActive:!1})}),We(n,"handleSlideDragStart",function(i){var a=ih(i)?i.changedTouches[0]:i;n.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:a.pageX}),n.attachDragEndListener()}),n.travellerDragStartHandlers={startX:n.handleTravellerDragStart.bind(n,"startX"),endX:n.handleTravellerDragStart.bind(n,"endX")},n.state={},n}return RL(t,e),IL(t,[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(n){var i=n.startX,a=n.endX,o=this.state.scaleValues,u=this.props,c=u.gap,s=u.data,f=s.length-1,l=Math.min(i,a),p=Math.max(i,a),h=t.getIndexInRange(o,l),v=t.getIndexInRange(o,p);return{startIndex:h-h%c,endIndex:v===f?f:v-v%c}}},{key:"getTextOfTick",value:function(n){var i=this.props,a=i.data,o=i.tickFormatter,u=i.dataKey,c=Ae(a[n],u,n);return X(o)?o(c,n):c}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(n){var i=this.state,a=i.slideMoveStartX,o=i.startX,u=i.endX,c=this.props,s=c.x,f=c.width,l=c.travellerWidth,p=c.startIndex,h=c.endIndex,v=c.onChange,d=n.pageX-a;d>0?d=Math.min(d,s+f-l-u,s+f-l-o):d<0&&(d=Math.max(d,s-o,s-u));var y=this.getIndex({startX:o+d,endX:u+d});(y.startIndex!==p||y.endIndex!==h)&&v&&v(y),this.setState({startX:o+d,endX:u+d,slideMoveStartX:n.pageX})}},{key:"handleTravellerDragStart",value:function(n,i){var a=ih(i)?i.changedTouches[0]:i;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:n,brushMoveStartX:a.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(n){var i=this.state,a=i.brushMoveStartX,o=i.movingTravellerId,u=i.endX,c=i.startX,s=this.state[o],f=this.props,l=f.x,p=f.width,h=f.travellerWidth,v=f.onChange,d=f.gap,y=f.data,b={startX:this.state.startX,endX:this.state.endX},w=n.pageX-a;w>0?w=Math.min(w,l+p-h-s):w<0&&(w=Math.max(w,l-s)),b[o]=s+w;var x=this.getIndex(b),A=x.startIndex,m=x.endIndex,g=function(){var P=y.length-1;return o==="startX"&&(u>c?A%d===0:m%d===0)||u<c&&m===P||o==="endX"&&(u>c?m%d===0:A%d===0)||u>c&&m===P};this.setState(We(We({},o,s+w),"brushMoveStartX",n.pageX),function(){v&&g()&&v(x)})}},{key:"handleTravellerMoveKeyboard",value:function(n,i){var a=this,o=this.state,u=o.scaleValues,c=o.startX,s=o.endX,f=this.state[i],l=u.indexOf(f);if(l!==-1){var p=l+n;if(!(p===-1||p>=u.length)){var h=u[p];i==="startX"&&h>=s||i==="endX"&&h<=c||this.setState(We({},i,h),function(){a.props.onChange(a.getIndex({startX:a.state.startX,endX:a.state.endX}))})}}}},{key:"renderBackground",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.fill,s=n.stroke;return S.createElement("rect",{stroke:s,fill:c,x:i,y:a,width:o,height:u})}},{key:"renderPanorama",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.data,s=n.children,f=n.padding,l=R.Children.only(s);return l?S.cloneElement(l,{x:i,y:a,width:o,height:u,margin:f,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(n,i){var a,o,u=this,c=this.props,s=c.y,f=c.travellerWidth,l=c.height,p=c.traveller,h=c.ariaLabel,v=c.data,d=c.startIndex,y=c.endIndex,b=Math.max(n,this.props.x),w=Go(Go({},H(this.props,!1)),{},{x:b,y:s,width:f,height:l}),x=h||"Min value: ".concat((a=v[d])===null||a===void 0?void 0:a.name,", Max value: ").concat((o=v[y])===null||o===void 0?void 0:o.name);return S.createElement(te,{tabIndex:0,role:"slider","aria-label":x,"aria-valuenow":n,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[i],onTouchStart:this.travellerDragStartHandlers[i],onKeyDown:function(m){["ArrowLeft","ArrowRight"].includes(m.key)&&(m.preventDefault(),m.stopPropagation(),u.handleTravellerMoveKeyboard(m.key==="ArrowRight"?1:-1,i))},onFocus:function(){u.setState({isTravellerFocused:!0})},onBlur:function(){u.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},t.renderTraveller(p,w))}},{key:"renderSlide",value:function(n,i){var a=this.props,o=a.y,u=a.height,c=a.stroke,s=a.travellerWidth,f=Math.min(n,i)+s,l=Math.max(Math.abs(i-n)-s,0);return S.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:c,fillOpacity:.2,x:f,y:o,width:l,height:u})}},{key:"renderText",value:function(){var n=this.props,i=n.startIndex,a=n.endIndex,o=n.y,u=n.height,c=n.travellerWidth,s=n.stroke,f=this.state,l=f.startX,p=f.endX,h=5,v={pointerEvents:"none",fill:s};return S.createElement(te,{className:"recharts-brush-texts"},S.createElement(rr,Sa({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,p)-h,y:o+u/2},v),this.getTextOfTick(i)),S.createElement(rr,Sa({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,p)+c+h,y:o+u/2},v),this.getTextOfTick(a)))}},{key:"render",value:function(){var n=this.props,i=n.data,a=n.className,o=n.children,u=n.x,c=n.y,s=n.width,f=n.height,l=n.alwaysShowText,p=this.state,h=p.startX,v=p.endX,d=p.isTextActive,y=p.isSlideMoving,b=p.isTravellerMoving,w=p.isTravellerFocused;if(!i||!i.length||!L(u)||!L(c)||!L(s)||!L(f)||s<=0||f<=0)return null;var x=J("recharts-brush",a),A=S.Children.count(o)===1,m=ML("userSelect","none");return S.createElement(te,{className:x,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:m},this.renderBackground(),A&&this.renderPanorama(),this.renderSlide(h,v),this.renderTravellerLayer(h,"startX"),this.renderTravellerLayer(v,"endX"),(d||y||b||w||l)&&this.renderText())}}],[{key:"renderDefaultTraveller",value:function(n){var i=n.x,a=n.y,o=n.width,u=n.height,c=n.stroke,s=Math.floor(a+u/2)-1;return S.createElement(S.Fragment,null,S.createElement("rect",{x:i,y:a,width:o,height:u,fill:c,stroke:"none"}),S.createElement("line",{x1:i+1,y1:s,x2:i+o-1,y2:s,fill:"none",stroke:"#fff"}),S.createElement("line",{x1:i+1,y1:s+2,x2:i+o-1,y2:s+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(n,i){var a;return S.isValidElement(n)?a=S.cloneElement(n,i):X(n)?a=n(i):a=t.renderDefaultTraveller(i),a}},{key:"getDerivedStateFromProps",value:function(n,i){var a=n.data,o=n.width,u=n.x,c=n.travellerWidth,s=n.updateId,f=n.startIndex,l=n.endIndex;if(a!==i.prevData||s!==i.prevUpdateId)return Go({prevData:a,prevTravellerWidth:c,prevUpdateId:s,prevX:u,prevWidth:o},a&&a.length?BL({data:a,width:o,x:u,travellerWidth:c,startIndex:f,endIndex:l}):{scale:null,scaleValues:null});if(i.scale&&(o!==i.prevWidth||u!==i.prevX||c!==i.prevTravellerWidth)){i.scale.range([u,u+o-c]);var p=i.scale.domain().map(function(h){return i.scale(h)});return{prevData:a,prevTravellerWidth:c,prevUpdateId:s,prevX:u,prevWidth:o,startX:i.scale(n.startIndex),endX:i.scale(n.endIndex),scaleValues:p}}return null}},{key:"getIndexInRange",value:function(n,i){for(var a=n.length,o=0,u=a-1;u-o>1;){var c=Math.floor((o+u)/2);n[c]>i?u=c:o=c}return i>=n[u]?u:o}}])}(R.PureComponent);We(Br,"displayName","Brush");We(Br,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var FL=Kc;function WL(e,t){var r;return FL(e,function(n,i,a){return r=t(n,i,a),!r}),!!r}var zL=WL,UL=wd,qL=pt,HL=zL,KL=Le,GL=Va;function VL(e,t,r){var n=KL(e)?UL:HL;return r&&GL(e,t,r)&&(t=void 0),n(e,qL(t))}var XL=VL;const YL=ae(XL);var ct=function(t,r){var n=t.alwaysShow,i=t.ifOverflow;return n&&(i="extendDomain"),i===r},ah=Ud;function ZL(e,t,r){t=="__proto__"&&ah?ah(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}var JL=ZL,QL=JL,eB=Wd,tB=pt;function rB(e,t){var r={};return t=tB(t),eB(e,function(n,i,a){QL(r,i,t(n,i,a))}),r}var nB=rB;const iB=ae(nB);function aB(e,t){for(var r=-1,n=e==null?0:e.length;++r<n;)if(!t(e[r],r,e))return!1;return!0}var oB=aB,uB=Kc;function cB(e,t){var r=!0;return uB(e,function(n,i,a){return r=!!t(n,i,a),r}),r}var sB=cB,lB=oB,fB=sB,pB=pt,hB=Le,dB=Va;function vB(e,t,r){var n=hB(e)?lB:fB;return r&&dB(e,t,r)&&(t=void 0),n(e,pB(t))}var yB=vB;const ky=ae(yB);var mB=["x","y"];function ai(e){"@babel/helpers - typeof";return ai=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ai(e)}function oc(){return oc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},oc.apply(this,arguments)}function oh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function xn(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?oh(Object(r),!0).forEach(function(n){gB(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):oh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function gB(e,t,r){return t=bB(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bB(e){var t=xB(e,"string");return ai(t)=="symbol"?t:t+""}function xB(e,t){if(ai(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ai(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function wB(e,t){if(e==null)return{};var r=OB(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function OB(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function AB(e,t){var r=e.x,n=e.y,i=wB(e,mB),a="".concat(r),o=parseInt(a,10),u="".concat(n),c=parseInt(u,10),s="".concat(t.height||i.height),f=parseInt(s,10),l="".concat(t.width||i.width),p=parseInt(l,10);return xn(xn(xn(xn(xn({},t),i),o?{x:o}:{}),c?{y:c}:{}),{},{height:f,width:p,name:t.name,radius:t.radius})}function uh(e){return S.createElement($y,oc({shapeType:"rectangle",propTransformer:AB,activeClassName:"recharts-active-bar"},e))}var SB=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return function(n,i){if(typeof t=="number")return t;var a=L(n)||X0(n);return a?t(n,i):(a||ir(),r)}},PB=["value","background"],Dy;function Fr(e){"@babel/helpers - typeof";return Fr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fr(e)}function _B(e,t){if(e==null)return{};var r=$B(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function $B(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function _a(){return _a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},_a.apply(this,arguments)}function ch(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function me(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ch(Object(r),!0).forEach(function(n){Mt(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ch(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function TB(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function sh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Ry(n.key),n)}}function EB(e,t,r){return t&&sh(e.prototype,t),r&&sh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function jB(e,t,r){return t=$a(t),MB(e,Ny()?Reflect.construct(t,r||[],$a(e).constructor):t.apply(e,r))}function MB(e,t){if(t&&(Fr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return CB(e)}function CB(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ny(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Ny=function(){return!!e})()}function $a(e){return $a=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},$a(e)}function IB(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&uc(e,t)}function uc(e,t){return uc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},uc(e,t)}function Mt(e,t,r){return t=Ry(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ry(e){var t=kB(e,"string");return Fr(t)=="symbol"?t:t+""}function kB(e,t){if(Fr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Fr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var sn=function(e){function t(){var r;TB(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=jB(this,t,[].concat(i)),Mt(r,"state",{isAnimationFinished:!1}),Mt(r,"id",tn("recharts-bar-")),Mt(r,"handleAnimationEnd",function(){var o=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),o&&o()}),Mt(r,"handleAnimationStart",function(){var o=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),o&&o()}),r}return IB(t,e),EB(t,[{key:"renderRectanglesStatically",value:function(n){var i=this,a=this.props,o=a.shape,u=a.dataKey,c=a.activeIndex,s=a.activeBar,f=H(this.props,!1);return n&&n.map(function(l,p){var h=p===c,v=h?s:o,d=me(me(me({},f),l),{},{isActive:h,option:v,index:p,dataKey:u,onAnimationStart:i.handleAnimationStart,onAnimationEnd:i.handleAnimationEnd});return S.createElement(te,_a({className:"recharts-bar-rectangle"},tr(i.props,l,p),{key:"rectangle-".concat(l==null?void 0:l.x,"-").concat(l==null?void 0:l.y,"-").concat(l==null?void 0:l.value,"-").concat(p)}),S.createElement(uh,d))})}},{key:"renderRectanglesWithAnimation",value:function(){var n=this,i=this.props,a=i.data,o=i.layout,u=i.isAnimationActive,c=i.animationBegin,s=i.animationDuration,f=i.animationEasing,l=i.animationId,p=this.state.prevData;return S.createElement(lt,{begin:c,duration:s,isActive:u,easing:f,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(h){var v=h.t,d=a.map(function(y,b){var w=p&&p[b];if(w){var x=Ue(w.x,y.x),A=Ue(w.y,y.y),m=Ue(w.width,y.width),g=Ue(w.height,y.height);return me(me({},y),{},{x:x(v),y:A(v),width:m(v),height:g(v)})}if(o==="horizontal"){var O=Ue(0,y.height),P=O(v);return me(me({},y),{},{y:y.y+y.height-P,height:P})}var _=Ue(0,y.width),E=_(v);return me(me({},y),{},{width:E})});return S.createElement(te,null,n.renderRectanglesStatically(d))})}},{key:"renderRectangles",value:function(){var n=this.props,i=n.data,a=n.isAnimationActive,o=this.state.prevData;return a&&i&&i.length&&(!o||!mi(o,i))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(i)}},{key:"renderBackground",value:function(){var n=this,i=this.props,a=i.data,o=i.dataKey,u=i.activeIndex,c=H(this.props.background,!1);return a.map(function(s,f){s.value;var l=s.background,p=_B(s,PB);if(!l)return null;var h=me(me(me(me(me({},p),{},{fill:"#eee"},l),c),tr(n.props,s,f)),{},{onAnimationStart:n.handleAnimationStart,onAnimationEnd:n.handleAnimationEnd,dataKey:o,index:f,className:"recharts-bar-background-rectangle"});return S.createElement(uh,_a({key:"background-bar-".concat(f),option:n.props.background,isActive:f===u},h))})}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.data,u=a.xAxis,c=a.yAxis,s=a.layout,f=a.children,l=Ke(f,gi);if(!l)return null;var p=s==="vertical"?o[0].height/2:o[0].width/2,h=function(y,b){var w=Array.isArray(y.value)?y.value[1]:y.value;return{x:y.x,y:y.y,value:w,errorVal:Ae(y,b)}},v={clipPath:n?"url(#clipPath-".concat(i,")"):null};return S.createElement(te,v,l.map(function(d){return S.cloneElement(d,{key:"error-bar-".concat(i,"-").concat(d.props.dataKey),data:o,xAxis:u,yAxis:c,layout:s,offset:p,dataPointFormatter:h})}))}},{key:"render",value:function(){var n=this.props,i=n.hide,a=n.data,o=n.className,u=n.xAxis,c=n.yAxis,s=n.left,f=n.top,l=n.width,p=n.height,h=n.isAnimationActive,v=n.background,d=n.id;if(i||!a||!a.length)return null;var y=this.state.isAnimationFinished,b=J("recharts-bar",o),w=u&&u.allowDataOverflow,x=c&&c.allowDataOverflow,A=w||x,m=Y(d)?this.id:d;return S.createElement(te,{className:b},w||x?S.createElement("defs",null,S.createElement("clipPath",{id:"clipPath-".concat(m)},S.createElement("rect",{x:w?s:s-l/2,y:x?f:f-p/2,width:w?l:l*2,height:x?p:p*2}))):null,S.createElement(te,{className:"recharts-bar-rectangles",clipPath:A?"url(#clipPath-".concat(m,")"):null},v?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(A,m),(!h||y)&&wt.renderCallByParent(this.props,a))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curData:n.data,prevData:i.curData}:n.data!==i.curData?{curData:n.data}:null}}])}(R.PureComponent);Dy=sn;Mt(sn,"displayName","Bar");Mt(sn,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!ur.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"});Mt(sn,"getComposedData",function(e){var t=e.props,r=e.item,n=e.barPosition,i=e.bandSize,a=e.xAxis,o=e.yAxis,u=e.xAxisTicks,c=e.yAxisTicks,s=e.stackedData,f=e.dataStartIndex,l=e.displayedData,p=e.offset,h=dk(n,r);if(!h)return null;var v=t.layout,d=r.type.defaultProps,y=d!==void 0?me(me({},d),r.props):r.props,b=y.dataKey,w=y.children,x=y.minPointSize,A=v==="horizontal"?o:a,m=s?A.scale.domain():null,g=wk({numericAxis:A}),O=Ke(w,Xc),P=l.map(function(_,E){var $,T,C,I,M,k;s?$=vk(s[f+E],m):($=Ae(_,b),Array.isArray($)||($=[g,$]));var D=SB(x,Dy.defaultProps.minPointSize)($[1],E);if(v==="horizontal"){var B,F=[o.scale($[0]),o.scale($[1])],q=F[0],G=F[1];T=Jf({axis:a,ticks:u,bandSize:i,offset:h.offset,entry:_,index:E}),C=(B=G??q)!==null&&B!==void 0?B:void 0,I=h.size;var z=q-G;if(M=Number.isNaN(z)?0:z,k={x:T,y:o.y,width:I,height:o.height},Math.abs(D)>0&&Math.abs(M)<Math.abs(D)){var V=Ce(M||D)*(Math.abs(D)-Math.abs(M));C-=V,M+=V}}else{var le=[a.scale($[0]),a.scale($[1])],ye=le[0],Be=le[1];if(T=ye,C=Jf({axis:o,ticks:c,bandSize:i,offset:h.offset,entry:_,index:E}),I=Be-ye,M=h.size,k={x:a.x,y:C,width:a.width,height:M},Math.abs(D)>0&&Math.abs(I)<Math.abs(D)){var Lt=Ce(I||D)*(Math.abs(D)-Math.abs(I));I+=Lt}}return me(me(me({},_),{},{x:T,y:C,width:I,height:M,value:s?$:$[1],payload:_,background:k},O&&O[E]&&O[E].props),{},{tooltipPayload:[ay(r,_)],tooltipPosition:{x:T+I/2,y:C+M/2}})});return me({data:P,layout:v},p)});function oi(e){"@babel/helpers - typeof";return oi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},oi(e)}function DB(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function lh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Ly(n.key),n)}}function NB(e,t,r){return t&&lh(e.prototype,t),r&&lh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function fh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function tt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?fh(Object(r),!0).forEach(function(n){ho(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ho(e,t,r){return t=Ly(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ly(e){var t=RB(e,"string");return oi(t)=="symbol"?t:t+""}function RB(e,t){if(oi(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(oi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var By=function(t,r,n,i,a){var o=t.width,u=t.height,c=t.layout,s=t.children,f=Object.keys(r),l={left:n.left,leftMirror:n.left,right:o-n.right,rightMirror:o-n.right,top:n.top,topMirror:n.top,bottom:u-n.bottom,bottomMirror:u-n.bottom},p=!!ze(s,sn);return f.reduce(function(h,v){var d=r[v],y=d.orientation,b=d.domain,w=d.padding,x=w===void 0?{}:w,A=d.mirror,m=d.reversed,g="".concat(y).concat(A?"Mirror":""),O,P,_,E,$;if(d.type==="number"&&(d.padding==="gap"||d.padding==="no-gap")){var T=b[1]-b[0],C=1/0,I=d.categoricalDomain.sort(J0);if(I.forEach(function(le,ye){ye>0&&(C=Math.min((le||0)-(I[ye-1]||0),C))}),Number.isFinite(C)){var M=C/T,k=d.layout==="vertical"?n.height:n.width;if(d.padding==="gap"&&(O=M*k/2),d.padding==="no-gap"){var D=Ie(t.barCategoryGap,M*k),B=M*k/2;O=B-D-(B-D)/k*D}}}i==="xAxis"?P=[n.left+(x.left||0)+(O||0),n.left+n.width-(x.right||0)-(O||0)]:i==="yAxis"?P=c==="horizontal"?[n.top+n.height-(x.bottom||0),n.top+(x.top||0)]:[n.top+(x.top||0)+(O||0),n.top+n.height-(x.bottom||0)-(O||0)]:P=d.range,m&&(P=[P[1],P[0]]);var F=ty(d,a,p),q=F.scale,G=F.realScaleType;q.domain(b).range(P),ry(q);var z=ny(q,tt(tt({},d),{},{realScaleType:G}));i==="xAxis"?($=y==="top"&&!A||y==="bottom"&&A,_=n.left,E=l[g]-$*d.height):i==="yAxis"&&($=y==="left"&&!A||y==="right"&&A,_=l[g]-$*d.width,E=n.top);var V=tt(tt(tt({},d),z),{},{realScaleType:G,x:_,y:E,scale:q,width:i==="xAxis"?n.width:d.width,height:i==="yAxis"?n.height:d.height});return V.bandSize=la(V,z),!d.hide&&i==="xAxis"?l[g]+=($?-1:1)*V.height:d.hide||(l[g]+=($?-1:1)*V.width),tt(tt({},h),{},ho({},v,V))},{})},Fy=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return{x:Math.min(n,a),y:Math.min(i,o),width:Math.abs(a-n),height:Math.abs(o-i)}},LB=function(t){var r=t.x1,n=t.y1,i=t.x2,a=t.y2;return Fy({x:r,y:n},{x:i,y:a})},Wy=function(){function e(t){DB(this,e),this.scale=t}return NB(e,[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=n.bandAware,a=n.position;if(r!==void 0){if(a)switch(a){case"start":return this.scale(r);case"middle":{var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+o}case"end":{var u=this.bandwidth?this.bandwidth():0;return this.scale(r)+u}default:return this.scale(r)}if(i){var c=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+c}return this.scale(r)}}},{key:"isInRange",value:function(r){var n=this.range(),i=n[0],a=n[n.length-1];return i<=a?r>=i&&r<=a:r>=a&&r<=i}}],[{key:"create",value:function(r){return new e(r)}}])}();ho(Wy,"EPS",1e-4);var Os=function(t){var r=Object.keys(t).reduce(function(n,i){return tt(tt({},n),{},ho({},i,Wy.create(t[i])))},{});return tt(tt({},r),{},{apply:function(i){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=a.bandAware,u=a.position;return iB(i,function(c,s){return r[s].apply(c,{bandAware:o,position:u})})},isInRange:function(i){return ky(i,function(a,o){return r[o].isInRange(a)})}})};function BB(e){return(e%180+180)%180}var FB=function(t){var r=t.width,n=t.height,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=BB(i),o=a*Math.PI/180,u=Math.atan(n/r),c=o>u&&o<Math.PI-u?n/Math.sin(o):r/Math.cos(o);return Math.abs(c)},WB=pt,zB=pi,UB=Ka;function qB(e){return function(t,r,n){var i=Object(t);if(!zB(t)){var a=WB(r);t=UB(t),r=function(u){return a(i[u],u,i)}}var o=e(t,r,n);return o>-1?i[a?t[o]:o]:void 0}}var HB=qB,KB=jy;function GB(e){var t=KB(e),r=t%1;return t===t?r?t-r:t:0}var VB=GB,XB=Dd,YB=pt,ZB=VB,JB=Math.max;function QB(e,t,r){var n=e==null?0:e.length;if(!n)return-1;var i=r==null?0:ZB(r);return i<0&&(i=JB(n+i,0)),XB(e,YB(t),i)}var eF=QB,tF=HB,rF=eF,nF=tF(rF),iF=nF;const aF=ae(iF);var oF=i0(function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}},function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")}),As=R.createContext(void 0),Ss=R.createContext(void 0),zy=R.createContext(void 0),Uy=R.createContext({}),qy=R.createContext(void 0),Hy=R.createContext(0),Ky=R.createContext(0),ph=function(t){var r=t.state,n=r.xAxisMap,i=r.yAxisMap,a=r.offset,o=t.clipPathId,u=t.children,c=t.width,s=t.height,f=oF(a);return S.createElement(As.Provider,{value:n},S.createElement(Ss.Provider,{value:i},S.createElement(Uy.Provider,{value:a},S.createElement(zy.Provider,{value:f},S.createElement(qy.Provider,{value:o},S.createElement(Hy.Provider,{value:s},S.createElement(Ky.Provider,{value:c},u)))))))},uF=function(){return R.useContext(qy)},Gy=function(t){var r=R.useContext(As);r==null&&ir();var n=r[t];return n==null&&ir(),n},cF=function(){var t=R.useContext(As);return jt(t)},sF=function(){var t=R.useContext(Ss),r=aF(t,function(n){return ky(n.domain,Number.isFinite)});return r||jt(t)},Vy=function(t){var r=R.useContext(Ss);r==null&&ir();var n=r[t];return n==null&&ir(),n},lF=function(){var t=R.useContext(zy);return t},fF=function(){return R.useContext(Uy)},Ps=function(){return R.useContext(Ky)},_s=function(){return R.useContext(Hy)};function Wr(e){"@babel/helpers - typeof";return Wr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wr(e)}function pF(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function hF(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Yy(n.key),n)}}function dF(e,t,r){return t&&hF(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function vF(e,t,r){return t=Ta(t),yF(e,Xy()?Reflect.construct(t,r||[],Ta(e).constructor):t.apply(e,r))}function yF(e,t){if(t&&(Wr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return mF(e)}function mF(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Xy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Xy=function(){return!!e})()}function Ta(e){return Ta=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ta(e)}function gF(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&cc(e,t)}function cc(e,t){return cc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},cc(e,t)}function hh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function dh(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?hh(Object(r),!0).forEach(function(n){$s(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function $s(e,t,r){return t=Yy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Yy(e){var t=bF(e,"string");return Wr(t)=="symbol"?t:t+""}function bF(e,t){if(Wr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Wr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function xF(e,t){return SF(e)||AF(e,t)||OF(e,t)||wF()}function wF(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function OF(e,t){if(e){if(typeof e=="string")return vh(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return vh(e,t)}}function vh(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function AF(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function SF(e){if(Array.isArray(e))return e}function sc(){return sc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},sc.apply(this,arguments)}var PF=function(t,r){var n;return S.isValidElement(t)?n=S.cloneElement(t,r):X(t)?n=t(r):n=S.createElement("line",sc({},r,{className:"recharts-reference-line-line"})),n},_F=function(t,r,n,i,a,o,u,c,s){var f=a.x,l=a.y,p=a.width,h=a.height;if(n){var v=s.y,d=t.y.apply(v,{position:o});if(ct(s,"discard")&&!t.y.isInRange(d))return null;var y=[{x:f+p,y:d},{x:f,y:d}];return c==="left"?y.reverse():y}if(r){var b=s.x,w=t.x.apply(b,{position:o});if(ct(s,"discard")&&!t.x.isInRange(w))return null;var x=[{x:w,y:l+h},{x:w,y:l}];return u==="top"?x.reverse():x}if(i){var A=s.segment,m=A.map(function(g){return t.apply(g,{position:o})});return ct(s,"discard")&&YL(m,function(g){return!t.isInRange(g)})?null:m}return null};function $F(e){var t=e.x,r=e.y,n=e.segment,i=e.xAxisId,a=e.yAxisId,o=e.shape,u=e.className,c=e.alwaysShow,s=uF(),f=Gy(i),l=Vy(a),p=lF();if(!s||!p)return null;it(c===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var h=Os({x:f.scale,y:l.scale}),v=Se(t),d=Se(r),y=n&&n.length===2,b=_F(h,v,d,y,p,e.position,f.orientation,l.orientation,e);if(!b)return null;var w=xF(b,2),x=w[0],A=x.x,m=x.y,g=w[1],O=g.x,P=g.y,_=ct(e,"hidden")?"url(#".concat(s,")"):void 0,E=dh(dh({clipPath:_},H(e,!0)),{},{x1:A,y1:m,x2:O,y2:P});return S.createElement(te,{className:J("recharts-reference-line",u)},PF(o,E),$e.renderCallByParent(e,LB({x1:A,y1:m,x2:O,y2:P})))}var Ts=function(e){function t(){return pF(this,t),vF(this,t,arguments)}return gF(t,e),dF(t,[{key:"render",value:function(){return S.createElement($F,this.props)}}])}(S.Component);$s(Ts,"displayName","ReferenceLine");$s(Ts,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function lc(){return lc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},lc.apply(this,arguments)}function zr(e){"@babel/helpers - typeof";return zr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},zr(e)}function yh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function mh(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?yh(Object(r),!0).forEach(function(n){vo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function TF(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function EF(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Jy(n.key),n)}}function jF(e,t,r){return t&&EF(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function MF(e,t,r){return t=Ea(t),CF(e,Zy()?Reflect.construct(t,r||[],Ea(e).constructor):t.apply(e,r))}function CF(e,t){if(t&&(zr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return IF(e)}function IF(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Zy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Zy=function(){return!!e})()}function Ea(e){return Ea=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ea(e)}function kF(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&fc(e,t)}function fc(e,t){return fc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},fc(e,t)}function vo(e,t,r){return t=Jy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Jy(e){var t=DF(e,"string");return zr(t)=="symbol"?t:t+""}function DF(e,t){if(zr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(zr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var NF=function(t){var r=t.x,n=t.y,i=t.xAxis,a=t.yAxis,o=Os({x:i.scale,y:a.scale}),u=o.apply({x:r,y:n},{bandAware:!0});return ct(t,"discard")&&!o.isInRange(u)?null:u},yo=function(e){function t(){return TF(this,t),MF(this,t,arguments)}return kF(t,e),jF(t,[{key:"render",value:function(){var n=this.props,i=n.x,a=n.y,o=n.r,u=n.alwaysShow,c=n.clipPathId,s=Se(i),f=Se(a);if(it(u===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!s||!f)return null;var l=NF(this.props);if(!l)return null;var p=l.x,h=l.y,v=this.props,d=v.shape,y=v.className,b=ct(this.props,"hidden")?"url(#".concat(c,")"):void 0,w=mh(mh({clipPath:b},H(this.props,!0)),{},{cx:p,cy:h});return S.createElement(te,{className:J("recharts-reference-dot",y)},t.renderDot(d,w),$e.renderCallByParent(this.props,{x:p-o,y:h-o,width:2*o,height:2*o}))}}])}(S.Component);vo(yo,"displayName","ReferenceDot");vo(yo,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1});vo(yo,"renderDot",function(e,t){var r;return S.isValidElement(e)?r=S.cloneElement(e,t):X(e)?r=e(t):r=S.createElement(oo,lc({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"})),r});function pc(){return pc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},pc.apply(this,arguments)}function Ur(e){"@babel/helpers - typeof";return Ur=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ur(e)}function gh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function bh(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?gh(Object(r),!0).forEach(function(n){mo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function RF(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function LF(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,em(n.key),n)}}function BF(e,t,r){return t&&LF(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function FF(e,t,r){return t=ja(t),WF(e,Qy()?Reflect.construct(t,r||[],ja(e).constructor):t.apply(e,r))}function WF(e,t){if(t&&(Ur(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return zF(e)}function zF(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Qy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Qy=function(){return!!e})()}function ja(e){return ja=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ja(e)}function UF(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&hc(e,t)}function hc(e,t){return hc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},hc(e,t)}function mo(e,t,r){return t=em(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function em(e){var t=qF(e,"string");return Ur(t)=="symbol"?t:t+""}function qF(e,t){if(Ur(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ur(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var HF=function(t,r,n,i,a){var o=a.x1,u=a.x2,c=a.y1,s=a.y2,f=a.xAxis,l=a.yAxis;if(!f||!l)return null;var p=Os({x:f.scale,y:l.scale}),h={x:t?p.x.apply(o,{position:"start"}):p.x.rangeMin,y:n?p.y.apply(c,{position:"start"}):p.y.rangeMin},v={x:r?p.x.apply(u,{position:"end"}):p.x.rangeMax,y:i?p.y.apply(s,{position:"end"}):p.y.rangeMax};return ct(a,"discard")&&(!p.isInRange(h)||!p.isInRange(v))?null:Fy(h,v)},go=function(e){function t(){return RF(this,t),FF(this,t,arguments)}return UF(t,e),BF(t,[{key:"render",value:function(){var n=this.props,i=n.x1,a=n.x2,o=n.y1,u=n.y2,c=n.className,s=n.alwaysShow,f=n.clipPathId;it(s===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=Se(i),p=Se(a),h=Se(o),v=Se(u),d=this.props.shape;if(!l&&!p&&!h&&!v&&!d)return null;var y=HF(l,p,h,v,this.props);if(!y&&!d)return null;var b=ct(this.props,"hidden")?"url(#".concat(f,")"):void 0;return S.createElement(te,{className:J("recharts-reference-area",c)},t.renderRect(d,bh(bh({clipPath:b},H(this.props,!0)),y)),$e.renderCallByParent(this.props,y))}}])}(S.Component);mo(go,"displayName","ReferenceArea");mo(go,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1});mo(go,"renderRect",function(e,t){var r;return S.isValidElement(e)?r=S.cloneElement(e,t):X(e)?r=e(t):r=S.createElement(ws,pc({},t,{className:"recharts-reference-area-rect"})),r});function tm(e,t,r){if(t<1)return[];if(t===1&&r===void 0)return e;for(var n=[],i=0;i<e.length;i+=t)n.push(e[i]);return n}function KF(e,t,r){var n={width:e.width+t.width,height:e.height+t.height};return FB(n,r)}function GF(e,t,r){var n=r==="width",i=e.x,a=e.y,o=e.width,u=e.height;return t===1?{start:n?i:a,end:n?i+o:a+u}:{start:n?i+o:a+u,end:n?i:a}}function Ma(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function VF(e,t){return tm(e,t+1)}function XF(e,t,r,n,i){for(var a=(n||[]).slice(),o=t.start,u=t.end,c=0,s=1,f=o,l=function(){var v=n==null?void 0:n[c];if(v===void 0)return{v:tm(n,s)};var d=c,y,b=function(){return y===void 0&&(y=r(v,d)),y},w=v.coordinate,x=c===0||Ma(e,w,b,f,u);x||(c=0,f=o,s+=1),x&&(f=w+e*(b()/2+i),c+=s)},p;s<=a.length;)if(p=l(),p)return p.v;return[]}function ui(e){"@babel/helpers - typeof";return ui=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ui(e)}function xh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function je(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?xh(Object(r),!0).forEach(function(n){YF(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function YF(e,t,r){return t=ZF(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ZF(e){var t=JF(e,"string");return ui(t)=="symbol"?t:t+""}function JF(e,t){if(ui(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ui(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function QF(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,u=t.start,c=t.end,s=function(p){var h=a[p],v,d=function(){return v===void 0&&(v=r(h,p)),v};if(p===o-1){var y=e*(h.coordinate+e*d()/2-c);a[p]=h=je(je({},h),{},{tickCoord:y>0?h.coordinate-y*e:h.coordinate})}else a[p]=h=je(je({},h),{},{tickCoord:h.coordinate});var b=Ma(e,h.tickCoord,d,u,c);b&&(c=h.tickCoord-e*(d()/2+i),a[p]=je(je({},h),{},{isShow:!0}))},f=o-1;f>=0;f--)s(f);return a}function e3(e,t,r,n,i,a){var o=(n||[]).slice(),u=o.length,c=t.start,s=t.end;if(a){var f=n[u-1],l=r(f,u-1),p=e*(f.coordinate+e*l/2-s);o[u-1]=f=je(je({},f),{},{tickCoord:p>0?f.coordinate-p*e:f.coordinate});var h=Ma(e,f.tickCoord,function(){return l},c,s);h&&(s=f.tickCoord-e*(l/2+i),o[u-1]=je(je({},f),{},{isShow:!0}))}for(var v=a?u-1:u,d=function(w){var x=o[w],A,m=function(){return A===void 0&&(A=r(x,w)),A};if(w===0){var g=e*(x.coordinate-e*m()/2-c);o[w]=x=je(je({},x),{},{tickCoord:g<0?x.coordinate-g*e:x.coordinate})}else o[w]=x=je(je({},x),{},{tickCoord:x.coordinate});var O=Ma(e,x.tickCoord,m,c,s);O&&(c=x.tickCoord+e*(m()/2+i),o[w]=je(je({},x),{},{isShow:!0}))},y=0;y<v;y++)d(y);return o}function Es(e,t,r){var n=e.tick,i=e.ticks,a=e.viewBox,o=e.minTickGap,u=e.orientation,c=e.interval,s=e.tickFormatter,f=e.unit,l=e.angle;if(!i||!i.length||!n)return[];if(L(c)||ur.isSsr)return VF(i,typeof c=="number"&&L(c)?c:0);var p=[],h=u==="top"||u==="bottom"?"width":"height",v=f&&h==="width"?On(f,{fontSize:t,letterSpacing:r}):{width:0,height:0},d=function(x,A){var m=X(s)?s(x.value,A):x.value;return h==="width"?KF(On(m,{fontSize:t,letterSpacing:r}),v,l):On(m,{fontSize:t,letterSpacing:r})[h]},y=i.length>=2?Ce(i[1].coordinate-i[0].coordinate):1,b=GF(a,y,h);return c==="equidistantPreserveStart"?XF(y,b,d,i,o):(c==="preserveStart"||c==="preserveStartEnd"?p=e3(y,b,d,i,o,c==="preserveStartEnd"):p=QF(y,b,d,i,o),p.filter(function(w){return w.isShow}))}var t3=["viewBox"],r3=["viewBox"],n3=["ticks"];function qr(e){"@babel/helpers - typeof";return qr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},qr(e)}function br(){return br=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},br.apply(this,arguments)}function wh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function we(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?wh(Object(r),!0).forEach(function(n){js(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):wh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Vo(e,t){if(e==null)return{};var r=i3(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function i3(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function a3(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Oh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,nm(n.key),n)}}function o3(e,t,r){return t&&Oh(e.prototype,t),r&&Oh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function u3(e,t,r){return t=Ca(t),c3(e,rm()?Reflect.construct(t,r||[],Ca(e).constructor):t.apply(e,r))}function c3(e,t){if(t&&(qr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return s3(e)}function s3(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function rm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(rm=function(){return!!e})()}function Ca(e){return Ca=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ca(e)}function l3(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&dc(e,t)}function dc(e,t){return dc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},dc(e,t)}function js(e,t,r){return t=nm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function nm(e){var t=f3(e,"string");return qr(t)=="symbol"?t:t+""}function f3(e,t){if(qr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(qr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var ln=function(e){function t(r){var n;return a3(this,t),n=u3(this,t,[r]),n.state={fontSize:"",letterSpacing:""},n}return l3(t,e),o3(t,[{key:"shouldComponentUpdate",value:function(n,i){var a=n.viewBox,o=Vo(n,t3),u=this.props,c=u.viewBox,s=Vo(u,r3);return!wr(a,c)||!wr(o,s)||!wr(i,this.state)}},{key:"componentDidMount",value:function(){var n=this.layerReference;if(n){var i=n.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];i&&this.setState({fontSize:window.getComputedStyle(i).fontSize,letterSpacing:window.getComputedStyle(i).letterSpacing})}}},{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.x,o=i.y,u=i.width,c=i.height,s=i.orientation,f=i.tickSize,l=i.mirror,p=i.tickMargin,h,v,d,y,b,w,x=l?-1:1,A=n.tickSize||f,m=L(n.tickCoord)?n.tickCoord:n.coordinate;switch(s){case"top":h=v=n.coordinate,y=o+ +!l*c,d=y-x*A,w=d-x*p,b=m;break;case"left":d=y=n.coordinate,v=a+ +!l*u,h=v-x*A,b=h-x*p,w=m;break;case"right":d=y=n.coordinate,v=a+ +l*u,h=v+x*A,b=h+x*p,w=m;break;default:h=v=n.coordinate,y=o+ +l*c,d=y+x*A,w=d+x*p,b=m;break}return{line:{x1:h,y1:d,x2:v,y2:y},tick:{x:b,y:w}}}},{key:"getTickTextAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o;switch(i){case"left":o=a?"start":"end";break;case"right":o=a?"end":"start";break;default:o="middle";break}return o}},{key:"getTickVerticalAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o="end";switch(i){case"left":case"right":o="middle";break;case"top":o=a?"start":"end";break;default:o=a?"end":"start";break}return o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.orientation,s=n.mirror,f=n.axisLine,l=we(we(we({},H(this.props,!1)),H(f,!1)),{},{fill:"none"});if(c==="top"||c==="bottom"){var p=+(c==="top"&&!s||c==="bottom"&&s);l=we(we({},l),{},{x1:i,y1:a+p*u,x2:i+o,y2:a+p*u})}else{var h=+(c==="left"&&!s||c==="right"&&s);l=we(we({},l),{},{x1:i+h*o,y1:a,x2:i+h*o,y2:a+u})}return S.createElement("line",br({},l,{className:J("recharts-cartesian-axis-line",He(f,"className"))}))}},{key:"renderTicks",value:function(n,i,a){var o=this,u=this.props,c=u.tickLine,s=u.stroke,f=u.tick,l=u.tickFormatter,p=u.unit,h=Es(we(we({},this.props),{},{ticks:n}),i,a),v=this.getTickTextAnchor(),d=this.getTickVerticalAnchor(),y=H(this.props,!1),b=H(f,!1),w=we(we({},y),{},{fill:"none"},H(c,!1)),x=h.map(function(A,m){var g=o.getTickLineCoord(A),O=g.line,P=g.tick,_=we(we(we(we({textAnchor:v,verticalAnchor:d},y),{},{stroke:"none",fill:s},b),P),{},{index:m,payload:A,visibleTicksCount:h.length,tickFormatter:l});return S.createElement(te,br({className:"recharts-cartesian-axis-tick",key:"tick-".concat(A.value,"-").concat(A.coordinate,"-").concat(A.tickCoord)},tr(o.props,A,m)),c&&S.createElement("line",br({},w,O,{className:J("recharts-cartesian-axis-tick-line",He(c,"className"))})),f&&t.renderTickItem(f,_,"".concat(X(l)?l(A.value,m):A.value).concat(p||"")))});return S.createElement("g",{className:"recharts-cartesian-axis-ticks"},x)}},{key:"render",value:function(){var n=this,i=this.props,a=i.axisLine,o=i.width,u=i.height,c=i.ticksGenerator,s=i.className,f=i.hide;if(f)return null;var l=this.props,p=l.ticks,h=Vo(l,n3),v=p;return X(c)&&(v=p&&p.length>0?c(this.props):c(h)),o<=0||u<=0||!v||!v.length?null:S.createElement(te,{className:J("recharts-cartesian-axis",s),ref:function(y){n.layerReference=y}},a&&this.renderAxisLine(),this.renderTicks(v,this.state.fontSize,this.state.letterSpacing),$e.renderCallByParent(this.props))}}],[{key:"renderTickItem",value:function(n,i,a){var o,u=J(i.className,"recharts-cartesian-axis-tick-value");return S.isValidElement(n)?o=S.cloneElement(n,we(we({},i),{},{className:u})):X(n)?o=n(we(we({},i),{},{className:u})):o=S.createElement(rr,br({},i,{className:"recharts-cartesian-axis-tick-value"}),a),o}}])}(R.Component);js(ln,"displayName","CartesianAxis");js(ln,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var p3=["x1","y1","x2","y2","key"],h3=["offset"];function ar(e){"@babel/helpers - typeof";return ar=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ar(e)}function Ah(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Me(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ah(Object(r),!0).forEach(function(n){d3(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ah(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function d3(e,t,r){return t=v3(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function v3(e){var t=y3(e,"string");return ar(t)=="symbol"?t:t+""}function y3(e,t){if(ar(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ar(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Zt(){return Zt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Zt.apply(this,arguments)}function Sh(e,t){if(e==null)return{};var r=m3(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function m3(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var g3=function(t){var r=t.fill;if(!r||r==="none")return null;var n=t.fillOpacity,i=t.x,a=t.y,o=t.width,u=t.height,c=t.ry;return S.createElement("rect",{x:i,y:a,ry:c,width:o,height:u,stroke:"none",fill:r,fillOpacity:n,className:"recharts-cartesian-grid-bg"})};function im(e,t){var r;if(S.isValidElement(e))r=S.cloneElement(e,t);else if(X(e))r=e(t);else{var n=t.x1,i=t.y1,a=t.x2,o=t.y2,u=t.key,c=Sh(t,p3),s=H(c,!1);s.offset;var f=Sh(s,h3);r=S.createElement("line",Zt({},f,{x1:n,y1:i,x2:a,y2:o,fill:"none",key:u}))}return r}function b3(e){var t=e.x,r=e.width,n=e.horizontal,i=n===void 0?!0:n,a=e.horizontalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,c){var s=Me(Me({},e),{},{x1:t,y1:u,x2:t+r,y2:u,key:"line-".concat(c),index:c});return im(i,s)});return S.createElement("g",{className:"recharts-cartesian-grid-horizontal"},o)}function x3(e){var t=e.y,r=e.height,n=e.vertical,i=n===void 0?!0:n,a=e.verticalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,c){var s=Me(Me({},e),{},{x1:u,y1:t,x2:u,y2:t+r,key:"line-".concat(c),index:c});return im(i,s)});return S.createElement("g",{className:"recharts-cartesian-grid-vertical"},o)}function w3(e){var t=e.horizontalFill,r=e.fillOpacity,n=e.x,i=e.y,a=e.width,o=e.height,u=e.horizontalPoints,c=e.horizontal,s=c===void 0?!0:c;if(!s||!t||!t.length)return null;var f=u.map(function(p){return Math.round(p+i-i)}).sort(function(p,h){return p-h});i!==f[0]&&f.unshift(0);var l=f.map(function(p,h){var v=!f[h+1],d=v?i+o-p:f[h+1]-p;if(d<=0)return null;var y=h%t.length;return S.createElement("rect",{key:"react-".concat(h),y:p,x:n,height:d,width:a,stroke:"none",fill:t[y],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return S.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},l)}function O3(e){var t=e.vertical,r=t===void 0?!0:t,n=e.verticalFill,i=e.fillOpacity,a=e.x,o=e.y,u=e.width,c=e.height,s=e.verticalPoints;if(!r||!n||!n.length)return null;var f=s.map(function(p){return Math.round(p+a-a)}).sort(function(p,h){return p-h});a!==f[0]&&f.unshift(0);var l=f.map(function(p,h){var v=!f[h+1],d=v?a+u-p:f[h+1]-p;if(d<=0)return null;var y=h%n.length;return S.createElement("rect",{key:"react-".concat(h),x:p,y:o,width:d,height:c,stroke:"none",fill:n[y],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return S.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},l)}var A3=function(t,r){var n=t.xAxis,i=t.width,a=t.height,o=t.offset;return ey(Es(Me(Me(Me({},ln.defaultProps),n),{},{ticks:gt(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.left,o.left+o.width,r)},S3=function(t,r){var n=t.yAxis,i=t.width,a=t.height,o=t.offset;return ey(Es(Me(Me(Me({},ln.defaultProps),n),{},{ticks:gt(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.top,o.top+o.height,r)},dr={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function P3(e){var t,r,n,i,a,o,u=Ps(),c=_s(),s=fF(),f=Me(Me({},e),{},{stroke:(t=e.stroke)!==null&&t!==void 0?t:dr.stroke,fill:(r=e.fill)!==null&&r!==void 0?r:dr.fill,horizontal:(n=e.horizontal)!==null&&n!==void 0?n:dr.horizontal,horizontalFill:(i=e.horizontalFill)!==null&&i!==void 0?i:dr.horizontalFill,vertical:(a=e.vertical)!==null&&a!==void 0?a:dr.vertical,verticalFill:(o=e.verticalFill)!==null&&o!==void 0?o:dr.verticalFill,x:L(e.x)?e.x:s.left,y:L(e.y)?e.y:s.top,width:L(e.width)?e.width:s.width,height:L(e.height)?e.height:s.height}),l=f.x,p=f.y,h=f.width,v=f.height,d=f.syncWithTicks,y=f.horizontalValues,b=f.verticalValues,w=cF(),x=sF();if(!L(h)||h<=0||!L(v)||v<=0||!L(l)||l!==+l||!L(p)||p!==+p)return null;var A=f.verticalCoordinatesGenerator||A3,m=f.horizontalCoordinatesGenerator||S3,g=f.horizontalPoints,O=f.verticalPoints;if((!g||!g.length)&&X(m)){var P=y&&y.length,_=m({yAxis:x?Me(Me({},x),{},{ticks:P?y:x.ticks}):void 0,width:u,height:c,offset:s},P?!0:d);it(Array.isArray(_),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(ar(_),"]")),Array.isArray(_)&&(g=_)}if((!O||!O.length)&&X(A)){var E=b&&b.length,$=A({xAxis:w?Me(Me({},w),{},{ticks:E?b:w.ticks}):void 0,width:u,height:c,offset:s},E?!0:d);it(Array.isArray($),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(ar($),"]")),Array.isArray($)&&(O=$)}return S.createElement("g",{className:"recharts-cartesian-grid"},S.createElement(g3,{fill:f.fill,fillOpacity:f.fillOpacity,x:f.x,y:f.y,width:f.width,height:f.height,ry:f.ry}),S.createElement(b3,Zt({},f,{offset:s,horizontalPoints:g,xAxis:w,yAxis:x})),S.createElement(x3,Zt({},f,{offset:s,verticalPoints:O,xAxis:w,yAxis:x})),S.createElement(w3,Zt({},f,{horizontalPoints:g})),S.createElement(O3,Zt({},f,{verticalPoints:O})))}P3.displayName="CartesianGrid";var _3=["type","layout","connectNulls","ref"],$3=["key"];function Hr(e){"@babel/helpers - typeof";return Hr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Hr(e)}function Ph(e,t){if(e==null)return{};var r=T3(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function T3(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Tn(){return Tn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Tn.apply(this,arguments)}function _h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Fe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?_h(Object(r),!0).forEach(function(n){rt(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_h(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function vr(e){return C3(e)||M3(e)||j3(e)||E3()}function E3(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function j3(e,t){if(e){if(typeof e=="string")return vc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return vc(e,t)}}function M3(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function C3(e){if(Array.isArray(e))return vc(e)}function vc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function I3(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function $h(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,om(n.key),n)}}function k3(e,t,r){return t&&$h(e.prototype,t),r&&$h(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function D3(e,t,r){return t=Ia(t),N3(e,am()?Reflect.construct(t,r||[],Ia(e).constructor):t.apply(e,r))}function N3(e,t){if(t&&(Hr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return R3(e)}function R3(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function am(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(am=function(){return!!e})()}function Ia(e){return Ia=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ia(e)}function L3(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&yc(e,t)}function yc(e,t){return yc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},yc(e,t)}function rt(e,t,r){return t=om(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function om(e){var t=B3(e,"string");return Hr(t)=="symbol"?t:t+""}function B3(e,t){if(Hr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Hr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var bo=function(e){function t(){var r;I3(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=D3(this,t,[].concat(i)),rt(r,"state",{isAnimationFinished:!0,totalLength:0}),rt(r,"generateSimpleStrokeDasharray",function(o,u){return"".concat(u,"px ").concat(o-u,"px")}),rt(r,"getStrokeDasharray",function(o,u,c){var s=c.reduce(function(b,w){return b+w});if(!s)return r.generateSimpleStrokeDasharray(u,o);for(var f=Math.floor(o/s),l=o%s,p=u-o,h=[],v=0,d=0;v<c.length;d+=c[v],++v)if(d+c[v]>l){h=[].concat(vr(c.slice(0,v)),[l-d]);break}var y=h.length%2===0?[0,p]:[p];return[].concat(vr(t.repeat(c,f)),vr(h),y).map(function(b){return"".concat(b,"px")}).join(", ")}),rt(r,"id",tn("recharts-line-")),rt(r,"pathRef",function(o){r.mainCurve=o}),rt(r,"handleAnimationEnd",function(){r.setState({isAnimationFinished:!0}),r.props.onAnimationEnd&&r.props.onAnimationEnd()}),rt(r,"handleAnimationStart",function(){r.setState({isAnimationFinished:!1}),r.props.onAnimationStart&&r.props.onAnimationStart()}),r}return L3(t,e),k3(t,[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var n=this.getTotalLength();this.setState({totalLength:n})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var n=this.getTotalLength();n!==this.state.totalLength&&this.setState({totalLength:n})}}},{key:"getTotalLength",value:function(){var n=this.mainCurve;try{return n&&n.getTotalLength&&n.getTotalLength()||0}catch{return 0}}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.points,u=a.xAxis,c=a.yAxis,s=a.layout,f=a.children,l=Ke(f,gi);if(!l)return null;var p=function(d,y){return{x:d.x,y:d.y,value:d.value,errorVal:Ae(d.payload,y)}},h={clipPath:n?"url(#clipPath-".concat(i,")"):null};return S.createElement(te,h,l.map(function(v){return S.cloneElement(v,{key:"bar-".concat(v.props.dataKey),data:o,xAxis:u,yAxis:c,layout:s,dataPointFormatter:p})}))}},{key:"renderDots",value:function(n,i,a){var o=this.props.isAnimationActive;if(o&&!this.state.isAnimationFinished)return null;var u=this.props,c=u.dot,s=u.points,f=u.dataKey,l=H(this.props,!1),p=H(c,!0),h=s.map(function(d,y){var b=Fe(Fe(Fe({key:"dot-".concat(y),r:3},l),p),{},{index:y,cx:d.x,cy:d.y,value:d.value,dataKey:f,payload:d.payload,points:s});return t.renderDotItem(c,b)}),v={clipPath:n?"url(#clipPath-".concat(i?"":"dots-").concat(a,")"):null};return S.createElement(te,Tn({className:"recharts-line-dots",key:"dots"},v),h)}},{key:"renderCurveStatically",value:function(n,i,a,o){var u=this.props,c=u.type,s=u.layout,f=u.connectNulls;u.ref;var l=Ph(u,_3),p=Fe(Fe(Fe({},H(l,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:i?"url(#clipPath-".concat(a,")"):null,points:n},o),{},{type:c,layout:s,connectNulls:f});return S.createElement(ha,Tn({},p,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(n,i){var a=this,o=this.props,u=o.points,c=o.strokeDasharray,s=o.isAnimationActive,f=o.animationBegin,l=o.animationDuration,p=o.animationEasing,h=o.animationId,v=o.animateNewValues,d=o.width,y=o.height,b=this.state,w=b.prevPoints,x=b.totalLength;return S.createElement(lt,{begin:f,duration:l,isActive:s,easing:p,from:{t:0},to:{t:1},key:"line-".concat(h),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(A){var m=A.t;if(w){var g=w.length/u.length,O=u.map(function(T,C){var I=Math.floor(C*g);if(w[I]){var M=w[I],k=Ue(M.x,T.x),D=Ue(M.y,T.y);return Fe(Fe({},T),{},{x:k(m),y:D(m)})}if(v){var B=Ue(d*2,T.x),F=Ue(y/2,T.y);return Fe(Fe({},T),{},{x:B(m),y:F(m)})}return Fe(Fe({},T),{},{x:T.x,y:T.y})});return a.renderCurveStatically(O,n,i)}var P=Ue(0,x),_=P(m),E;if(c){var $="".concat(c).split(/[,\s]+/gim).map(function(T){return parseFloat(T)});E=a.getStrokeDasharray(_,x,$)}else E=a.generateSimpleStrokeDasharray(x,_);return a.renderCurveStatically(u,n,i,{strokeDasharray:E})})}},{key:"renderCurve",value:function(n,i){var a=this.props,o=a.points,u=a.isAnimationActive,c=this.state,s=c.prevPoints,f=c.totalLength;return u&&o&&o.length&&(!s&&f>0||!mi(s,o))?this.renderCurveWithAnimation(n,i):this.renderCurveStatically(o,n,i)}},{key:"render",value:function(){var n,i=this.props,a=i.hide,o=i.dot,u=i.points,c=i.className,s=i.xAxis,f=i.yAxis,l=i.top,p=i.left,h=i.width,v=i.height,d=i.isAnimationActive,y=i.id;if(a||!u||!u.length)return null;var b=this.state.isAnimationFinished,w=u.length===1,x=J("recharts-line",c),A=s&&s.allowDataOverflow,m=f&&f.allowDataOverflow,g=A||m,O=Y(y)?this.id:y,P=(n=H(o,!1))!==null&&n!==void 0?n:{r:3,strokeWidth:2},_=P.r,E=_===void 0?3:_,$=P.strokeWidth,T=$===void 0?2:$,C=ux(o)?o:{},I=C.clipDot,M=I===void 0?!0:I,k=E*2+T;return S.createElement(te,{className:x},A||m?S.createElement("defs",null,S.createElement("clipPath",{id:"clipPath-".concat(O)},S.createElement("rect",{x:A?p:p-h/2,y:m?l:l-v/2,width:A?h:h*2,height:m?v:v*2})),!M&&S.createElement("clipPath",{id:"clipPath-dots-".concat(O)},S.createElement("rect",{x:p-k/2,y:l-k/2,width:h+k,height:v+k}))):null,!w&&this.renderCurve(g,O),this.renderErrorBar(g,O),(w||o)&&this.renderDots(g,M,O),(!d||b)&&wt.renderCallByParent(this.props,u))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curPoints:n.points,prevPoints:i.curPoints}:n.points!==i.curPoints?{curPoints:n.points}:null}},{key:"repeat",value:function(n,i){for(var a=n.length%2!==0?[].concat(vr(n),[0]):n,o=[],u=0;u<i;++u)o=[].concat(vr(o),vr(a));return o}},{key:"renderDotItem",value:function(n,i){var a;if(S.isValidElement(n))a=S.cloneElement(n,i);else if(X(n))a=n(i);else{var o=i.key,u=Ph(i,$3),c=J("recharts-line-dot",typeof n!="boolean"?n.className:"");a=S.createElement(oo,Tn({key:o},u,{className:c}))}return a}}])}(R.PureComponent);rt(bo,"displayName","Line");rt(bo,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!ur.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1});rt(bo,"getComposedData",function(e){var t=e.props,r=e.xAxis,n=e.yAxis,i=e.xAxisTicks,a=e.yAxisTicks,o=e.dataKey,u=e.bandSize,c=e.displayedData,s=e.offset,f=t.layout,l=c.map(function(p,h){var v=Ae(p,o);return f==="horizontal"?{x:Zf({axis:r,ticks:i,bandSize:u,entry:p,index:h}),y:Y(v)?null:n.scale(v),value:v,payload:p}:{x:Y(v)?null:r.scale(v),y:Zf({axis:n,ticks:a,bandSize:u,entry:p,index:h}),value:v,payload:p}});return Fe({points:l,layout:f},s)});function Kr(e){"@babel/helpers - typeof";return Kr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Kr(e)}function F3(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function W3(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,sm(n.key),n)}}function z3(e,t,r){return t&&W3(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function U3(e,t,r){return t=ka(t),q3(e,um()?Reflect.construct(t,r||[],ka(e).constructor):t.apply(e,r))}function q3(e,t){if(t&&(Kr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return H3(e)}function H3(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function um(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(um=function(){return!!e})()}function ka(e){return ka=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ka(e)}function K3(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&mc(e,t)}function mc(e,t){return mc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},mc(e,t)}function cm(e,t,r){return t=sm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function sm(e){var t=G3(e,"string");return Kr(t)=="symbol"?t:t+""}function G3(e,t){if(Kr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Kr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function gc(){return gc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},gc.apply(this,arguments)}function V3(e){var t=e.xAxisId,r=Ps(),n=_s(),i=Gy(t);return i==null?null:R.createElement(ln,gc({},i,{className:J("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(o){return gt(o,!0)}}))}var xo=function(e){function t(){return F3(this,t),U3(this,t,arguments)}return K3(t,e),z3(t,[{key:"render",value:function(){return R.createElement(V3,this.props)}}])}(R.Component);cm(xo,"displayName","XAxis");cm(xo,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function Gr(e){"@babel/helpers - typeof";return Gr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Gr(e)}function X3(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Y3(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,pm(n.key),n)}}function Z3(e,t,r){return t&&Y3(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function J3(e,t,r){return t=Da(t),Q3(e,lm()?Reflect.construct(t,r||[],Da(e).constructor):t.apply(e,r))}function Q3(e,t){if(t&&(Gr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return eW(e)}function eW(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function lm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(lm=function(){return!!e})()}function Da(e){return Da=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Da(e)}function tW(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&bc(e,t)}function bc(e,t){return bc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},bc(e,t)}function fm(e,t,r){return t=pm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pm(e){var t=rW(e,"string");return Gr(t)=="symbol"?t:t+""}function rW(e,t){if(Gr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Gr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function xc(){return xc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},xc.apply(this,arguments)}var nW=function(t){var r=t.yAxisId,n=Ps(),i=_s(),a=Vy(r);return a==null?null:R.createElement(ln,xc({},a,{className:J("recharts-".concat(a.axisType," ").concat(a.axisType),a.className),viewBox:{x:0,y:0,width:n,height:i},ticksGenerator:function(u){return gt(u,!0)}}))},wo=function(e){function t(){return X3(this,t),J3(this,t,arguments)}return tW(t,e),Z3(t,[{key:"render",value:function(){return R.createElement(nW,this.props)}}])}(R.Component);fm(wo,"displayName","YAxis");fm(wo,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});function Th(e){return uW(e)||oW(e)||aW(e)||iW()}function iW(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function aW(e,t){if(e){if(typeof e=="string")return wc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return wc(e,t)}}function oW(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function uW(e){if(Array.isArray(e))return wc(e)}function wc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Oc=function(t,r,n,i,a){var o=Ke(t,Ts),u=Ke(t,yo),c=[].concat(Th(o),Th(u)),s=Ke(t,go),f="".concat(i,"Id"),l=i[0],p=r;if(c.length&&(p=c.reduce(function(d,y){if(y.props[f]===n&&ct(y.props,"extendDomain")&&L(y.props[l])){var b=y.props[l];return[Math.min(d[0],b),Math.max(d[1],b)]}return d},p)),s.length){var h="".concat(l,"1"),v="".concat(l,"2");p=s.reduce(function(d,y){if(y.props[f]===n&&ct(y.props,"extendDomain")&&L(y.props[h])&&L(y.props[v])){var b=y.props[h],w=y.props[v];return[Math.min(d[0],b,w),Math.max(d[1],b,w)]}return d},p)}return a&&a.length&&(p=a.reduce(function(d,y){return L(y)?[Math.min(d[0],y),Math.max(d[1],y)]:d},p)),p},hm={exports:{}};(function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1));function i(c,s,f){this.fn=c,this.context=s,this.once=f||!1}function a(c,s,f,l,p){if(typeof f!="function")throw new TypeError("The listener must be a function");var h=new i(f,l||c,p),v=r?r+s:s;return c._events[v]?c._events[v].fn?c._events[v]=[c._events[v],h]:c._events[v].push(h):(c._events[v]=h,c._eventsCount++),c}function o(c,s){--c._eventsCount===0?c._events=new n:delete c._events[s]}function u(){this._events=new n,this._eventsCount=0}u.prototype.eventNames=function(){var s=[],f,l;if(this._eventsCount===0)return s;for(l in f=this._events)t.call(f,l)&&s.push(r?l.slice(1):l);return Object.getOwnPropertySymbols?s.concat(Object.getOwnPropertySymbols(f)):s},u.prototype.listeners=function(s){var f=r?r+s:s,l=this._events[f];if(!l)return[];if(l.fn)return[l.fn];for(var p=0,h=l.length,v=new Array(h);p<h;p++)v[p]=l[p].fn;return v},u.prototype.listenerCount=function(s){var f=r?r+s:s,l=this._events[f];return l?l.fn?1:l.length:0},u.prototype.emit=function(s,f,l,p,h,v){var d=r?r+s:s;if(!this._events[d])return!1;var y=this._events[d],b=arguments.length,w,x;if(y.fn){switch(y.once&&this.removeListener(s,y.fn,void 0,!0),b){case 1:return y.fn.call(y.context),!0;case 2:return y.fn.call(y.context,f),!0;case 3:return y.fn.call(y.context,f,l),!0;case 4:return y.fn.call(y.context,f,l,p),!0;case 5:return y.fn.call(y.context,f,l,p,h),!0;case 6:return y.fn.call(y.context,f,l,p,h,v),!0}for(x=1,w=new Array(b-1);x<b;x++)w[x-1]=arguments[x];y.fn.apply(y.context,w)}else{var A=y.length,m;for(x=0;x<A;x++)switch(y[x].once&&this.removeListener(s,y[x].fn,void 0,!0),b){case 1:y[x].fn.call(y[x].context);break;case 2:y[x].fn.call(y[x].context,f);break;case 3:y[x].fn.call(y[x].context,f,l);break;case 4:y[x].fn.call(y[x].context,f,l,p);break;default:if(!w)for(m=1,w=new Array(b-1);m<b;m++)w[m-1]=arguments[m];y[x].fn.apply(y[x].context,w)}}return!0},u.prototype.on=function(s,f,l){return a(this,s,f,l,!1)},u.prototype.once=function(s,f,l){return a(this,s,f,l,!0)},u.prototype.removeListener=function(s,f,l,p){var h=r?r+s:s;if(!this._events[h])return this;if(!f)return o(this,h),this;var v=this._events[h];if(v.fn)v.fn===f&&(!p||v.once)&&(!l||v.context===l)&&o(this,h);else{for(var d=0,y=[],b=v.length;d<b;d++)(v[d].fn!==f||p&&!v[d].once||l&&v[d].context!==l)&&y.push(v[d]);y.length?this._events[h]=y.length===1?y[0]:y:o(this,h)}return this},u.prototype.removeAllListeners=function(s){var f;return s?(f=r?r+s:s,this._events[f]&&o(this,f)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,e.exports=u})(hm);var cW=hm.exports;const sW=ae(cW);var Xo=new sW,Yo="recharts.syncMouseEvents";function ci(e){"@babel/helpers - typeof";return ci=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ci(e)}function lW(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function fW(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,dm(n.key),n)}}function pW(e,t,r){return t&&fW(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Zo(e,t,r){return t=dm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dm(e){var t=hW(e,"string");return ci(t)=="symbol"?t:t+""}function hW(e,t){if(ci(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ci(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var dW=function(){function e(){lW(this,e),Zo(this,"activeIndex",0),Zo(this,"coordinateList",[]),Zo(this,"layout","horizontal")}return pW(e,[{key:"setDetails",value:function(r){var n,i=r.coordinateList,a=i===void 0?null:i,o=r.container,u=o===void 0?null:o,c=r.layout,s=c===void 0?null:c,f=r.offset,l=f===void 0?null:f,p=r.mouseHandlerCallback,h=p===void 0?null:p;this.coordinateList=(n=a??this.coordinateList)!==null&&n!==void 0?n:[],this.container=u??this.container,this.layout=s??this.layout,this.offset=l??this.offset,this.mouseHandlerCallback=h??this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(r){if(this.coordinateList.length!==0)switch(r.key){case"ArrowRight":{if(this.layout!=="horizontal")return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break}case"ArrowLeft":{if(this.layout!=="horizontal")return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse();break}}}},{key:"setIndex",value:function(r){this.activeIndex=r}},{key:"spoofMouse",value:function(){var r,n;if(this.layout==="horizontal"&&this.coordinateList.length!==0){var i=this.container.getBoundingClientRect(),a=i.x,o=i.y,u=i.height,c=this.coordinateList[this.activeIndex].coordinate,s=((r=window)===null||r===void 0?void 0:r.scrollX)||0,f=((n=window)===null||n===void 0?void 0:n.scrollY)||0,l=a+c+s,p=o+this.offset.top+u/2+f;this.mouseHandlerCallback({pageX:l,pageY:p})}}}])}();function vW(e,t,r){if(r==="number"&&t===!0&&Array.isArray(e)){var n=e==null?void 0:e[0],i=e==null?void 0:e[1];if(n&&i&&L(n)&&L(i))return!0}return!1}function yW(e,t,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:e==="horizontal"?t.x-i:r.left+.5,y:e==="horizontal"?r.top+.5:t.y-i,width:e==="horizontal"?n:r.width-1,height:e==="horizontal"?r.height-1:n}}function vm(e){var t=e.cx,r=e.cy,n=e.radius,i=e.startAngle,a=e.endAngle,o=se(t,r,n,i),u=se(t,r,n,a);return{points:[o,u],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function mW(e,t,r){var n,i,a,o;if(e==="horizontal")n=t.x,a=n,i=r.top,o=r.top+r.height;else if(e==="vertical")i=t.y,o=i,n=r.left,a=r.left+r.width;else if(t.cx!=null&&t.cy!=null)if(e==="centric"){var u=t.cx,c=t.cy,s=t.innerRadius,f=t.outerRadius,l=t.angle,p=se(u,c,s,l),h=se(u,c,f,l);n=p.x,i=p.y,a=h.x,o=h.y}else return vm(t);return[{x:n,y:i},{x:a,y:o}]}function si(e){"@babel/helpers - typeof";return si=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},si(e)}function Eh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ii(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Eh(Object(r),!0).forEach(function(n){gW(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Eh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function gW(e,t,r){return t=bW(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bW(e){var t=xW(e,"string");return si(t)=="symbol"?t:t+""}function xW(e,t){if(si(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(si(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function wW(e){var t,r,n=e.element,i=e.tooltipEventType,a=e.isActive,o=e.activeCoordinate,u=e.activePayload,c=e.offset,s=e.activeTooltipIndex,f=e.tooltipAxisBandSize,l=e.layout,p=e.chartName,h=(t=n.props.cursor)!==null&&t!==void 0?t:(r=n.type.defaultProps)===null||r===void 0?void 0:r.cursor;if(!n||!h||!a||!o||p!=="ScatterChart"&&i!=="axis")return null;var v,d=ha;if(p==="ScatterChart")v=o,d=LN;else if(p==="BarChart")v=yW(l,o,c,f),d=ws;else if(l==="radial"){var y=vm(o),b=y.cx,w=y.cy,x=y.radius,A=y.startAngle,m=y.endAngle;v={cx:b,cy:w,startAngle:A,endAngle:m,innerRadius:x,outerRadius:x},d=fy}else v={points:mW(l,o,c)},d=ha;var g=Ii(Ii(Ii(Ii({stroke:"#ccc",pointerEvents:"none"},c),v),H(h,!1)),{},{payload:u,payloadIndex:s,className:J("recharts-tooltip-cursor",h.className)});return R.isValidElement(h)?R.cloneElement(h,g):R.createElement(d,g)}var OW=["item"],AW=["children","className","width","height","style","compact","title","desc"];function Vr(e){"@babel/helpers - typeof";return Vr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Vr(e)}function xr(){return xr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},xr.apply(this,arguments)}function jh(e,t){return _W(e)||PW(e,t)||mm(e,t)||SW()}function SW(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function PW(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function _W(e){if(Array.isArray(e))return e}function Mh(e,t){if(e==null)return{};var r=$W(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function $W(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function TW(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function EW(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,gm(n.key),n)}}function jW(e,t,r){return t&&EW(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function MW(e,t,r){return t=Na(t),CW(e,ym()?Reflect.construct(t,r||[],Na(e).constructor):t.apply(e,r))}function CW(e,t){if(t&&(Vr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return IW(e)}function IW(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ym(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(ym=function(){return!!e})()}function Na(e){return Na=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Na(e)}function kW(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ac(e,t)}function Ac(e,t){return Ac=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ac(e,t)}function Xr(e){return RW(e)||NW(e)||mm(e)||DW()}function DW(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function mm(e,t){if(e){if(typeof e=="string")return Sc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Sc(e,t)}}function NW(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function RW(e){if(Array.isArray(e))return Sc(e)}function Sc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Ch(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function j(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ch(Object(r),!0).forEach(function(n){K(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ch(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function K(e,t,r){return t=gm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function gm(e){var t=LW(e,"string");return Vr(t)=="symbol"?t:t+""}function LW(e,t){if(Vr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Vr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var BW={xAxis:["bottom","top"],yAxis:["left","right"]},FW={width:"100%",height:"100%"},bm={x:0,y:0};function ki(e){return e}var WW=function(t,r){return r==="horizontal"?t.x:r==="vertical"?t.y:r==="centric"?t.angle:t.radius},zW=function(t,r,n,i){var a=r.find(function(f){return f&&f.index===n});if(a){if(t==="horizontal")return{x:a.coordinate,y:i.y};if(t==="vertical")return{x:i.x,y:a.coordinate};if(t==="centric"){var o=a.coordinate,u=i.radius;return j(j(j({},i),se(i.cx,i.cy,u,o)),{},{angle:o,radius:u})}var c=a.coordinate,s=i.angle;return j(j(j({},i),se(i.cx,i.cy,c,s)),{},{angle:s,radius:c})}return bm},Oo=function(t,r){var n=r.graphicalItems,i=r.dataStartIndex,a=r.dataEndIndex,o=(n??[]).reduce(function(u,c){var s=c.props.data;return s&&s.length?[].concat(Xr(u),Xr(s)):u},[]);return o.length>0?o:t&&t.length&&L(i)&&L(a)?t.slice(i,a+1):[]};function xm(e){return e==="number"?[0,"auto"]:void 0}var Pc=function(t,r,n,i){var a=t.graphicalItems,o=t.tooltipAxis,u=Oo(r,t);return n<0||!a||!a.length||n>=u.length?null:a.reduce(function(c,s){var f,l=(f=s.props.data)!==null&&f!==void 0?f:r;l&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=n&&(l=l.slice(t.dataStartIndex,t.dataEndIndex+1));var p;if(o.dataKey&&!o.allowDuplicatedCategory){var h=l===void 0?u:l;p=Ni(h,o.dataKey,i)}else p=l&&l[n]||u[n];return p?[].concat(Xr(c),[ay(s,p)]):c},[])},Ih=function(t,r,n,i){var a=i||{x:t.chartX,y:t.chartY},o=WW(a,n),u=t.orderedTooltipTicks,c=t.tooltipAxis,s=t.tooltipTicks,f=ck(o,u,s,c);if(f>=0&&s){var l=s[f]&&s[f].value,p=Pc(t,r,f,l),h=zW(n,u,f,a);return{activeTooltipIndex:f,activeLabel:l,activePayload:p,activeCoordinate:h}}return null},UW=function(t,r){var n=r.axes,i=r.graphicalItems,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.layout,l=t.children,p=t.stackOffset,h=Qv(f,a);return n.reduce(function(v,d){var y,b=d.type.defaultProps!==void 0?j(j({},d.type.defaultProps),d.props):d.props,w=b.type,x=b.dataKey,A=b.allowDataOverflow,m=b.allowDuplicatedCategory,g=b.scale,O=b.ticks,P=b.includeHidden,_=b[o];if(v[_])return v;var E=Oo(t.data,{graphicalItems:i.filter(function(z){var V,le=o in z.props?z.props[o]:(V=z.type.defaultProps)===null||V===void 0?void 0:V[o];return le===_}),dataStartIndex:c,dataEndIndex:s}),$=E.length,T,C,I;vW(b.domain,A,w)&&(T=Bu(b.domain,null,A),h&&(w==="number"||g!=="auto")&&(I=Sn(E,x,"category")));var M=xm(w);if(!T||T.length===0){var k,D=(k=b.domain)!==null&&k!==void 0?k:M;if(x){if(T=Sn(E,x,w),w==="category"&&h){var B=Z0(T);m&&B?(C=T,T=Aa(0,$)):m||(T=tp(D,T,d).reduce(function(z,V){return z.indexOf(V)>=0?z:[].concat(Xr(z),[V])},[]))}else if(w==="category")m?T=T.filter(function(z){return z!==""&&!Y(z)}):T=tp(D,T,d).reduce(function(z,V){return z.indexOf(V)>=0||V===""||Y(V)?z:[].concat(Xr(z),[V])},[]);else if(w==="number"){var F=hk(E,i.filter(function(z){var V,le,ye=o in z.props?z.props[o]:(V=z.type.defaultProps)===null||V===void 0?void 0:V[o],Be="hide"in z.props?z.props.hide:(le=z.type.defaultProps)===null||le===void 0?void 0:le.hide;return ye===_&&(P||!Be)}),x,a,f);F&&(T=F)}h&&(w==="number"||g!=="auto")&&(I=Sn(E,x,"category"))}else h?T=Aa(0,$):u&&u[_]&&u[_].hasStack&&w==="number"?T=p==="expand"?[0,1]:iy(u[_].stackGroups,c,s):T=Jv(E,i.filter(function(z){var V=o in z.props?z.props[o]:z.type.defaultProps[o],le="hide"in z.props?z.props.hide:z.type.defaultProps.hide;return V===_&&(P||!le)}),w,f,!0);if(w==="number")T=Oc(l,T,_,a,O),D&&(T=Bu(D,T,A));else if(w==="category"&&D){var q=D,G=T.every(function(z){return q.indexOf(z)>=0});G&&(T=q)}}return j(j({},v),{},K({},_,j(j({},b),{},{axisType:a,domain:T,categoricalDomain:I,duplicateDomain:C,originalDomain:(y=b.domain)!==null&&y!==void 0?y:M,isCategorical:h,layout:f})))},{})},qW=function(t,r){var n=r.graphicalItems,i=r.Axis,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.layout,l=t.children,p=Oo(t.data,{graphicalItems:n,dataStartIndex:c,dataEndIndex:s}),h=p.length,v=Qv(f,a),d=-1;return n.reduce(function(y,b){var w=b.type.defaultProps!==void 0?j(j({},b.type.defaultProps),b.props):b.props,x=w[o],A=xm("number");if(!y[x]){d++;var m;return v?m=Aa(0,h):u&&u[x]&&u[x].hasStack?(m=iy(u[x].stackGroups,c,s),m=Oc(l,m,x,a)):(m=Bu(A,Jv(p,n.filter(function(g){var O,P,_=o in g.props?g.props[o]:(O=g.type.defaultProps)===null||O===void 0?void 0:O[o],E="hide"in g.props?g.props.hide:(P=g.type.defaultProps)===null||P===void 0?void 0:P.hide;return _===x&&!E}),"number",f),i.defaultProps.allowDataOverflow),m=Oc(l,m,x,a)),j(j({},y),{},K({},x,j(j({axisType:a},i.defaultProps),{},{hide:!0,orientation:He(BW,"".concat(a,".").concat(d%2),null),domain:m,originalDomain:A,isCategorical:v,layout:f})))}return y},{})},HW=function(t,r){var n=r.axisType,i=n===void 0?"xAxis":n,a=r.AxisComp,o=r.graphicalItems,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.children,l="".concat(i,"Id"),p=Ke(f,a),h={};return p&&p.length?h=UW(t,{axes:p,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:c,dataEndIndex:s}):o&&o.length&&(h=qW(t,{Axis:a,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:c,dataEndIndex:s})),h},KW=function(t){var r=jt(t),n=gt(r,!1,!0);return{tooltipTicks:n,orderedTooltipTicks:Gc(n,function(i){return i.coordinate}),tooltipAxis:r,tooltipAxisBandSize:la(r,n)}},kh=function(t){var r=t.children,n=t.defaultShowTooltip,i=ze(r,Br),a=0,o=0;return t.data&&t.data.length!==0&&(o=t.data.length-1),i&&i.props&&(i.props.startIndex>=0&&(a=i.props.startIndex),i.props.endIndex>=0&&(o=i.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:a,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!n}},GW=function(t){return!t||!t.length?!1:t.some(function(r){var n=bt(r&&r.type);return n&&n.indexOf("Bar")>=0})},Dh=function(t){return t==="horizontal"?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:t==="vertical"?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:t==="centric"?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},VW=function(t,r){var n=t.props,i=t.graphicalItems,a=t.xAxisMap,o=a===void 0?{}:a,u=t.yAxisMap,c=u===void 0?{}:u,s=n.width,f=n.height,l=n.children,p=n.margin||{},h=ze(l,Br),v=ze(l,Or),d=Object.keys(c).reduce(function(m,g){var O=c[g],P=O.orientation;return!O.mirror&&!O.hide?j(j({},m),{},K({},P,m[P]+O.width)):m},{left:p.left||0,right:p.right||0}),y=Object.keys(o).reduce(function(m,g){var O=o[g],P=O.orientation;return!O.mirror&&!O.hide?j(j({},m),{},K({},P,He(m,"".concat(P))+O.height)):m},{top:p.top||0,bottom:p.bottom||0}),b=j(j({},y),d),w=b.bottom;h&&(b.bottom+=h.props.height||Br.defaultProps.height),v&&r&&(b=fk(b,i,n,r));var x=s-b.left-b.right,A=f-b.top-b.bottom;return j(j({brushBottom:w},b),{},{width:Math.max(x,0),height:Math.max(A,0)})},XW=function(t,r){if(r==="xAxis")return t[r].width;if(r==="yAxis")return t[r].height},Ms=function(t){var r=t.chartName,n=t.GraphicalChild,i=t.defaultTooltipEventType,a=i===void 0?"axis":i,o=t.validateTooltipEventTypes,u=o===void 0?["axis"]:o,c=t.axisComponents,s=t.legendContent,f=t.formatAxisMap,l=t.defaultProps,p=function(b,w){var x=w.graphicalItems,A=w.stackGroups,m=w.offset,g=w.updateId,O=w.dataStartIndex,P=w.dataEndIndex,_=b.barSize,E=b.layout,$=b.barGap,T=b.barCategoryGap,C=b.maxBarSize,I=Dh(E),M=I.numericAxisName,k=I.cateAxisName,D=GW(x),B=[];return x.forEach(function(F,q){var G=Oo(b.data,{graphicalItems:[F],dataStartIndex:O,dataEndIndex:P}),z=F.type.defaultProps!==void 0?j(j({},F.type.defaultProps),F.props):F.props,V=z.dataKey,le=z.maxBarSize,ye=z["".concat(M,"Id")],Be=z["".concat(k,"Id")],Lt={},De=c.reduce(function(Bt,Ft){var Ao=w["".concat(Ft.axisType,"Map")],Cs=z["".concat(Ft.axisType,"Id")];Ao&&Ao[Cs]||Ft.axisType==="zAxis"||ir();var Is=Ao[Cs];return j(j({},Bt),{},K(K({},Ft.axisType,Is),"".concat(Ft.axisType,"Ticks"),gt(Is)))},Lt),W=De[k],Z=De["".concat(k,"Ticks")],Q=A&&A[ye]&&A[ye].hasStack&&Ok(F,A[ye].stackGroups),N=bt(F.type).indexOf("Bar")>=0,de=la(W,Z),ee=[],be=D&&sk({barSize:_,stackGroups:A,totalSize:XW(De,k)});if(N){var xe,Ne,Tt=Y(le)?C:le,fr=(xe=(Ne=la(W,Z,!0))!==null&&Ne!==void 0?Ne:Tt)!==null&&xe!==void 0?xe:0;ee=lk({barGap:$,barCategoryGap:T,bandSize:fr!==de?fr:de,sizeList:be[Be],maxBarSize:Tt}),fr!==de&&(ee=ee.map(function(Bt){return j(j({},Bt),{},{position:j(j({},Bt.position),{},{offset:Bt.position.offset-fr/2})})}))}var bi=F&&F.type&&F.type.getComposedData;bi&&B.push({props:j(j({},bi(j(j({},De),{},{displayedData:G,props:b,dataKey:V,item:F,bandSize:de,barPosition:ee,offset:m,stackedData:Q,layout:E,dataStartIndex:O,dataEndIndex:P}))),{},K(K(K({key:F.key||"item-".concat(q)},M,De[M]),k,De[k]),"animationId",g)),childIndex:lx(F,b.children),item:F})}),B},h=function(b,w){var x=b.props,A=b.dataStartIndex,m=b.dataEndIndex,g=b.updateId;if(!Gs({props:x}))return null;var O=x.children,P=x.layout,_=x.stackOffset,E=x.data,$=x.reverseStackOrder,T=Dh(P),C=T.numericAxisName,I=T.cateAxisName,M=Ke(O,n),k=xk(E,M,"".concat(C,"Id"),"".concat(I,"Id"),_,$),D=c.reduce(function(z,V){var le="".concat(V.axisType,"Map");return j(j({},z),{},K({},le,HW(x,j(j({},V),{},{graphicalItems:M,stackGroups:V.axisType===C&&k,dataStartIndex:A,dataEndIndex:m}))))},{}),B=VW(j(j({},D),{},{props:x,graphicalItems:M}),w==null?void 0:w.legendBBox);Object.keys(D).forEach(function(z){D[z]=f(x,D[z],B,z.replace("Map",""),r)});var F=D["".concat(I,"Map")],q=KW(F),G=p(x,j(j({},D),{},{dataStartIndex:A,dataEndIndex:m,updateId:g,graphicalItems:M,stackGroups:k,offset:B}));return j(j({formattedGraphicalItems:G,graphicalItems:M,offset:B,stackGroups:k},q),D)},v=function(y){function b(w){var x,A,m;return TW(this,b),m=MW(this,b,[w]),K(m,"eventEmitterSymbol",Symbol("rechartsEventEmitter")),K(m,"accessibilityManager",new dW),K(m,"handleLegendBBoxUpdate",function(g){if(g){var O=m.state,P=O.dataStartIndex,_=O.dataEndIndex,E=O.updateId;m.setState(j({legendBBox:g},h({props:m.props,dataStartIndex:P,dataEndIndex:_,updateId:E},j(j({},m.state),{},{legendBBox:g}))))}}),K(m,"handleReceiveSyncEvent",function(g,O,P){if(m.props.syncId===g){if(P===m.eventEmitterSymbol&&typeof m.props.syncMethod!="function")return;m.applySyncEvent(O)}}),K(m,"handleBrushChange",function(g){var O=g.startIndex,P=g.endIndex;if(O!==m.state.dataStartIndex||P!==m.state.dataEndIndex){var _=m.state.updateId;m.setState(function(){return j({dataStartIndex:O,dataEndIndex:P},h({props:m.props,dataStartIndex:O,dataEndIndex:P,updateId:_},m.state))}),m.triggerSyncEvent({dataStartIndex:O,dataEndIndex:P})}}),K(m,"handleMouseEnter",function(g){var O=m.getMouseInfo(g);if(O){var P=j(j({},O),{},{isTooltipActive:!0});m.setState(P),m.triggerSyncEvent(P);var _=m.props.onMouseEnter;X(_)&&_(P,g)}}),K(m,"triggeredAfterMouseMove",function(g){var O=m.getMouseInfo(g),P=O?j(j({},O),{},{isTooltipActive:!0}):{isTooltipActive:!1};m.setState(P),m.triggerSyncEvent(P);var _=m.props.onMouseMove;X(_)&&_(P,g)}),K(m,"handleItemMouseEnter",function(g){m.setState(function(){return{isTooltipActive:!0,activeItem:g,activePayload:g.tooltipPayload,activeCoordinate:g.tooltipPosition||{x:g.cx,y:g.cy}}})}),K(m,"handleItemMouseLeave",function(){m.setState(function(){return{isTooltipActive:!1}})}),K(m,"handleMouseMove",function(g){g.persist(),m.throttleTriggeredAfterMouseMove(g)}),K(m,"handleMouseLeave",function(g){m.throttleTriggeredAfterMouseMove.cancel();var O={isTooltipActive:!1};m.setState(O),m.triggerSyncEvent(O);var P=m.props.onMouseLeave;X(P)&&P(O,g)}),K(m,"handleOuterEvent",function(g){var O=sx(g),P=He(m.props,"".concat(O));if(O&&X(P)){var _,E;/.*touch.*/i.test(O)?E=m.getMouseInfo(g.changedTouches[0]):E=m.getMouseInfo(g),P((_=E)!==null&&_!==void 0?_:{},g)}}),K(m,"handleClick",function(g){var O=m.getMouseInfo(g);if(O){var P=j(j({},O),{},{isTooltipActive:!0});m.setState(P),m.triggerSyncEvent(P);var _=m.props.onClick;X(_)&&_(P,g)}}),K(m,"handleMouseDown",function(g){var O=m.props.onMouseDown;if(X(O)){var P=m.getMouseInfo(g);O(P,g)}}),K(m,"handleMouseUp",function(g){var O=m.props.onMouseUp;if(X(O)){var P=m.getMouseInfo(g);O(P,g)}}),K(m,"handleTouchMove",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&m.throttleTriggeredAfterMouseMove(g.changedTouches[0])}),K(m,"handleTouchStart",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&m.handleMouseDown(g.changedTouches[0])}),K(m,"handleTouchEnd",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&m.handleMouseUp(g.changedTouches[0])}),K(m,"handleDoubleClick",function(g){var O=m.props.onDoubleClick;if(X(O)){var P=m.getMouseInfo(g);O(P,g)}}),K(m,"handleContextMenu",function(g){var O=m.props.onContextMenu;if(X(O)){var P=m.getMouseInfo(g);O(P,g)}}),K(m,"triggerSyncEvent",function(g){m.props.syncId!==void 0&&Xo.emit(Yo,m.props.syncId,g,m.eventEmitterSymbol)}),K(m,"applySyncEvent",function(g){var O=m.props,P=O.layout,_=O.syncMethod,E=m.state.updateId,$=g.dataStartIndex,T=g.dataEndIndex;if(g.dataStartIndex!==void 0||g.dataEndIndex!==void 0)m.setState(j({dataStartIndex:$,dataEndIndex:T},h({props:m.props,dataStartIndex:$,dataEndIndex:T,updateId:E},m.state)));else if(g.activeTooltipIndex!==void 0){var C=g.chartX,I=g.chartY,M=g.activeTooltipIndex,k=m.state,D=k.offset,B=k.tooltipTicks;if(!D)return;if(typeof _=="function")M=_(B,g);else if(_==="value"){M=-1;for(var F=0;F<B.length;F++)if(B[F].value===g.activeLabel){M=F;break}}var q=j(j({},D),{},{x:D.left,y:D.top}),G=Math.min(C,q.x+q.width),z=Math.min(I,q.y+q.height),V=B[M]&&B[M].value,le=Pc(m.state,m.props.data,M),ye=B[M]?{x:P==="horizontal"?B[M].coordinate:G,y:P==="horizontal"?z:B[M].coordinate}:bm;m.setState(j(j({},g),{},{activeLabel:V,activeCoordinate:ye,activePayload:le,activeTooltipIndex:M}))}else m.setState(g)}),K(m,"renderCursor",function(g){var O,P=m.state,_=P.isTooltipActive,E=P.activeCoordinate,$=P.activePayload,T=P.offset,C=P.activeTooltipIndex,I=P.tooltipAxisBandSize,M=m.getTooltipEventType(),k=(O=g.props.active)!==null&&O!==void 0?O:_,D=m.props.layout,B=g.key||"_recharts-cursor";return S.createElement(wW,{key:B,activeCoordinate:E,activePayload:$,activeTooltipIndex:C,chartName:r,element:g,isActive:k,layout:D,offset:T,tooltipAxisBandSize:I,tooltipEventType:M})}),K(m,"renderPolarAxis",function(g,O,P){var _=He(g,"type.axisType"),E=He(m.state,"".concat(_,"Map")),$=g.type.defaultProps,T=$!==void 0?j(j({},$),g.props):g.props,C=E&&E[T["".concat(_,"Id")]];return R.cloneElement(g,j(j({},C),{},{className:J(_,C.className),key:g.key||"".concat(O,"-").concat(P),ticks:gt(C,!0)}))}),K(m,"renderPolarGrid",function(g){var O=g.props,P=O.radialLines,_=O.polarAngles,E=O.polarRadius,$=m.state,T=$.radiusAxisMap,C=$.angleAxisMap,I=jt(T),M=jt(C),k=M.cx,D=M.cy,B=M.innerRadius,F=M.outerRadius;return R.cloneElement(g,{polarAngles:Array.isArray(_)?_:gt(M,!0).map(function(q){return q.coordinate}),polarRadius:Array.isArray(E)?E:gt(I,!0).map(function(q){return q.coordinate}),cx:k,cy:D,innerRadius:B,outerRadius:F,key:g.key||"polar-grid",radialLines:P})}),K(m,"renderLegend",function(){var g=m.state.formattedGraphicalItems,O=m.props,P=O.children,_=O.width,E=O.height,$=m.props.margin||{},T=_-($.left||0)-($.right||0),C=Yv({children:P,formattedGraphicalItems:g,legendWidth:T,legendContent:s});if(!C)return null;var I=C.item,M=Mh(C,OW);return R.cloneElement(I,j(j({},M),{},{chartWidth:_,chartHeight:E,margin:$,onBBoxUpdate:m.handleLegendBBoxUpdate}))}),K(m,"renderTooltip",function(){var g,O=m.props,P=O.children,_=O.accessibilityLayer,E=ze(P,dt);if(!E)return null;var $=m.state,T=$.isTooltipActive,C=$.activeCoordinate,I=$.activePayload,M=$.activeLabel,k=$.offset,D=(g=E.props.active)!==null&&g!==void 0?g:T;return R.cloneElement(E,{viewBox:j(j({},k),{},{x:k.left,y:k.top}),active:D,label:M,payload:D?I:[],coordinate:C,accessibilityLayer:_})}),K(m,"renderBrush",function(g){var O=m.props,P=O.margin,_=O.data,E=m.state,$=E.offset,T=E.dataStartIndex,C=E.dataEndIndex,I=E.updateId;return R.cloneElement(g,{key:g.key||"_recharts-brush",onChange:Ei(m.handleBrushChange,g.props.onChange),data:_,x:L(g.props.x)?g.props.x:$.left,y:L(g.props.y)?g.props.y:$.top+$.height+$.brushBottom-(P.bottom||0),width:L(g.props.width)?g.props.width:$.width,startIndex:T,endIndex:C,updateId:"brush-".concat(I)})}),K(m,"renderReferenceElement",function(g,O,P){if(!g)return null;var _=m,E=_.clipPathId,$=m.state,T=$.xAxisMap,C=$.yAxisMap,I=$.offset,M=g.type.defaultProps||{},k=g.props,D=k.xAxisId,B=D===void 0?M.xAxisId:D,F=k.yAxisId,q=F===void 0?M.yAxisId:F;return R.cloneElement(g,{key:g.key||"".concat(O,"-").concat(P),xAxis:T[B],yAxis:C[q],viewBox:{x:I.left,y:I.top,width:I.width,height:I.height},clipPathId:E})}),K(m,"renderActivePoints",function(g){var O=g.item,P=g.activePoint,_=g.basePoint,E=g.childIndex,$=g.isRange,T=[],C=O.props.key,I=O.item.type.defaultProps!==void 0?j(j({},O.item.type.defaultProps),O.item.props):O.item.props,M=I.activeDot,k=I.dataKey,D=j(j({index:E,dataKey:k,cx:P.x,cy:P.y,r:4,fill:xs(O.item),strokeWidth:2,stroke:"#fff",payload:P.payload,value:P.value},H(M,!1)),Ri(M));return T.push(b.renderActiveDot(M,D,"".concat(C,"-activePoint-").concat(E))),_?T.push(b.renderActiveDot(M,j(j({},D),{},{cx:_.x,cy:_.y}),"".concat(C,"-basePoint-").concat(E))):$&&T.push(null),T}),K(m,"renderGraphicChild",function(g,O,P){var _=m.filterFormatItem(g,O,P);if(!_)return null;var E=m.getTooltipEventType(),$=m.state,T=$.isTooltipActive,C=$.tooltipAxis,I=$.activeTooltipIndex,M=$.activeLabel,k=m.props.children,D=ze(k,dt),B=_.props,F=B.points,q=B.isRange,G=B.baseLine,z=_.item.type.defaultProps!==void 0?j(j({},_.item.type.defaultProps),_.item.props):_.item.props,V=z.activeDot,le=z.hide,ye=z.activeBar,Be=z.activeShape,Lt=!!(!le&&T&&D&&(V||ye||Be)),De={};E!=="axis"&&D&&D.props.trigger==="click"?De={onClick:Ei(m.handleItemMouseEnter,g.props.onClick)}:E!=="axis"&&(De={onMouseLeave:Ei(m.handleItemMouseLeave,g.props.onMouseLeave),onMouseEnter:Ei(m.handleItemMouseEnter,g.props.onMouseEnter)});var W=R.cloneElement(g,j(j({},_.props),De));function Z(Ft){return typeof C.dataKey=="function"?C.dataKey(Ft.payload):null}if(Lt)if(I>=0){var Q,N;if(C.dataKey&&!C.allowDuplicatedCategory){var de=typeof C.dataKey=="function"?Z:"payload.".concat(C.dataKey.toString());Q=Ni(F,de,M),N=q&&G&&Ni(G,de,M)}else Q=F==null?void 0:F[I],N=q&&G&&G[I];if(Be||ye){var ee=g.props.activeIndex!==void 0?g.props.activeIndex:I;return[R.cloneElement(g,j(j(j({},_.props),De),{},{activeIndex:ee})),null,null]}if(!Y(Q))return[W].concat(Xr(m.renderActivePoints({item:_,activePoint:Q,basePoint:N,childIndex:I,isRange:q})))}else{var be,xe=(be=m.getItemByXY(m.state.activeCoordinate))!==null&&be!==void 0?be:{graphicalItem:W},Ne=xe.graphicalItem,Tt=Ne.item,fr=Tt===void 0?g:Tt,bi=Ne.childIndex,Bt=j(j(j({},_.props),De),{},{activeIndex:bi});return[R.cloneElement(fr,Bt),null,null]}return q?[W,null,null]:[W,null]}),K(m,"renderCustomized",function(g,O,P){return R.cloneElement(g,j(j({key:"recharts-customized-".concat(P)},m.props),m.state))}),K(m,"renderMap",{CartesianGrid:{handler:ki,once:!0},ReferenceArea:{handler:m.renderReferenceElement},ReferenceLine:{handler:ki},ReferenceDot:{handler:m.renderReferenceElement},XAxis:{handler:ki},YAxis:{handler:ki},Brush:{handler:m.renderBrush,once:!0},Bar:{handler:m.renderGraphicChild},Line:{handler:m.renderGraphicChild},Area:{handler:m.renderGraphicChild},Radar:{handler:m.renderGraphicChild},RadialBar:{handler:m.renderGraphicChild},Scatter:{handler:m.renderGraphicChild},Pie:{handler:m.renderGraphicChild},Funnel:{handler:m.renderGraphicChild},Tooltip:{handler:m.renderCursor,once:!0},PolarGrid:{handler:m.renderPolarGrid,once:!0},PolarAngleAxis:{handler:m.renderPolarAxis},PolarRadiusAxis:{handler:m.renderPolarAxis},Customized:{handler:m.renderCustomized}}),m.clipPathId="".concat((x=w.id)!==null&&x!==void 0?x:tn("recharts"),"-clip"),m.throttleTriggeredAfterMouseMove=Xd(m.triggeredAfterMouseMove,(A=w.throttleDelay)!==null&&A!==void 0?A:1e3/60),m.state={},m}return kW(b,y),jW(b,[{key:"componentDidMount",value:function(){var x,A;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:(x=this.props.margin.left)!==null&&x!==void 0?x:0,top:(A=this.props.margin.top)!==null&&A!==void 0?A:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var x=this.props,A=x.children,m=x.data,g=x.height,O=x.layout,P=ze(A,dt);if(P){var _=P.props.defaultIndex;if(!(typeof _!="number"||_<0||_>this.state.tooltipTicks.length-1)){var E=this.state.tooltipTicks[_]&&this.state.tooltipTicks[_].value,$=Pc(this.state,m,_,E),T=this.state.tooltipTicks[_].coordinate,C=(this.state.offset.top+g)/2,I=O==="horizontal",M=I?{x:T,y:C}:{y:T,x:C},k=this.state.formattedGraphicalItems.find(function(B){var F=B.item;return F.type.name==="Scatter"});k&&(M=j(j({},M),k.props.points[_].tooltipPosition),$=k.props.points[_].tooltipPayload);var D={activeTooltipIndex:_,isTooltipActive:!0,activeLabel:E,activePayload:$,activeCoordinate:M};this.setState(D),this.renderCursor(P),this.accessibilityManager.setIndex(_)}}}},{key:"getSnapshotBeforeUpdate",value:function(x,A){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==A.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==x.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==x.margin){var m,g;this.accessibilityManager.setDetails({offset:{left:(m=this.props.margin.left)!==null&&m!==void 0?m:0,top:(g=this.props.margin.top)!==null&&g!==void 0?g:0}})}return null}},{key:"componentDidUpdate",value:function(x){eu([ze(x.children,dt)],[ze(this.props.children,dt)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var x=ze(this.props.children,dt);if(x&&typeof x.props.shared=="boolean"){var A=x.props.shared?"axis":"item";return u.indexOf(A)>=0?A:a}return a}},{key:"getMouseInfo",value:function(x){if(!this.container)return null;var A=this.container,m=A.getBoundingClientRect(),g=NE(m),O={chartX:Math.round(x.pageX-g.left),chartY:Math.round(x.pageY-g.top)},P=m.width/A.offsetWidth||1,_=this.inRange(O.chartX,O.chartY,P);if(!_)return null;var E=this.state,$=E.xAxisMap,T=E.yAxisMap,C=this.getTooltipEventType(),I=Ih(this.state,this.props.data,this.props.layout,_);if(C!=="axis"&&$&&T){var M=jt($).scale,k=jt(T).scale,D=M&&M.invert?M.invert(O.chartX):null,B=k&&k.invert?k.invert(O.chartY):null;return j(j({},O),{},{xValue:D,yValue:B},I)}return I?j(j({},O),I):null}},{key:"inRange",value:function(x,A){var m=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,g=this.props.layout,O=x/m,P=A/m;if(g==="horizontal"||g==="vertical"){var _=this.state.offset,E=O>=_.left&&O<=_.left+_.width&&P>=_.top&&P<=_.top+_.height;return E?{x:O,y:P}:null}var $=this.state,T=$.angleAxisMap,C=$.radiusAxisMap;if(T&&C){var I=jt(T);return ip({x:O,y:P},I)}return null}},{key:"parseEventsOfWrapper",value:function(){var x=this.props.children,A=this.getTooltipEventType(),m=ze(x,dt),g={};m&&A==="axis"&&(m.props.trigger==="click"?g={onClick:this.handleClick}:g={onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu});var O=Ri(this.props,this.handleOuterEvent);return j(j({},O),g)}},{key:"addListener",value:function(){Xo.on(Yo,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){Xo.removeListener(Yo,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(x,A,m){for(var g=this.state.formattedGraphicalItems,O=0,P=g.length;O<P;O++){var _=g[O];if(_.item===x||_.props.key===x.key||A===bt(_.item.type)&&m===_.childIndex)return _}return null}},{key:"renderClipPath",value:function(){var x=this.clipPathId,A=this.state.offset,m=A.left,g=A.top,O=A.height,P=A.width;return S.createElement("defs",null,S.createElement("clipPath",{id:x},S.createElement("rect",{x:m,y:g,height:O,width:P})))}},{key:"getXScales",value:function(){var x=this.state.xAxisMap;return x?Object.entries(x).reduce(function(A,m){var g=jh(m,2),O=g[0],P=g[1];return j(j({},A),{},K({},O,P.scale))},{}):null}},{key:"getYScales",value:function(){var x=this.state.yAxisMap;return x?Object.entries(x).reduce(function(A,m){var g=jh(m,2),O=g[0],P=g[1];return j(j({},A),{},K({},O,P.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(x){var A;return(A=this.state.xAxisMap)===null||A===void 0||(A=A[x])===null||A===void 0?void 0:A.scale}},{key:"getYScaleByAxisId",value:function(x){var A;return(A=this.state.yAxisMap)===null||A===void 0||(A=A[x])===null||A===void 0?void 0:A.scale}},{key:"getItemByXY",value:function(x){var A=this.state,m=A.formattedGraphicalItems,g=A.activeItem;if(m&&m.length)for(var O=0,P=m.length;O<P;O++){var _=m[O],E=_.props,$=_.item,T=$.type.defaultProps!==void 0?j(j({},$.type.defaultProps),$.props):$.props,C=bt($.type);if(C==="Bar"){var I=(E.data||[]).find(function(B){return gN(x,B)});if(I)return{graphicalItem:_,payload:I}}else if(C==="RadialBar"){var M=(E.data||[]).find(function(B){return ip(x,B)});if(M)return{graphicalItem:_,payload:M}}else if(fo(_,g)||po(_,g)||ni(_,g)){var k=oL({graphicalItem:_,activeTooltipItem:g,itemData:T.data}),D=T.activeIndex===void 0?k:T.activeIndex;return{graphicalItem:j(j({},_),{},{childIndex:D}),payload:ni(_,g)?T.data[k]:_.props.data[k]}}}return null}},{key:"render",value:function(){var x=this;if(!Gs(this))return null;var A=this.props,m=A.children,g=A.className,O=A.width,P=A.height,_=A.style,E=A.compact,$=A.title,T=A.desc,C=Mh(A,AW),I=H(C,!1);if(E)return S.createElement(ph,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},S.createElement(ru,xr({},I,{width:O,height:P,title:$,desc:T}),this.renderClipPath(),Xs(m,this.renderMap)));if(this.props.accessibilityLayer){var M,k;I.tabIndex=(M=this.props.tabIndex)!==null&&M!==void 0?M:0,I.role=(k=this.props.role)!==null&&k!==void 0?k:"application",I.onKeyDown=function(B){x.accessibilityManager.keyboardEvent(B)},I.onFocus=function(){x.accessibilityManager.focus()}}var D=this.parseEventsOfWrapper();return S.createElement(ph,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},S.createElement("div",xr({className:J("recharts-wrapper",g),style:j({position:"relative",cursor:"default",width:O,height:P},_)},D,{ref:function(F){x.container=F}}),S.createElement(ru,xr({},I,{width:O,height:P,title:$,desc:T,style:FW}),this.renderClipPath(),Xs(m,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}])}(R.Component);K(v,"displayName",r),K(v,"defaultProps",j({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},l)),K(v,"getDerivedStateFromProps",function(y,b){var w=y.dataKey,x=y.data,A=y.children,m=y.width,g=y.height,O=y.layout,P=y.stackOffset,_=y.margin,E=b.dataStartIndex,$=b.dataEndIndex;if(b.updateId===void 0){var T=kh(y);return j(j(j({},T),{},{updateId:0},h(j(j({props:y},T),{},{updateId:0}),b)),{},{prevDataKey:w,prevData:x,prevWidth:m,prevHeight:g,prevLayout:O,prevStackOffset:P,prevMargin:_,prevChildren:A})}if(w!==b.prevDataKey||x!==b.prevData||m!==b.prevWidth||g!==b.prevHeight||O!==b.prevLayout||P!==b.prevStackOffset||!wr(_,b.prevMargin)){var C=kh(y),I={chartX:b.chartX,chartY:b.chartY,isTooltipActive:b.isTooltipActive},M=j(j({},Ih(b,x,O)),{},{updateId:b.updateId+1}),k=j(j(j({},C),I),M);return j(j(j({},k),h(j({props:y},k),b)),{},{prevDataKey:w,prevData:x,prevWidth:m,prevHeight:g,prevLayout:O,prevStackOffset:P,prevMargin:_,prevChildren:A})}if(!eu(A,b.prevChildren)){var D,B,F,q,G=ze(A,Br),z=G&&(D=(B=G.props)===null||B===void 0?void 0:B.startIndex)!==null&&D!==void 0?D:E,V=G&&(F=(q=G.props)===null||q===void 0?void 0:q.endIndex)!==null&&F!==void 0?F:$,le=z!==E||V!==$,ye=!Y(x),Be=ye&&!le?b.updateId:b.updateId+1;return j(j({updateId:Be},h(j(j({props:y},b),{},{updateId:Be,dataStartIndex:z,dataEndIndex:V}),b)),{},{prevChildren:A,dataStartIndex:z,dataEndIndex:V})}return null}),K(v,"renderActiveDot",function(y,b,w){var x;return R.isValidElement(y)?x=R.cloneElement(y,b):X(y)?x=y(b):x=S.createElement(oo,b),S.createElement(te,{className:"recharts-active-dot",key:w},x)});var d=R.forwardRef(function(b,w){return S.createElement(v,xr({},b,{ref:w}))});return d.displayName=v.displayName,d},QW=Ms({chartName:"LineChart",GraphicalChild:bo,axisComponents:[{axisType:"xAxis",AxisComp:xo},{axisType:"yAxis",AxisComp:wo}],formatAxisMap:By}),ez=Ms({chartName:"BarChart",GraphicalChild:sn,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:xo},{axisType:"yAxis",AxisComp:wo}],formatAxisMap:By}),tz=Ms({chartName:"PieChart",GraphicalChild:Rt,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:lo},{axisType:"radiusAxis",AxisComp:co}],formatAxisMap:Ck,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}});export{ez as B,P3 as C,QW as L,tz as P,JW as R,dt as T,xo as X,wo as Y,Or as a,bo as b,Rt as c,Xc as d,sn as e};
//# sourceMappingURL=charts-CfM58meh.js.map
