import{l as G,u as J,m as V,b as Z,o as ee,c as R,T as n,j as e,p as v}from"./index-BAEl3mLK.js";import{r as B}from"./vendor-o6zXO7vr.js";import{c as a,X as se,a7 as f,B as j,a6 as te,T as re,ab as ae,R as b,C as l,ac as x,y as _,f as p,P as ie,S as z,a3 as ne,j as le,ad as de,k as g,ae as H,n as ce,o as oe,u as xe,t as A,g as F,af as d}from"./ui-DUwPBEDa.js";import"./charts-DW2bYSvi.js";const{Title:he,Text:h}=re,ye=()=>{const{taskId:i}=G(),M=J(),[W,q]=B.useState("overview"),{subscribeToTask:k,unsubscribeFromTask:I,connected:T}=V(),{tasks:je,getTaskById:L}=Z(),{logs:Y,connected:u}=ee(),c=L(i||""),w=Y[i||""]||[],{data:m,isLoading:D,error:E,refetch:y}=R({queryKey:["task",i],queryFn:()=>v.get(`/tasks/${i}`),enabled:!!i,refetchInterval:(c==null?void 0:c.status)===n.Running?5e3:!1}),{data:t}=R({queryKey:["task-results",i],queryFn:()=>v.get(`/results?task_id=${i}`),enabled:!!i&&(c==null?void 0:c.status)===n.Completed});if(B.useEffect(()=>{if(i&&T)return k(i),()=>{I(i)}},[i,T,k,I]),D)return e.jsx("div",{style:{padding:24},children:e.jsx(a,{children:e.jsxs("div",{style:{textAlign:"center",padding:"40px 0"},children:[e.jsx(se,{size:"large"}),e.jsx("div",{style:{marginTop:16},children:"正在加载任务详情..."})]})})});if(E)return e.jsx("div",{style:{padding:24},children:e.jsx(a,{children:e.jsx(f,{message:"加载失败",description:"无法加载任务详情，请检查任务ID是否正确。",type:"error",showIcon:!0,action:e.jsx(j,{size:"small",danger:!0,onClick:()=>y(),children:"重试"})})})});const r=(m==null?void 0:m.data)||c;if(!r)return e.jsx("div",{style:{padding:24},children:e.jsx(a,{children:e.jsx(f,{message:"任务不存在",description:"未找到指定的任务。",type:"warning",showIcon:!0})})});const K=s=>{switch(s){case"pending":return"default";case"running":return"processing";case"completed":return"success";case"failed":return"error";case"stopped":return"warning";default:return"default"}},P=s=>{switch(s){case"pending":return"等待中";case"running":return"运行中";case"completed":return"已完成";case"failed":return"失败";case"stopped":return"已停止";default:return s}},U=async()=>{try{await v.delete(`/tasks/${i}`),y()}catch(s){console.error("停止任务失败:",s)}},N=()=>{console.log("下载结果")},O=()=>{var s,o,S,$,C;return e.jsxs(b,{gutter:[16,16],children:[e.jsxs(l,{xs:24,lg:16,children:[e.jsx(a,{title:"任务信息",style:{marginBottom:16},children:e.jsxs(x,{column:2,children:[e.jsx(x.Item,{label:"任务ID",children:e.jsx(h,{copyable:!0,children:r.id})}),e.jsx(x.Item,{label:"目标",children:e.jsx(h,{strong:!0,children:r.target})}),e.jsx(x.Item,{label:"状态",children:e.jsx(_,{status:K(r.status),text:P(r.status)})}),e.jsx(x.Item,{label:"扫描类型",children:r.scan_type||"标准扫描"}),e.jsx(x.Item,{label:"创建时间",children:p(r.created_at).format("YYYY-MM-DD HH:mm:ss")}),e.jsx(x.Item,{label:"持续时间",children:r.updated_at?p(r.updated_at).diff(p(r.created_at),"second")+"秒":"-"})]})}),e.jsxs(a,{title:"扫描进度",style:{marginBottom:16},children:[e.jsx(ie,{percent:r.progress||0,status:r.status===n.Failed?"exception":r.status===n.Completed?"success":"active",strokeColor:{"0%":"#108ee9","100%":"#87d068"}}),e.jsx("div",{style:{marginTop:8},children:e.jsx(h,{type:"secondary",children:r.status===n.Running?"正在执行扫描...":r.status===n.Completed?"扫描已完成":r.status===n.Failed?"扫描失败":"等待开始"})})]}),r.error&&e.jsx(a,{title:"错误信息",style:{marginBottom:16},children:e.jsx(f,{message:"任务执行出错",description:r.error,type:"error",showIcon:!0})})]}),e.jsxs(l,{xs:24,lg:8,children:[e.jsx(a,{title:"连接状态",style:{marginBottom:16},children:e.jsxs(z,{direction:"vertical",style:{width:"100%"},children:[e.jsx("div",{children:e.jsx(_,{status:u?"success":"error",text:u?"WebSocket已连接":"WebSocket断开"})}),e.jsx("div",{children:e.jsxs(h,{type:"secondary",children:["实时日志: ",u?"启用":"禁用"]})})]})}),e.jsx(a,{title:"操作",style:{marginBottom:16},children:e.jsxs(z,{direction:"vertical",style:{width:"100%"},children:[r.status===n.Running&&e.jsx(j,{type:"primary",danger:!0,icon:e.jsx(ne,{}),onClick:U,block:!0,children:"停止任务"}),e.jsx(j,{icon:e.jsx(le,{}),onClick:()=>y(),block:!0,children:"刷新状态"}),r.status===n.Completed&&e.jsx(j,{type:"primary",icon:e.jsx(de,{}),onClick:N,block:!0,children:"下载结果"})]})}),(t==null?void 0:t.data)&&e.jsx(a,{title:"扫描统计",style:{marginBottom:16},children:e.jsxs(b,{gutter:16,children:[e.jsx(l,{span:12,children:e.jsx(g,{title:"发现主机",value:((s=t.data.hosts)==null?void 0:s.length)||0,prefix:e.jsx(H,{})})}),e.jsx(l,{span:12,children:e.jsx(g,{title:"开放端口",value:((o=t.data.ports)==null?void 0:o.length)||0,prefix:e.jsx(ce,{})})}),e.jsx(l,{span:12,style:{marginTop:16},children:e.jsx(g,{title:"发现漏洞",value:((S=t.data.vulnerabilities)==null?void 0:S.length)||0,prefix:e.jsx(oe,{}),valueStyle:{color:(($=t.data.vulnerabilities)==null?void 0:$.length)>0?"#cf1322":"#3f8600"}})}),e.jsx(l,{span:12,style:{marginTop:16},children:e.jsx(g,{title:"子域名",value:((C=t.data.subdomains)==null?void 0:C.length)||0,prefix:e.jsx(H,{})})})]})})]})]})},Q=()=>e.jsx(a,{title:"实时日志",style:{height:"600px",overflow:"hidden"},children:e.jsx("div",{style:{height:"520px",overflow:"auto",padding:"8px 0"},children:w.length===0?e.jsxs("div",{style:{textAlign:"center",padding:"40px 0",color:"#999"},children:[e.jsx(xe,{style:{fontSize:24,marginBottom:8}}),e.jsx("div",{children:"暂无日志数据"}),e.jsx(h,{type:"secondary",style:{fontSize:12},children:u?"等待任务产生日志...":"请检查WebSocket连接"})]}):e.jsx(A,{mode:"left",style:{paddingTop:16},children:w.map((s,o)=>e.jsxs(A.Item,{color:s.level==="error"?"red":s.level==="warn"?"orange":"blue",children:[e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(F,{color:s.level==="error"?"red":s.level==="warn"?"orange":"blue",children:s.level.toUpperCase()}),e.jsx(h,{type:"secondary",style:{fontSize:12},children:p(s.timestamp).format("HH:mm:ss")})]}),e.jsx("div",{style:{wordBreak:"break-all"},children:s.message})]},o))})})}),X=()=>e.jsx("div",{children:t!=null&&t.data?e.jsxs(b,{gutter:[16,16],children:[t.data.hosts&&e.jsx(l,{xs:24,lg:12,children:e.jsx(a,{title:`发现主机 (${t.data.hosts.length})`,children:e.jsx(d,{size:"small",dataSource:t.data.hosts,renderItem:s=>e.jsx(d.Item,{children:e.jsx(d.Item.Meta,{title:s.ip_address,description:`状态: ${s.status} | 系统: ${s.os_name||"未知"}`})}),style:{maxHeight:400,overflow:"auto"}})})}),t.data.ports&&e.jsx(l,{xs:24,lg:12,children:e.jsx(a,{title:`开放端口 (${t.data.ports.length})`,children:e.jsx(d,{size:"small",dataSource:t.data.ports,renderItem:s=>e.jsx(d.Item,{children:e.jsx(d.Item.Meta,{title:`${s.port}/${s.protocol}`,description:`服务: ${s.service||"未知"} | 状态: ${s.state}`})}),style:{maxHeight:400,overflow:"auto"}})})}),t.data.vulnerabilities&&t.data.vulnerabilities.length>0&&e.jsx(l,{xs:24,children:e.jsx(a,{title:`发现漏洞 (${t.data.vulnerabilities.length})`,children:e.jsx(d,{dataSource:t.data.vulnerabilities,renderItem:s=>{var o;return e.jsx(d.Item,{children:e.jsx(d.Item.Meta,{title:e.jsxs("span",{children:[e.jsx(F,{color:s.severity==="critical"?"red":s.severity==="high"?"orange":s.severity==="medium"?"gold":"green",children:(o=s.severity)==null?void 0:o.toUpperCase()}),s.name]}),description:s.description})})},style:{maxHeight:400,overflow:"auto"}})})})]}):e.jsx(a,{children:e.jsx("div",{style:{textAlign:"center",padding:"40px 0"},children:e.jsx(h,{type:"secondary",children:"暂无扫描结果"})})})});return e.jsxs("div",{style:{padding:24},children:[e.jsxs("div",{style:{marginBottom:16},children:[e.jsx(j,{icon:e.jsx(te,{}),onClick:()=>M("/tasks"),style:{marginRight:16},children:"返回任务列表"}),e.jsxs(he,{level:3,style:{display:"inline-block",margin:0},children:["任务详情 - ",r.target]})]}),e.jsx(ae,{activeKey:W,onChange:q,items:[{key:"overview",label:"概览",children:O()},{key:"logs",label:"实时日志",children:Q()},{key:"results",label:"扫描结果",children:X()}]})]})};export{ye as default};
//# sourceMappingURL=TaskDetail-hk41wc_h.js.map
