// 扫描任务相关类型
export interface Task {
  id: string;
  target: string;
  task_type: ScanType;
  scan_type?: string;
  status: TaskStatus;
  progress: number;
  created_at: string;
  updated_at?: string;
  started_at?: string;
  completed_at?: string;
  error?: string;
  config: ScanConfig;
}

export interface ScanTask extends Task {}

export interface CreateTaskRequest {
  name: string;
  target: string;
  task_type: string; // 改为string类型以匹配后端
  config?: string; // 改为string类型以匹配后端，将是JSON字符串
  total_steps?: number;
}

export enum ScanType {
  Quick = 'quick',
  Standard = 'standard',
  Deep = 'deep',
  Custom = 'custom',
  WebFocused = 'web_focused',
  NetworkFocused = 'network_focused',
}

export enum TaskStatus {
  Pending = 'pending',
  Running = 'running',
  Completed = 'completed',
  Failed = 'failed',
  Stopped = 'stopped',
}

// 扫描配置
export interface ScanConfig {
  scan_mode: ScanMode;
  target_type: TargetType;
  enable_port_scan: boolean;
  enable_service_detection: boolean;
  enable_dns_resolution: boolean;
  enable_subdomain_enum: boolean;
  enable_web_crawling: boolean;
  enable_vulnerability_scan: boolean;
  max_parallel_tasks: number;
  timeout_per_step: number;
  max_retries: number;
  continue_on_error: boolean;
  stop_on_critical_error: boolean;
  nmap_config: NmapConfig;
  dnsx_config: DnsxConfig;
  subfinder_config: SubfinderConfig;
  httpx_config: HttpxConfig;
  crawl4ai_config: Crawl4AIConfig;
  nuclei_config: NucleiConfig;
}

export enum ScanMode {
  Quick = 'quick',
  Standard = 'standard',
  Deep = 'deep',
  Custom = 'custom',
}

export enum TargetType {
  SingleHost = 'single_host',
  Domain = 'domain',
  Network = 'network',
  WebAsset = 'web_asset',
  Mixed = 'mixed',
}

// 工具配置
export interface NmapConfig {
  scan_type: string;
  port_range: string;
  timing: number;
  threads: number;
  host_timeout: number;
  enable_service_detection: boolean;
  enable_os_detection: boolean;
  enable_script_scan: boolean;
  stealth_mode: boolean;
  aggressive_scan: boolean;
  scripts: string[];
  exclude_ports: number[];
  fragment_packets: boolean;
  spoof_mac: boolean;
  decoy_scan: boolean;
}

export interface DnsxConfig {
  threads: number;
  timeout: number;
  retries: number;
  resolver: string;
  query_types: string[];
  wildcard_filter: boolean;
  subdomain_brute: boolean;
  custom_wordlist?: string;
}

export interface SubfinderConfig {
  threads: number;
  timeout: number;
  sources: string[];
  exclude_sources: string[];
  rate_limit: number;
  recursive: boolean;
  max_depth: number;
  wordlist_size: string;
  use_all_sources: boolean;
  silent: boolean;
  verbose: boolean;
}

export interface HttpxConfig {
  threads: number;
  timeout: number;
  retries: number;
  ports: number[];
  follow_redirects: boolean;
  max_redirects: number;
  custom_headers: Record<string, string>;
  user_agent_type: UserAgentType;
  proxy?: string;
  rate_limit?: number;
  screenshot: boolean;
  tech_detect: boolean;
  status_code: boolean;
  content_length: boolean;
  response_time: boolean;
  extract_title: boolean;
}

export interface Crawl4AIConfig {
  max_depth: number;
  max_pages: number;
  delay_between_requests: number;
  timeout_per_page: number;
  user_agent_type: UserAgentType;
  follow_external_links: boolean;
  extract_images: boolean;
  extract_links: boolean;
  extract_forms: boolean;
  extract_scripts: boolean;
  extract_css: boolean;
  extract_metadata: boolean;
  screenshot: boolean;
  exclude_extensions: string[];
  include_patterns: string[];
  exclude_patterns: string[];
  proxy?: string;
  headers: Record<string, string>;
  cookies: Record<string, string>;
  javascript_enabled: boolean;
  wait_for_selector?: string;
  output_format: string;
}

export interface NucleiConfig {
  templates: string[];
  severity: string[];
  tags: string[];
  exclude_tags: string[];
  threads: number;
  rate_limit: number;
  timeout: number;
  retries: number;
  max_host_error: number;
  bulk_size: number;
  concurrent_requests: number;
  follow_redirects: boolean;
  update_templates: boolean;
  no_color: boolean;
  json_output: boolean;
  verbose: boolean;
  debug: boolean;
  disable_clustering: boolean;
  custom_headers: Record<string, string>;
  proxy?: string;
  user_agent_type: UserAgentType;
  template_urls: string[];
  exclude_matchers: string[];
  include_conditions: string[];
  passive_scan: boolean;
  aggressive_scan: boolean;
}

export enum UserAgentType {
  Desktop = 'desktop',
  Mobile = 'mobile',
  Crawler = 'crawler',
  ApiClient = 'api_client',
  Random = 'random',
}

// 扫描结果类型
export interface Host {
  id: number;
  task_id: string;
  ip_address: string;
  hostname?: string;
  os_name?: string;
  os_info?: string;
  status: string;
  port_count?: number;
  created_at: string;
}

export interface Port {
  id: number;
  task_id: string;
  host_id?: number;
  host_ip?: string;
  port: number;
  protocol: string;
  state: string;
  status?: string;
  service?: string;
  version?: string;
  banner?: string;
  created_at: string;
}

export interface Subdomain {
  id: number;
  task_id: string;
  domain: string;
  subdomain: string;
  ip_address?: string;
  status: string;
  created_at: string;
}

export interface WebAsset {
  id: number;
  task_id: string;
  host_id: number;
  url: string;
  title?: string;
  status_code: number;
  content_type?: string;
  content_length?: number;
  server?: string;
  technologies: string[];
  headers: Record<string, string>;
  created_at: string;
}

export interface CrawledPage {
  id: number;
  task_id: string;
  host_id: number;
  url: string;
  title?: string;
  content: string;
  status_code?: number;
  content_type?: string;
  depth?: number;
  parent_url?: string;
  links: string[];
  images: string[];
  forms: FormInfo[];
  scripts: string[];
  technologies: string[];
  headers: Record<string, string>;
  error_message?: string;
  created_at: string;
}

export interface FormInfo {
  action: string;
  method: string;
  inputs: FormInput[];
  has_file_upload: boolean;
  has_password_field: boolean;
  csrf_token?: string;
}

export interface FormInput {
  input_type: string;
  name: string;
  value?: string;
  placeholder?: string;
  required: boolean;
  max_length?: number;
}

export interface Vulnerability {
  id: number;
  task_id: string;
  host_id?: number;
  host_ip?: string;
  web_asset_id?: number;
  vulnerability_id: string;
  name?: string;
  severity: VulnerabilitySeverity;
  title?: string;
  description?: string;
  solution?: string;
  reference: string[];
  proof_of_concept?: string;
  cvss_score?: number;
  cve_id?: string;
  created_at: string;
}

export enum VulnerabilitySeverity {
  Info = 'info',
  Low = 'low',
  Medium = 'medium',
  High = 'high',
  Critical = 'critical',
}

// API 响应类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// WebSocket 消息类型
export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
}

export interface TaskUpdate {
  task_id: string;
  status: TaskStatus;
  progress: number;
  message?: string;
  error?: string;
}

export interface LogMessage {
  task_id: string;
  level: string;
  message: string;
  timestamp: string;
}

// 统计信息类型
export interface ScanStatistics {
  total_hosts: number;
  total_ports: number;
  total_subdomains: number;
  total_web_assets: number;
  total_pages: number;
  total_vulnerabilities: number;
  vulnerability_by_severity: Record<string, number>;
  port_by_status: Record<string, number>;
  web_asset_by_status: Record<string, number>;
  top_technologies: [string, number][];
  top_services: [string, number][];
  scan_duration: number;
  scan_efficiency: number;
}

// 系统配置类型
export interface SystemConfig {
  max_concurrent_tasks: number;
  default_timeout: number;
  log_level: string;
  enable_real_time_updates: boolean;
  websocket_enabled: boolean;
  rate_limiting: boolean;
  max_requests_per_minute: number;
  storage_retention_days: number;
  export_formats: string[];
  default_scan_mode: ScanMode;
  security_settings: SecuritySettings;
}

export interface SecuritySettings {
  require_authentication: boolean;
  session_timeout: number;
  max_login_attempts: number;
  enable_audit_log: boolean;
  allowed_targets: string[];
  blocked_targets: string[];
  rate_limit_per_ip: number;
}

// 用户认证相关类型
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  user: UserInfo;
}

export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
  role?: UserRole;
}

export interface UpdateUserRequest {
  username?: string;
  email?: string;
  role?: UserRole;
  is_active?: boolean;
}

export interface ChangePasswordRequest {
  old_password: string;
  new_password: string;
}

export interface UserInfo {
  id: number;
  username: string;
  email: string;
  role: UserRole;
  is_active: boolean;
  created_at: string;
  last_login?: string;
}

export enum UserRole {
  Admin = 'admin',
  User = 'user',
  Viewer = 'viewer',
}