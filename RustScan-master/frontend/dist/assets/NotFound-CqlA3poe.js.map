{"version": 3, "file": "NotFound-CqlA3poe.js", "sources": ["../../src/pages/NotFound.tsx"], "sourcesContent": ["import React from 'react';\nimport { Result, Button } from 'antd';\nimport { useNavigate } from 'react-router-dom';\n\nconst NotFound: React.FC = () => {\n  const navigate = useNavigate();\n\n  return (\n    <div style={{ \n      display: 'flex', \n      alignItems: 'center', \n      justifyContent: 'center', \n      minHeight: '60vh' \n    }}>\n      <Result\n        status=\"404\"\n        title=\"404\"\n        subTitle=\"抱歉，您访问的页面不存在。\"\n        extra={\n          <Button type=\"primary\" onClick={() => navigate('/dashboard')}>\n            返回首页\n          </Button>\n        }\n      />\n    </div>\n  );\n};\n\nexport default NotFound;"], "names": ["NotFound", "navigate", "useNavigate", "jsx", "Result", "<PERSON><PERSON>"], "mappings": "sJAIA,MAAMA,EAAqB,IAAM,CAC/B,MAAMC,EAAWC,EAAA,EAEjB,OACEC,EAAAA,IAAC,OAAI,MAAO,CACV,QAAS,OACT,WAAY,SACZ,eAAgB,SAChB,UAAW,MAAA,EAEX,SAAAA,EAAAA,IAACC,EAAA,CACC,OAAO,MACP,MAAM,MACN,SAAS,gBACT,MACED,EAAAA,IAACE,EAAA,CAAO,KAAK,UAAU,QAAS,IAAMJ,EAAS,YAAY,EAAG,SAAA,MAAA,CAE9D,CAAA,CAAA,EAGN,CAEJ"}