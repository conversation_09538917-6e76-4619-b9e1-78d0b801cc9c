import{u as L,a as W,b as B,c as Y,j as s,T as t,S as a,t as u}from"./index-BAEl3mLK.js";import{r as y}from"./vendor-o6zXO7vr.js";import{u as h}from"./useMutation-C9iXHWZW.js";import{c as H,T as O,S as k,B as c,j as A,$ as J,I as N,a0 as G,a1 as S,v as U,x as d,g as Q,y as V,P as X,f,Q as Z,a2 as z,a3 as ee,a4 as se,a5 as te,s as r}from"./ui-DUwPBEDa.js";import"./charts-DW2bYSvi.js";const{Title:ae,Text:i}=O,{Search:ne}=N,{Option:n}=S,he=()=>{var P;const T=L(),x=W(),{tasks:re,setTasks:ie}=B(),[p,D]=y.useState(""),[j,M]=y.useState("all"),[g,_]=y.useState("all"),{data:m,isLoading:v,refetch:b}=Y({queryKey:["tasks"],queryFn:()=>u.getTasks(1,100),refetchInterval:1e4}),C=h({mutationFn:u.startTask,onSuccess:()=>{r.success("任务已启动"),x.invalidateQueries({queryKey:["tasks"]})},onError:e=>{r.error("启动任务失败: "+e.message)}}),w=h({mutationFn:u.stopTask,onSuccess:()=>{r.success("任务已停止"),x.invalidateQueries({queryKey:["tasks"]})},onError:e=>{r.error("停止任务失败: "+e.message)}}),I=h({mutationFn:u.deleteTask,onSuccess:()=>{r.success("任务已删除"),x.invalidateQueries({queryKey:["tasks"]})},onError:e=>{r.error("删除任务失败: "+e.message)}}),F=h({mutationFn:u.restartTask,onSuccess:()=>{r.success("任务已重启"),x.invalidateQueries({queryKey:["tasks"]})},onError:e=>{r.error("重启任务失败: "+e.message)}}),R=(((P=m==null?void 0:m.data)==null?void 0:P.data)||[]).filter(e=>{const l=e.target.toLowerCase().includes(p.toLowerCase())||e.id.toLowerCase().includes(p.toLowerCase()),o=j==="all"||e.status===j,K=g==="all"||e.task_type===g;return l&&o&&K}),$=e=>{switch(e){case t.Pending:return"default";case t.Running:return"processing";case t.Completed:return"success";case t.Failed:return"error";case t.Stopped:return"warning";default:return"default"}},q=e=>{switch(e){case a.Quick:return"green";case a.Standard:return"blue";case a.Deep:return"purple";case a.WebFocused:return"orange";default:return"default"}},E=[{title:"任务ID",dataIndex:"id",key:"id",width:120,render:e=>s.jsx(i,{code:!0,style:{fontSize:12},children:e.slice(-8)})},{title:"扫描目标",dataIndex:"target",key:"target",ellipsis:!0,render:e=>s.jsx(d,{title:e,children:s.jsx(i,{children:e})})},{title:"扫描类型",dataIndex:"task_type",key:"task_type",width:100,render:e=>s.jsxs(Q,{color:q(e),children:[e===a.Quick&&"快速",e===a.Standard&&"标准",e===a.Deep&&"深度",e===a.WebFocused&&"Web",e===a.Custom&&"自定义"]})},{title:"状态",dataIndex:"status",key:"status",width:100,render:(e,l)=>s.jsxs(k,{children:[s.jsx(V,{status:e===t.Running?"processing":e===t.Completed?"success":e===t.Failed?"error":"default"}),s.jsxs(Q,{color:$(e),children:[e===t.Pending&&"等待中",e===t.Running&&"运行中",e===t.Completed&&"已完成",e===t.Failed&&"失败",e===t.Stopped&&"已停止"]})]})},{title:"进度",dataIndex:"progress",key:"progress",width:120,render:(e,l)=>s.jsx(X,{percent:e,size:"small",status:l.status===t.Failed?"exception":l.status===t.Completed?"success":"active"})},{title:"创建时间",dataIndex:"created_at",key:"created_at",width:160,render:e=>s.jsx(i,{children:f(e).format("YYYY-MM-DD HH:mm")})},{title:"耗时",key:"duration",width:100,render:e=>{if(!e.started_at)return s.jsx(i,{type:"secondary",children:"-"});const l=e.completed_at||new Date().toISOString(),o=f(l).diff(f(e.started_at),"second");return o<60?s.jsxs(i,{children:[o,"秒"]}):o<3600?s.jsxs(i,{children:[Math.floor(o/60),"分钟"]}):s.jsxs(i,{children:[Math.floor(o/3600),"小时"]})}},{title:"操作",key:"action",width:200,render:e=>s.jsxs(k,{size:"small",children:[s.jsx(d,{title:"查看详情",children:s.jsx(c,{type:"text",icon:s.jsx(Z,{}),size:"small",onClick:()=>T(`/tasks/${e.id}`)})}),e.status===t.Pending&&s.jsx(d,{title:"启动任务",children:s.jsx(c,{type:"text",icon:s.jsx(z,{}),size:"small",loading:C.isPending,onClick:()=>C.mutate(e.id)})}),e.status===t.Running&&s.jsx(d,{title:"停止任务",children:s.jsx(c,{type:"text",icon:s.jsx(ee,{}),size:"small",loading:w.isPending,onClick:()=>w.mutate(e.id)})}),(e.status===t.Failed||e.status===t.Stopped)&&s.jsx(d,{title:"重启任务",children:s.jsx(c,{type:"text",icon:s.jsx(z,{}),size:"small",loading:F.isPending,onClick:()=>F.mutate(e.id)})}),s.jsx(se,{title:"确定删除此任务？",description:"删除后将无法恢复任务数据",onConfirm:()=>I.mutate(e.id),okText:"确定",cancelText:"取消",children:s.jsx(d,{title:"删除任务",children:s.jsx(c,{type:"text",icon:s.jsx(te,{}),size:"small",danger:!0,loading:I.isPending})})})]})}];return s.jsx("div",{style:{padding:24},children:s.jsxs(H,{children:[s.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:24},children:[s.jsxs("div",{children:[s.jsx(ae,{level:3,style:{margin:0},children:"扫描任务"}),s.jsx(i,{type:"secondary",children:"管理和监控所有扫描任务"})]}),s.jsxs(k,{children:[s.jsx(c,{icon:s.jsx(A,{}),onClick:()=>b(),loading:v,children:"刷新"}),s.jsx(c,{type:"primary",icon:s.jsx(J,{}),onClick:()=>T("/tasks/create"),children:"创建任务"})]})]}),s.jsxs("div",{style:{display:"flex",gap:16,marginBottom:16,flexWrap:"wrap"},children:[s.jsx(ne,{placeholder:"搜索任务ID或目标",allowClear:!0,style:{width:300},value:p,onChange:e=>D(e.target.value),prefix:s.jsx(G,{})}),s.jsxs(S,{placeholder:"任务状态",style:{width:120},value:j,onChange:M,children:[s.jsx(n,{value:"all",children:"全部状态"}),s.jsx(n,{value:t.Pending,children:"等待中"}),s.jsx(n,{value:t.Running,children:"运行中"}),s.jsx(n,{value:t.Completed,children:"已完成"}),s.jsx(n,{value:t.Failed,children:"失败"}),s.jsx(n,{value:t.Stopped,children:"已停止"})]}),s.jsxs(S,{placeholder:"扫描类型",style:{width:120},value:g,onChange:_,children:[s.jsx(n,{value:"all",children:"全部类型"}),s.jsx(n,{value:a.Quick,children:"快速扫描"}),s.jsx(n,{value:a.Standard,children:"标准扫描"}),s.jsx(n,{value:a.Deep,children:"深度扫描"}),s.jsx(n,{value:a.WebFocused,children:"Web扫描"})]})]}),s.jsx(U,{columns:E,dataSource:R,rowKey:"id",loading:v,pagination:{total:R.length,pageSize:20,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 条记录`},scroll:{x:1200}})]})})};export{he as default};
//# sourceMappingURL=TaskList-DNdowEG0.js.map
