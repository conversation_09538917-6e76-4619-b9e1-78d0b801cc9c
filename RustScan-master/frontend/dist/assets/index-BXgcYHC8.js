import{q as B,v as r,j as e,w as j}from"./index-BAEl3mLK.js";import{r as i}from"./vendor-o6zXO7vr.js";import{F as l,R as L,C as p,c as u,k as g,A as Q,O as U,d as v,v as J,B as w,$ as K,U as N,I,a1 as R,a8 as G,s as o,g as _,S as H,an as W,a4 as X,a5 as Y,Q as Z}from"./ui-DUwPBEDa.js";import"./charts-DW2bYSvi.js";const{Option:S}=R,re=()=>{const[c,$]=i.useState([]),[b,k]=i.useState(!1),[A,x]=i.useState(!1),[d,C]=i.useState(null),[m]=l.useForm(),[n,F]=i.useState({current:1,pageSize:20,total:0}),{user:f}=B(),h=async(s=1,t=20)=>{k(!0);try{const a=await j.getUsers(s,t);a.data.success&&a.data.data&&($(a.data.data),F({current:s,pageSize:t,total:a.data.data.length}))}catch{o.error("获取用户列表失败")}finally{k(!1)}};i.useEffect(()=>{h()},[]);const D=()=>{C(null),m.resetFields(),x(!0)},T=s=>{C(s),m.setFieldsValue({username:s.username,email:s.email,role:s.role,is_active:s.is_active}),x(!0)},V=async s=>{try{await j.deleteUser(s),o.success("删除用户成功"),h(n.current,n.pageSize)}catch{o.error("删除用户失败")}},q=async()=>{var s,t;try{const a=await m.validateFields();if(d){const y={username:a.username,email:a.email,role:a.role,is_active:a.is_active};await j.updateUser(d.id,y),o.success("更新用户成功")}else{const y={username:a.username,email:a.email,password:a.password,role:a.role};await j.register(y),o.success("创建用户成功")}x(!1),h(n.current,n.pageSize)}catch(a){o.error(((t=(s=a.response)==null?void 0:s.data)==null?void 0:t.error)||"操作失败")}},E=s=>{const a={[r.Admin]:{color:"red",icon:e.jsx(U,{})},[r.User]:{color:"blue",icon:e.jsx(v,{})},[r.Viewer]:{color:"green",icon:e.jsx(Z,{})}}[s];return e.jsx(_,{color:a.color,icon:a.icon,children:s===r.Admin?"管理员":s===r.User?"用户":"观察者"})},M=[{title:"ID",dataIndex:"id",key:"id",width:80},{title:"用户名",dataIndex:"username",key:"username"},{title:"邮箱",dataIndex:"email",key:"email"},{title:"角色",dataIndex:"role",key:"role",render:s=>E(s)},{title:"状态",dataIndex:"is_active",key:"is_active",render:s=>e.jsx(_,{color:s?"green":"red",children:s?"活跃":"禁用"})},{title:"创建时间",dataIndex:"created_at",key:"created_at",render:s=>new Date(s).toLocaleString()},{title:"最后登录",dataIndex:"last_login",key:"last_login",render:s=>s?new Date(s).toLocaleString():"从未登录"},{title:"操作",key:"action",render:(s,t)=>e.jsxs(H,{children:[e.jsx(w,{type:"link",icon:e.jsx(W,{}),onClick:()=>T(t),children:"编辑"}),t.id!==(f==null?void 0:f.id)&&e.jsx(X,{title:"确定要删除这个用户吗？",onConfirm:()=>V(t.id),okText:"确定",cancelText:"取消",children:e.jsx(w,{type:"link",danger:!0,icon:e.jsx(Y,{}),children:"删除"})})]})}],O=c.filter(s=>s.role===r.Admin).length,z=c.filter(s=>s.role===r.User).length;c.filter(s=>s.role===r.Viewer).length;const P=c.filter(s=>s.is_active).length;return e.jsxs("div",{style:{padding:"24px"},children:[e.jsxs(L,{gutter:16,style:{marginBottom:"24px"},children:[e.jsx(p,{span:6,children:e.jsx(u,{children:e.jsx(g,{title:"总用户数",value:n.total,prefix:e.jsx(Q,{})})})}),e.jsx(p,{span:6,children:e.jsx(u,{children:e.jsx(g,{title:"管理员",value:O,prefix:e.jsx(U,{}),valueStyle:{color:"#cf1322"}})})}),e.jsx(p,{span:6,children:e.jsx(u,{children:e.jsx(g,{title:"普通用户",value:z,prefix:e.jsx(v,{}),valueStyle:{color:"#1890ff"}})})}),e.jsx(p,{span:6,children:e.jsx(u,{children:e.jsx(g,{title:"活跃用户",value:P,prefix:e.jsx(v,{}),valueStyle:{color:"#52c41a"}})})})]}),e.jsx(u,{title:"用户管理",extra:e.jsx(w,{type:"primary",icon:e.jsx(K,{}),onClick:D,children:"新建用户"}),children:e.jsx(J,{dataSource:c,columns:M,rowKey:"id",loading:b,pagination:{...n,showSizeChanger:!0,showQuickJumper:!0,showTotal:(s,t)=>`第 ${t[0]}-${t[1]} 条，共 ${s} 条`,onChange:(s,t)=>{h(s,t)}}})}),e.jsx(N,{title:d?"编辑用户":"新建用户",open:A,onOk:q,onCancel:()=>x(!1),destroyOnClose:!0,children:e.jsxs(l,{form:m,layout:"vertical",preserve:!1,children:[e.jsx(l.Item,{label:"用户名",name:"username",rules:[{required:!0,message:"请输入用户名"},{min:3,max:50,message:"用户名长度为3-50个字符"}],children:e.jsx(I,{placeholder:"请输入用户名"})}),e.jsx(l.Item,{label:"邮箱",name:"email",rules:[{required:!0,message:"请输入邮箱"},{type:"email",message:"请输入有效的邮箱地址"}],children:e.jsx(I,{placeholder:"请输入邮箱"})}),!d&&e.jsx(l.Item,{label:"密码",name:"password",rules:[{required:!0,message:"请输入密码"},{min:6,max:100,message:"密码长度为6-100个字符"}],children:e.jsx(I.Password,{placeholder:"请输入密码"})}),e.jsx(l.Item,{label:"角色",name:"role",rules:[{required:!0,message:"请选择角色"}],children:e.jsxs(R,{placeholder:"请选择角色",children:[e.jsx(S,{value:r.Viewer,children:"观察者"}),e.jsx(S,{value:r.User,children:"用户"}),e.jsx(S,{value:r.Admin,children:"管理员"})]})}),d&&e.jsx(l.Item,{label:"状态",name:"is_active",valuePropName:"checked",children:e.jsx(G,{checkedChildren:"活跃",unCheckedChildren:"禁用"})})]})})]})};export{re as default};
//# sourceMappingURL=index-BXgcYHC8.js.map
