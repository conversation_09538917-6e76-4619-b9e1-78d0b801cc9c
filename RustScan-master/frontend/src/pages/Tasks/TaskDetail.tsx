import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Typography,
  Spin,
  Row,
  Col,
  Progress,
  Tag,
  Button,
  Tabs,
  List,
  Timeline,
  Statistic,
  Alert,
  Space,
  Descriptions,
  Badge
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  DownloadOutlined,
  ArrowLeftOutlined,
  BugOutlined,
  GlobalOutlined,
  SecurityScanOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { useWebSocket } from '@/hooks/useWebSocket';
import { useTaskStore, useWebSocketStore } from '@/hooks/useStore';
import { api } from '@/api';
import type { Task, LogMessage } from '@/types';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

const TaskDetail: React.FC = () => {
  const { taskId } = useParams<{ taskId: string }>();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('overview');
  
  const { subscribeToTask, unsubscribeFromTask, connected } = useWebSocket();
  const { tasks, getTaskById } = useTaskStore();
  const { logs, connected: wsConnected } = useWebSocketStore();

  const task = getTaskById(taskId || '');
  const taskLogs = logs[taskId || ''] || [];

  // 获取任务详情
  const { data: taskData, isLoading, error, refetch } = useQuery({
    queryKey: ['task', taskId],
    queryFn: () => api.get(`/tasks/${taskId}`),
    enabled: !!taskId,
    refetchInterval: task?.status === 'running' ? 5000 : false,
  });

  // 获取任务结果
  const { data: resultsData } = useQuery({
    queryKey: ['task-results', taskId],
    queryFn: () => api.get(`/results?task_id=${taskId}`),
    enabled: !!taskId && task?.status === 'completed',
  });

  // WebSocket 订阅
  useEffect(() => {
    if (taskId && connected) {
      subscribeToTask(taskId);
      return () => unsubscribeFromTask(taskId);
    }
  }, [taskId, connected, subscribeToTask, unsubscribeFromTask]);

  if (isLoading) {
    return (
      <div style={{ padding: 24 }}>
        <Card>
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>
              正在加载任务详情...
            </div>
          </div>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: 24 }}>
        <Card>
          <Alert
            message="加载失败"
            description="无法加载任务详情，请检查任务ID是否正确。"
            type="error"
            showIcon
            action={
              <Button size="small" danger onClick={() => refetch()}>
                重试
              </Button>
            }
          />
        </Card>
      </div>
    );
  }

  const currentTask = taskData?.data || task;
  
  if (!currentTask) {
    return (
      <div style={{ padding: 24 }}>
        <Card>
          <Alert
            message="任务不存在"
            description="未找到指定的任务。"
            type="warning"
            showIcon
          />
        </Card>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'default';
      case 'running': return 'processing';
      case 'completed': return 'success';
      case 'failed': return 'error';
      case 'stopped': return 'warning';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return '等待中';
      case 'running': return '运行中';
      case 'completed': return '已完成';
      case 'failed': return '失败';
      case 'stopped': return '已停止';
      default: return status;
    }
  };

  const handleStopTask = async () => {
    try {
      await api.delete(`/tasks/${taskId}`);
      refetch();
    } catch (err) {
      console.error('停止任务失败:', err);
    }
  };

  const handleDownloadResults = () => {
    // 实现结果下载逻辑
    console.log('下载结果');
  };

  const renderOverview = () => (
    <Row gutter={[16, 16]}>
      <Col xs={24} lg={16}>
        <Card title="任务信息" style={{ marginBottom: 16 }}>
          <Descriptions column={2}>
            <Descriptions.Item label="任务ID">
              <Text copyable>{currentTask.id}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="目标">
              <Text strong>{currentTask.target}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="状态">
              <Badge status={getStatusColor(currentTask.status)} text={getStatusText(currentTask.status)} />
            </Descriptions.Item>
            <Descriptions.Item label="扫描类型">
              {currentTask.scan_type || '标准扫描'}
            </Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {dayjs(currentTask.created_at).format('YYYY-MM-DD HH:mm:ss')}
            </Descriptions.Item>
            <Descriptions.Item label="持续时间">
              {currentTask.updated_at ? 
                dayjs(currentTask.updated_at).diff(dayjs(currentTask.created_at), 'second') + '秒' : 
                '-'
              }
            </Descriptions.Item>
          </Descriptions>
        </Card>

        <Card title="扫描进度" style={{ marginBottom: 16 }}>
          <Progress
            percent={currentTask.progress || 0}
            status={currentTask.status === 'failed' ? 'exception' : 
                   currentTask.status === 'completed' ? 'success' : 'active'}
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
          />
          <div style={{ marginTop: 8 }}>
            <Text type="secondary">
              {currentTask.status === 'running' ? '正在执行扫描...' : 
               currentTask.status === 'completed' ? '扫描已完成' :
               currentTask.status === 'failed' ? '扫描失败' : '等待开始'}
            </Text>
          </div>
        </Card>

        {currentTask.error && (
          <Card title="错误信息" style={{ marginBottom: 16 }}>
            <Alert
              message="任务执行出错"
              description={currentTask.error}
              type="error"
              showIcon
            />
          </Card>
        )}
      </Col>

      <Col xs={24} lg={8}>
        <Card title="连接状态" style={{ marginBottom: 16 }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Badge 
                status={wsConnected ? 'success' : 'error'} 
                text={wsConnected ? 'WebSocket已连接' : 'WebSocket断开'} 
              />
            </div>
            <div>
              <Text type="secondary">
                实时日志: {wsConnected ? '启用' : '禁用'}
              </Text>
            </div>
          </Space>
        </Card>

        <Card title="操作" style={{ marginBottom: 16 }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            {currentTask.status === 'running' && (
              <Button 
                type="primary" 
                danger 
                icon={<PauseCircleOutlined />}
                onClick={handleStopTask}
                block
              >
                停止任务
              </Button>
            )}
            <Button 
              icon={<ReloadOutlined />}
              onClick={() => refetch()}
              block
            >
              刷新状态
            </Button>
            {currentTask.status === 'completed' && (
              <Button 
                type="primary" 
                icon={<DownloadOutlined />}
                onClick={handleDownloadResults}
                block
              >
                下载结果
              </Button>
            )}
          </Space>
        </Card>

        {resultsData?.data && (
          <Card title="扫描统计" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <Statistic 
                  title="发现主机" 
                  value={resultsData.data.hosts?.length || 0} 
                  prefix={<GlobalOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="开放端口" 
                  value={resultsData.data.ports?.length || 0} 
                  prefix={<SecurityScanOutlined />}
                />
              </Col>
              <Col span={12} style={{ marginTop: 16 }}>
                <Statistic 
                  title="发现漏洞" 
                  value={resultsData.data.vulnerabilities?.length || 0} 
                  prefix={<BugOutlined />}
                  valueStyle={{ color: resultsData.data.vulnerabilities?.length > 0 ? '#cf1322' : '#3f8600' }}
                />
              </Col>
              <Col span={12} style={{ marginTop: 16 }}>
                <Statistic 
                  title="子域名" 
                  value={resultsData.data.subdomains?.length || 0} 
                  prefix={<GlobalOutlined />}
                />
              </Col>
            </Row>
          </Card>
        )}
      </Col>
    </Row>
  );

  const renderLogs = () => (
    <Card title="实时日志" style={{ height: '600px', overflow: 'hidden' }}>
      <div style={{ height: '520px', overflow: 'auto', padding: '8px 0' }}>
        {taskLogs.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
            <ClockCircleOutlined style={{ fontSize: 24, marginBottom: 8 }} />
            <div>暂无日志数据</div>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {wsConnected ? '等待任务产生日志...' : '请检查WebSocket连接'}
            </Text>
          </div>
        ) : (
          <Timeline mode="left" style={{ paddingTop: 16 }}>
            {taskLogs.map((log: LogMessage, index: number) => (
              <Timeline.Item
                key={index}
                color={log.level === 'error' ? 'red' : log.level === 'warn' ? 'orange' : 'blue'}
              >
                <div style={{ marginBottom: 8 }}>
                  <Tag color={log.level === 'error' ? 'red' : log.level === 'warn' ? 'orange' : 'blue'}>
                    {log.level.toUpperCase()}
                  </Tag>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    {dayjs(log.timestamp).format('HH:mm:ss')}
                  </Text>
                </div>
                <div style={{ wordBreak: 'break-all' }}>
                  {log.message}
                </div>
              </Timeline.Item>
            ))}
          </Timeline>
        )}
      </div>
    </Card>
  );

  const renderResults = () => (
    <div>
      {resultsData?.data ? (
        <Row gutter={[16, 16]}>
          {resultsData.data.hosts && (
            <Col xs={24} lg={12}>
              <Card title={`发现主机 (${resultsData.data.hosts.length})`}>
                <List
                  size="small"
                  dataSource={resultsData.data.hosts}
                  renderItem={(host: any) => (
                    <List.Item>
                      <List.Item.Meta
                        title={host.ip_address}
                        description={`状态: ${host.status} | 系统: ${host.os_name || '未知'}`}
                      />
                    </List.Item>
                  )}
                  style={{ maxHeight: 400, overflow: 'auto' }}
                />
              </Card>
            </Col>
          )}
          
          {resultsData.data.ports && (
            <Col xs={24} lg={12}>
              <Card title={`开放端口 (${resultsData.data.ports.length})`}>
                <List
                  size="small"
                  dataSource={resultsData.data.ports}
                  renderItem={(port: any) => (
                    <List.Item>
                      <List.Item.Meta
                        title={`${port.port}/${port.protocol}`}
                        description={`服务: ${port.service || '未知'} | 状态: ${port.state}`}
                      />
                    </List.Item>
                  )}
                  style={{ maxHeight: 400, overflow: 'auto' }}
                />
              </Card>
            </Col>
          )}

          {resultsData.data.vulnerabilities && resultsData.data.vulnerabilities.length > 0 && (
            <Col xs={24}>
              <Card title={`发现漏洞 (${resultsData.data.vulnerabilities.length})`}>
                <List
                  dataSource={resultsData.data.vulnerabilities}
                  renderItem={(vuln: any) => (
                    <List.Item>
                      <List.Item.Meta
                        title={
                          <span>
                            <Tag color={vuln.severity === 'critical' ? 'red' : 
                                      vuln.severity === 'high' ? 'orange' : 
                                      vuln.severity === 'medium' ? 'gold' : 'green'}>
                              {vuln.severity?.toUpperCase()}
                            </Tag>
                            {vuln.name}
                          </span>
                        }
                        description={vuln.description}
                      />
                    </List.Item>
                  )}
                  style={{ maxHeight: 400, overflow: 'auto' }}
                />
              </Card>
            </Col>
          )}
        </Row>
      ) : (
        <Card>
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Text type="secondary">暂无扫描结果</Text>
          </div>
        </Card>
      )}
    </div>
  );

  return (
    <div style={{ padding: 24 }}>
      <div style={{ marginBottom: 16 }}>
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={() => navigate('/tasks')}
          style={{ marginRight: 16 }}
        >
          返回任务列表
        </Button>
        <Title level={3} style={{ display: 'inline-block', margin: 0 }}>
          任务详情 - {currentTask.target}
        </Title>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'overview',
            label: '概览',
            children: renderOverview(),
          },
          {
            key: 'logs',
            label: '实时日志',
            children: renderLogs(),
          },
          {
            key: 'results',
            label: '扫描结果',
            children: renderResults(),
          },
        ]}
      />
    </div>
  );
};

export default TaskDetail;