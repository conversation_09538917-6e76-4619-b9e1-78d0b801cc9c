{"version": 3, "file": "TaskDetail-hk41wc_h.js", "sources": ["../../src/pages/Tasks/TaskDetail.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport {\n  Card,\n  Typography,\n  Spin,\n  Row,\n  Col,\n  Progress,\n  Tag,\n  Button,\n  Tabs,\n  List,\n  Timeline,\n  Statistic,\n  Alert,\n  Space,\n  Descriptions,\n  Badge\n} from 'antd';\nimport {\n  PlayCircleOutlined,\n  PauseCircleOutlined,\n  ReloadOutlined,\n  DownloadOutlined,\n  ArrowLeftOutlined,\n  BugOutlined,\n  GlobalOutlined,\n  SecurityScanOutlined,\n  ClockCircleOutlined\n} from '@ant-design/icons';\nimport { useQuery } from '@tanstack/react-query';\nimport { useWebSocket } from '@/hooks/useWebSocket';\nimport { useTaskStore, useWebSocketStore } from '@/hooks/useStore';\nimport { api } from '@/api';\nimport type { Task, LogMessage } from '@/types';\nimport { TaskStatus } from '@/types';\nimport dayjs from 'dayjs';\n\nconst { Title, Text } = Typography;\n\nconst TaskDetail: React.FC = () => {\n  const { taskId } = useParams<{ taskId: string }>();\n  const navigate = useNavigate();\n  const [activeTab, setActiveTab] = useState('overview');\n  \n  const { subscribeToTask, unsubscribeFromTask, connected } = useWebSocket();\n  const { tasks, getTaskById } = useTaskStore();\n  const { logs, connected: wsConnected } = useWebSocketStore();\n\n  const task = getTaskById(taskId || '');\n  const taskLogs = logs[taskId || ''] || [];\n\n  // 获取任务详情\n  const { data: taskData, isLoading, error, refetch } = useQuery({\n    queryKey: ['task', taskId],\n    queryFn: () => api.get(`/tasks/${taskId}`),\n    enabled: !!taskId,\n    refetchInterval: task?.status === TaskStatus.Running ? 5000 : false,\n  });\n\n  // 获取任务结果\n  const { data: resultsData } = useQuery({\n    queryKey: ['task-results', taskId],\n    queryFn: () => api.get(`/results?task_id=${taskId}`),\n    enabled: !!taskId && task?.status === TaskStatus.Completed,\n  });\n\n  // WebSocket 订阅\n  useEffect(() => {\n    if (taskId && connected) {\n      subscribeToTask(taskId);\n      return () => {\n        unsubscribeFromTask(taskId);\n      };\n    }\n    return undefined;\n  }, [taskId, connected, subscribeToTask, unsubscribeFromTask]);\n\n  if (isLoading) {\n    return (\n      <div style={{ padding: 24 }}>\n        <Card>\n          <div style={{ textAlign: 'center', padding: '40px 0' }}>\n            <Spin size=\"large\" />\n            <div style={{ marginTop: 16 }}>\n              正在加载任务详情...\n            </div>\n          </div>\n        </Card>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div style={{ padding: 24 }}>\n        <Card>\n          <Alert\n            message=\"加载失败\"\n            description=\"无法加载任务详情，请检查任务ID是否正确。\"\n            type=\"error\"\n            showIcon\n            action={\n              <Button size=\"small\" danger onClick={() => refetch()}>\n                重试\n              </Button>\n            }\n          />\n        </Card>\n      </div>\n    );\n  }\n\n  const currentTask = taskData?.data || task;\n  \n  if (!currentTask) {\n    return (\n      <div style={{ padding: 24 }}>\n        <Card>\n          <Alert\n            message=\"任务不存在\"\n            description=\"未找到指定的任务。\"\n            type=\"warning\"\n            showIcon\n          />\n        </Card>\n      </div>\n    );\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'pending': return 'default';\n      case 'running': return 'processing';\n      case 'completed': return 'success';\n      case 'failed': return 'error';\n      case 'stopped': return 'warning';\n      default: return 'default';\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'pending': return '等待中';\n      case 'running': return '运行中';\n      case 'completed': return '已完成';\n      case 'failed': return '失败';\n      case 'stopped': return '已停止';\n      default: return status;\n    }\n  };\n\n  const handleStopTask = async () => {\n    try {\n      await api.delete(`/tasks/${taskId}`);\n      refetch();\n    } catch (err) {\n      console.error('停止任务失败:', err);\n    }\n  };\n\n  const handleDownloadResults = () => {\n    // 实现结果下载逻辑\n    console.log('下载结果');\n  };\n\n  const renderOverview = () => (\n    <Row gutter={[16, 16]}>\n      <Col xs={24} lg={16}>\n        <Card title=\"任务信息\" style={{ marginBottom: 16 }}>\n          <Descriptions column={2}>\n            <Descriptions.Item label=\"任务ID\">\n              <Text copyable>{currentTask.id}</Text>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"目标\">\n              <Text strong>{currentTask.target}</Text>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"状态\">\n              <Badge status={getStatusColor(currentTask.status)} text={getStatusText(currentTask.status)} />\n            </Descriptions.Item>\n            <Descriptions.Item label=\"扫描类型\">\n              {currentTask.scan_type || '标准扫描'}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"创建时间\">\n              {dayjs(currentTask.created_at).format('YYYY-MM-DD HH:mm:ss')}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"持续时间\">\n              {currentTask.updated_at ? \n                dayjs(currentTask.updated_at).diff(dayjs(currentTask.created_at), 'second') + '秒' : \n                '-'\n              }\n            </Descriptions.Item>\n          </Descriptions>\n        </Card>\n\n        <Card title=\"扫描进度\" style={{ marginBottom: 16 }}>\n          <Progress\n            percent={currentTask.progress || 0}\n            status={currentTask.status === TaskStatus.Failed ? 'exception' :\n                   currentTask.status === TaskStatus.Completed ? 'success' : 'active'}\n            strokeColor={{\n              '0%': '#108ee9',\n              '100%': '#87d068',\n            }}\n          />\n          <div style={{ marginTop: 8 }}>\n            <Text type=\"secondary\">\n              {currentTask.status === TaskStatus.Running ? '正在执行扫描...' :\n               currentTask.status === TaskStatus.Completed ? '扫描已完成' :\n               currentTask.status === TaskStatus.Failed ? '扫描失败' : '等待开始'}\n            </Text>\n          </div>\n        </Card>\n\n        {currentTask.error && (\n          <Card title=\"错误信息\" style={{ marginBottom: 16 }}>\n            <Alert\n              message=\"任务执行出错\"\n              description={currentTask.error}\n              type=\"error\"\n              showIcon\n            />\n          </Card>\n        )}\n      </Col>\n\n      <Col xs={24} lg={8}>\n        <Card title=\"连接状态\" style={{ marginBottom: 16 }}>\n          <Space direction=\"vertical\" style={{ width: '100%' }}>\n            <div>\n              <Badge \n                status={wsConnected ? 'success' : 'error'} \n                text={wsConnected ? 'WebSocket已连接' : 'WebSocket断开'} \n              />\n            </div>\n            <div>\n              <Text type=\"secondary\">\n                实时日志: {wsConnected ? '启用' : '禁用'}\n              </Text>\n            </div>\n          </Space>\n        </Card>\n\n        <Card title=\"操作\" style={{ marginBottom: 16 }}>\n          <Space direction=\"vertical\" style={{ width: '100%' }}>\n            {currentTask.status === TaskStatus.Running && (\n              <Button \n                type=\"primary\" \n                danger \n                icon={<PauseCircleOutlined />}\n                onClick={handleStopTask}\n                block\n              >\n                停止任务\n              </Button>\n            )}\n            <Button \n              icon={<ReloadOutlined />}\n              onClick={() => refetch()}\n              block\n            >\n              刷新状态\n            </Button>\n            {currentTask.status === TaskStatus.Completed && (\n              <Button \n                type=\"primary\" \n                icon={<DownloadOutlined />}\n                onClick={handleDownloadResults}\n                block\n              >\n                下载结果\n              </Button>\n            )}\n          </Space>\n        </Card>\n\n        {resultsData?.data && (\n          <Card title=\"扫描统计\" style={{ marginBottom: 16 }}>\n            <Row gutter={16}>\n              <Col span={12}>\n                <Statistic \n                  title=\"发现主机\" \n                  value={resultsData.data.hosts?.length || 0} \n                  prefix={<GlobalOutlined />}\n                />\n              </Col>\n              <Col span={12}>\n                <Statistic \n                  title=\"开放端口\" \n                  value={resultsData.data.ports?.length || 0} \n                  prefix={<SecurityScanOutlined />}\n                />\n              </Col>\n              <Col span={12} style={{ marginTop: 16 }}>\n                <Statistic \n                  title=\"发现漏洞\" \n                  value={resultsData.data.vulnerabilities?.length || 0} \n                  prefix={<BugOutlined />}\n                  valueStyle={{ color: resultsData.data.vulnerabilities?.length > 0 ? '#cf1322' : '#3f8600' }}\n                />\n              </Col>\n              <Col span={12} style={{ marginTop: 16 }}>\n                <Statistic \n                  title=\"子域名\" \n                  value={resultsData.data.subdomains?.length || 0} \n                  prefix={<GlobalOutlined />}\n                />\n              </Col>\n            </Row>\n          </Card>\n        )}\n      </Col>\n    </Row>\n  );\n\n  const renderLogs = () => (\n    <Card title=\"实时日志\" style={{ height: '600px', overflow: 'hidden' }}>\n      <div style={{ height: '520px', overflow: 'auto', padding: '8px 0' }}>\n        {taskLogs.length === 0 ? (\n          <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>\n            <ClockCircleOutlined style={{ fontSize: 24, marginBottom: 8 }} />\n            <div>暂无日志数据</div>\n            <Text type=\"secondary\" style={{ fontSize: 12 }}>\n              {wsConnected ? '等待任务产生日志...' : '请检查WebSocket连接'}\n            </Text>\n          </div>\n        ) : (\n          <Timeline mode=\"left\" style={{ paddingTop: 16 }}>\n            {taskLogs.map((log: LogMessage, index: number) => (\n              <Timeline.Item\n                key={index}\n                color={log.level === 'error' ? 'red' : log.level === 'warn' ? 'orange' : 'blue'}\n              >\n                <div style={{ marginBottom: 8 }}>\n                  <Tag color={log.level === 'error' ? 'red' : log.level === 'warn' ? 'orange' : 'blue'}>\n                    {log.level.toUpperCase()}\n                  </Tag>\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    {dayjs(log.timestamp).format('HH:mm:ss')}\n                  </Text>\n                </div>\n                <div style={{ wordBreak: 'break-all' }}>\n                  {log.message}\n                </div>\n              </Timeline.Item>\n            ))}\n          </Timeline>\n        )}\n      </div>\n    </Card>\n  );\n\n  const renderResults = () => (\n    <div>\n      {resultsData?.data ? (\n        <Row gutter={[16, 16]}>\n          {resultsData.data.hosts && (\n            <Col xs={24} lg={12}>\n              <Card title={`发现主机 (${resultsData.data.hosts.length})`}>\n                <List\n                  size=\"small\"\n                  dataSource={resultsData.data.hosts}\n                  renderItem={(host: any) => (\n                    <List.Item>\n                      <List.Item.Meta\n                        title={host.ip_address}\n                        description={`状态: ${host.status} | 系统: ${host.os_name || '未知'}`}\n                      />\n                    </List.Item>\n                  )}\n                  style={{ maxHeight: 400, overflow: 'auto' }}\n                />\n              </Card>\n            </Col>\n          )}\n          \n          {resultsData.data.ports && (\n            <Col xs={24} lg={12}>\n              <Card title={`开放端口 (${resultsData.data.ports.length})`}>\n                <List\n                  size=\"small\"\n                  dataSource={resultsData.data.ports}\n                  renderItem={(port: any) => (\n                    <List.Item>\n                      <List.Item.Meta\n                        title={`${port.port}/${port.protocol}`}\n                        description={`服务: ${port.service || '未知'} | 状态: ${port.state}`}\n                      />\n                    </List.Item>\n                  )}\n                  style={{ maxHeight: 400, overflow: 'auto' }}\n                />\n              </Card>\n            </Col>\n          )}\n\n          {resultsData.data.vulnerabilities && resultsData.data.vulnerabilities.length > 0 && (\n            <Col xs={24}>\n              <Card title={`发现漏洞 (${resultsData.data.vulnerabilities.length})`}>\n                <List\n                  dataSource={resultsData.data.vulnerabilities}\n                  renderItem={(vuln: any) => (\n                    <List.Item>\n                      <List.Item.Meta\n                        title={\n                          <span>\n                            <Tag color={vuln.severity === 'critical' ? 'red' : \n                                      vuln.severity === 'high' ? 'orange' : \n                                      vuln.severity === 'medium' ? 'gold' : 'green'}>\n                              {vuln.severity?.toUpperCase()}\n                            </Tag>\n                            {vuln.name}\n                          </span>\n                        }\n                        description={vuln.description}\n                      />\n                    </List.Item>\n                  )}\n                  style={{ maxHeight: 400, overflow: 'auto' }}\n                />\n              </Card>\n            </Col>\n          )}\n        </Row>\n      ) : (\n        <Card>\n          <div style={{ textAlign: 'center', padding: '40px 0' }}>\n            <Text type=\"secondary\">暂无扫描结果</Text>\n          </div>\n        </Card>\n      )}\n    </div>\n  );\n\n  return (\n    <div style={{ padding: 24 }}>\n      <div style={{ marginBottom: 16 }}>\n        <Button \n          icon={<ArrowLeftOutlined />} \n          onClick={() => navigate('/tasks')}\n          style={{ marginRight: 16 }}\n        >\n          返回任务列表\n        </Button>\n        <Title level={3} style={{ display: 'inline-block', margin: 0 }}>\n          任务详情 - {currentTask.target}\n        </Title>\n      </div>\n\n      <Tabs\n        activeKey={activeTab}\n        onChange={setActiveTab}\n        items={[\n          {\n            key: 'overview',\n            label: '概览',\n            children: renderOverview(),\n          },\n          {\n            key: 'logs',\n            label: '实时日志',\n            children: renderLogs(),\n          },\n          {\n            key: 'results',\n            label: '扫描结果',\n            children: renderResults(),\n          },\n        ]}\n      />\n    </div>\n  );\n};\n\nexport default TaskDetail;"], "names": ["Title", "Text", "Typography", "TaskDetail", "taskId", "useParams", "navigate", "useNavigate", "activeTab", "setActiveTab", "useState", "subscribeToTask", "unsubscribeFromTask", "connected", "useWebSocket", "tasks", "getTaskById", "useTaskStore", "logs", "wsConnected", "useWebSocketStore", "task", "taskLogs", "taskData", "isLoading", "error", "refetch", "useQuery", "api", "TaskStatus", "resultsData", "useEffect", "jsx", "Card", "jsxs", "Spin", "<PERSON><PERSON>", "<PERSON><PERSON>", "currentTask", "getStatusColor", "status", "getStatusText", "handleStopTask", "err", "handleDownloadResults", "renderOverview", "Row", "Col", "Descriptions", "Badge", "dayjs", "Progress", "Space", "PauseCircleOutlined", "ReloadOutlined", "DownloadOutlined", "Statistic", "_a", "GlobalOutlined", "_b", "SecurityScanOutlined", "_c", "BugOutlined", "_d", "_e", "renderLogs", "ClockCircleOutlined", "Timeline", "log", "index", "Tag", "renderResults", "List", "host", "port", "vuln", "ArrowLeftOutlined", "Tabs"], "mappings": "uYAuCA,KAAM,CAAE,MAAAA,GAAO,KAAAC,CAAA,EAASC,GAElBC,GAAuB,IAAM,CACjC,KAAM,CAAE,OAAAC,CAAA,EAAWC,EAAA,EACbC,EAAWC,EAAA,EACX,CAACC,EAAWC,CAAY,EAAIC,EAAAA,SAAS,UAAU,EAE/C,CAAE,gBAAAC,EAAiB,oBAAAC,EAAqB,UAAAC,CAAA,EAAcC,EAAA,EACtD,CAAE,MAAAC,GAAO,YAAAC,CAAA,EAAgBC,EAAA,EACzB,CAAE,KAAAC,EAAM,UAAWC,CAAA,EAAgBC,GAAA,EAEnCC,EAAOL,EAAYZ,GAAU,EAAE,EAC/BkB,EAAWJ,EAAKd,GAAU,EAAE,GAAK,CAAA,EAGjC,CAAE,KAAMmB,EAAU,UAAAC,EAAW,MAAAC,EAAO,QAAAC,CAAA,EAAYC,EAAS,CAC7D,SAAU,CAAC,OAAQvB,CAAM,EACzB,QAAS,IAAMwB,EAAI,IAAI,UAAUxB,CAAM,EAAE,EACzC,QAAS,CAAC,CAACA,EACX,iBAAiBiB,GAAA,YAAAA,EAAM,UAAWQ,EAAW,QAAU,IAAO,EAAA,CAC/D,EAGK,CAAE,KAAMC,CAAA,EAAgBH,EAAS,CACrC,SAAU,CAAC,eAAgBvB,CAAM,EACjC,QAAS,IAAMwB,EAAI,IAAI,oBAAoBxB,CAAM,EAAE,EACnD,QAAS,CAAC,CAACA,IAAUiB,GAAA,YAAAA,EAAM,UAAWQ,EAAW,SAAA,CAClD,EAaD,GAVAE,EAAAA,UAAU,IAAM,CACd,GAAI3B,GAAUS,EACZ,OAAAF,EAAgBP,CAAM,EACf,IAAM,CACXQ,EAAoBR,CAAM,CAC5B,CAGJ,EAAG,CAACA,EAAQS,EAAWF,EAAiBC,CAAmB,CAAC,EAExDY,EACF,aACG,MAAA,CAAI,MAAO,CAAE,QAAS,IACrB,SAAAQ,EAAAA,IAACC,EAAA,CACC,SAAAC,EAAAA,KAAC,OAAI,MAAO,CAAE,UAAW,SAAU,QAAS,UAC1C,SAAA,CAAAF,EAAAA,IAACG,GAAA,CAAK,KAAK,OAAA,CAAQ,QAClB,MAAA,CAAI,MAAO,CAAE,UAAW,EAAA,EAAM,SAAA,aAAA,CAE/B,CAAA,CAAA,CACF,EACF,EACF,EAIJ,GAAIV,EACF,OACEO,MAAC,OAAI,MAAO,CAAE,QAAS,EAAA,EACrB,eAACC,EAAA,CACC,SAAAD,EAAAA,IAACI,EAAA,CACC,QAAQ,OACR,YAAY,wBACZ,KAAK,QACL,SAAQ,GACR,OACEJ,EAAAA,IAACK,EAAA,CAAO,KAAK,QAAQ,OAAM,GAAC,QAAS,IAAMX,IAAW,SAAA,IAAA,CAEtD,CAAA,CAAA,EAGN,CAAA,CACF,EAIJ,MAAMY,GAAcf,GAAA,YAAAA,EAAU,OAAQF,EAEtC,GAAI,CAACiB,EACH,OACEN,MAAC,OAAI,MAAO,CAAE,QAAS,EAAA,EACrB,eAACC,EAAA,CACC,SAAAD,EAAAA,IAACI,EAAA,CACC,QAAQ,QACR,YAAY,YACZ,KAAK,UACL,SAAQ,EAAA,CAAA,EAEZ,CAAA,CACF,EAIJ,MAAMG,EAAkBC,GAAmB,CACzC,OAAQA,EAAA,CACN,IAAK,UAAW,MAAO,UACvB,IAAK,UAAW,MAAO,aACvB,IAAK,YAAa,MAAO,UACzB,IAAK,SAAU,MAAO,QACtB,IAAK,UAAW,MAAO,UACvB,QAAS,MAAO,SAAA,CAEpB,EAEMC,EAAiBD,GAAmB,CACxC,OAAQA,EAAA,CACN,IAAK,UAAW,MAAO,MACvB,IAAK,UAAW,MAAO,MACvB,IAAK,YAAa,MAAO,MACzB,IAAK,SAAU,MAAO,KACtB,IAAK,UAAW,MAAO,MACvB,QAAS,OAAOA,CAAA,CAEpB,EAEME,EAAiB,SAAY,CACjC,GAAI,CACF,MAAMd,EAAI,OAAO,UAAUxB,CAAM,EAAE,EACnCsB,EAAA,CACF,OAASiB,EAAK,CACZ,QAAQ,MAAM,UAAWA,CAAG,CAC9B,CACF,EAEMC,EAAwB,IAAM,CAElC,QAAQ,IAAI,MAAM,CACpB,EAEMC,EAAiB,mBACrBX,OAAAA,EAAAA,KAACY,EAAA,CAAI,OAAQ,CAAC,GAAI,EAAE,EAClB,SAAA,CAAAZ,EAAAA,KAACa,EAAA,CAAI,GAAI,GAAI,GAAI,GACf,SAAA,CAAAf,EAAAA,IAACC,EAAA,CAAK,MAAM,OAAO,MAAO,CAAE,aAAc,EAAA,EACxC,SAAAC,EAAAA,KAACc,EAAA,CAAa,OAAQ,EACpB,SAAA,CAAAhB,EAAAA,IAACgB,EAAa,KAAb,CAAkB,MAAM,OACvB,SAAAhB,EAAAA,IAAC/B,EAAA,CAAK,SAAQ,GAAE,SAAAqC,EAAY,EAAA,CAAG,EACjC,EACAN,EAAAA,IAACgB,EAAa,KAAb,CAAkB,MAAM,KACvB,SAAAhB,EAAAA,IAAC/B,EAAA,CAAK,OAAM,GAAE,SAAAqC,EAAY,MAAA,CAAO,EACnC,QACCU,EAAa,KAAb,CAAkB,MAAM,KACvB,eAACC,EAAA,CAAM,OAAQV,EAAeD,EAAY,MAAM,EAAG,KAAMG,EAAcH,EAAY,MAAM,EAAG,EAC9F,EACAN,MAACgB,EAAa,KAAb,CAAkB,MAAM,OACtB,SAAAV,EAAY,WAAa,OAC5B,EACAN,EAAAA,IAACgB,EAAa,KAAb,CAAkB,MAAM,OACtB,SAAAE,EAAMZ,EAAY,UAAU,EAAE,OAAO,qBAAqB,CAAA,CAC7D,EACAN,MAACgB,EAAa,KAAb,CAAkB,MAAM,OACtB,SAAAV,EAAY,WACXY,EAAMZ,EAAY,UAAU,EAAE,KAAKY,EAAMZ,EAAY,UAAU,EAAG,QAAQ,EAAI,IAC9E,GAAA,CAEJ,CAAA,CAAA,CACF,CAAA,CACF,EAEAJ,OAACD,GAAK,MAAM,OAAO,MAAO,CAAE,aAAc,IACxC,SAAA,CAAAD,EAAAA,IAACmB,GAAA,CACC,QAASb,EAAY,UAAY,EACjC,OAAQA,EAAY,SAAWT,EAAW,OAAS,YAC5CS,EAAY,SAAWT,EAAW,UAAY,UAAY,SACjE,YAAa,CACX,KAAM,UACN,OAAQ,SAAA,CACV,CAAA,EAEFG,EAAAA,IAAC,MAAA,CAAI,MAAO,CAAE,UAAW,CAAA,EACvB,SAAAA,EAAAA,IAAC/B,EAAA,CAAK,KAAK,YACR,SAAAqC,EAAY,SAAWT,EAAW,QAAU,YAC5CS,EAAY,SAAWT,EAAW,UAAY,QAC9CS,EAAY,SAAWT,EAAW,OAAS,OAAS,MAAA,CACvD,CAAA,CACF,CAAA,EACF,EAECS,EAAY,OACXN,MAACC,EAAA,CAAK,MAAM,OAAO,MAAO,CAAE,aAAc,EAAA,EACxC,SAAAD,EAAAA,IAACI,EAAA,CACC,QAAQ,SACR,YAAaE,EAAY,MACzB,KAAK,QACL,SAAQ,EAAA,CAAA,CACV,CACF,CAAA,EAEJ,EAEAJ,EAAAA,KAACa,EAAA,CAAI,GAAI,GAAI,GAAI,EACf,SAAA,CAAAf,MAACC,GAAK,MAAM,OAAO,MAAO,CAAE,aAAc,IACxC,SAAAC,EAAAA,KAACkB,EAAA,CAAM,UAAU,WAAW,MAAO,CAAE,MAAO,QAC1C,SAAA,CAAApB,MAAC,MAAA,CACC,SAAAA,EAAAA,IAACiB,EAAA,CACC,OAAQ9B,EAAc,UAAY,QAClC,KAAMA,EAAc,eAAiB,aAAA,CAAA,EAEzC,EACAa,MAAC,MAAA,CACC,SAAAE,EAAAA,KAACjC,EAAA,CAAK,KAAK,YAAY,SAAA,CAAA,SACdkB,EAAc,KAAO,IAAA,CAAA,CAC9B,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CACF,QAECc,EAAA,CAAK,MAAM,KAAK,MAAO,CAAE,aAAc,IACtC,SAAAC,EAAAA,KAACkB,GAAM,UAAU,WAAW,MAAO,CAAE,MAAO,QACzC,SAAA,CAAAd,EAAY,SAAWT,EAAW,SACjCG,EAAAA,IAACK,EAAA,CACC,KAAK,UACL,OAAM,GACN,WAAOgB,GAAA,EAAoB,EAC3B,QAASX,EACT,MAAK,GACN,SAAA,MAAA,CAAA,EAIHV,EAAAA,IAACK,EAAA,CACC,WAAOiB,GAAA,EAAe,EACtB,QAAS,IAAM5B,EAAA,EACf,MAAK,GACN,SAAA,MAAA,CAAA,EAGAY,EAAY,SAAWT,EAAW,WACjCG,EAAAA,IAACK,EAAA,CACC,KAAK,UACL,WAAOkB,GAAA,EAAiB,EACxB,QAASX,EACT,MAAK,GACN,SAAA,MAAA,CAAA,CAED,CAAA,CAEJ,CAAA,CACF,GAECd,GAAA,YAAAA,EAAa,OACZE,MAACC,EAAA,CAAK,MAAM,OAAO,MAAO,CAAE,aAAc,EAAA,EACxC,SAAAC,EAAAA,KAACY,EAAA,CAAI,OAAQ,GACX,SAAA,CAAAd,EAAAA,IAACe,EAAA,CAAI,KAAM,GACT,SAAAf,EAAAA,IAACwB,EAAA,CACC,MAAM,OACN,QAAOC,EAAA3B,EAAY,KAAK,QAAjB,YAAA2B,EAAwB,SAAU,EACzC,aAASC,EAAA,CAAA,CAAe,CAAA,CAAA,EAE5B,EACA1B,EAAAA,IAACe,EAAA,CAAI,KAAM,GACT,SAAAf,EAAAA,IAACwB,EAAA,CACC,MAAM,OACN,QAAOG,EAAA7B,EAAY,KAAK,QAAjB,YAAA6B,EAAwB,SAAU,EACzC,aAASC,GAAA,CAAA,CAAqB,CAAA,CAAA,EAElC,EACA5B,MAACe,GAAI,KAAM,GAAI,MAAO,CAAE,UAAW,IACjC,SAAAf,EAAAA,IAACwB,EAAA,CACC,MAAM,OACN,QAAOK,EAAA/B,EAAY,KAAK,kBAAjB,YAAA+B,EAAkC,SAAU,EACnD,aAASC,GAAA,EAAY,EACrB,WAAY,CAAE,QAAOC,EAAAjC,EAAY,KAAK,kBAAjB,YAAAiC,EAAkC,QAAS,EAAI,UAAY,SAAA,CAAU,CAAA,EAE9F,EACA/B,MAACe,GAAI,KAAM,GAAI,MAAO,CAAE,UAAW,IACjC,SAAAf,EAAAA,IAACwB,EAAA,CACC,MAAM,MACN,QAAOQ,EAAAlC,EAAY,KAAK,aAAjB,YAAAkC,EAA6B,SAAU,EAC9C,aAASN,EAAA,CAAA,CAAe,CAAA,CAAA,CAC1B,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CAEJ,CAAA,EACF,GAGIO,EAAa,IACjBjC,EAAAA,IAACC,EAAA,CAAK,MAAM,OAAO,MAAO,CAAE,OAAQ,QAAS,SAAU,QAAA,EACrD,SAAAD,EAAAA,IAAC,MAAA,CAAI,MAAO,CAAE,OAAQ,QAAS,SAAU,OAAQ,QAAS,SACvD,SAAAV,EAAS,SAAW,EACnBY,OAAC,MAAA,CAAI,MAAO,CAAE,UAAW,SAAU,QAAS,SAAU,MAAO,QAC3D,SAAA,CAAAF,MAACkC,IAAoB,MAAO,CAAE,SAAU,GAAI,aAAc,GAAK,EAC/DlC,EAAAA,IAAC,OAAI,SAAA,QAAA,CAAM,EACXA,EAAAA,IAAC/B,EAAA,CAAK,KAAK,YAAY,MAAO,CAAE,SAAU,EAAA,EACvC,SAAAkB,EAAc,cAAgB,gBAAA,CACjC,CAAA,CAAA,CACF,EAEAa,EAAAA,IAACmC,EAAA,CAAS,KAAK,OAAO,MAAO,CAAE,WAAY,EAAA,EACxC,SAAA7C,EAAS,IAAI,CAAC8C,EAAiBC,IAC9BnC,EAAAA,KAACiC,EAAS,KAAT,CAEC,MAAOC,EAAI,QAAU,QAAU,MAAQA,EAAI,QAAU,OAAS,SAAW,OAEzE,SAAA,CAAAlC,EAAAA,KAAC,MAAA,CAAI,MAAO,CAAE,aAAc,GAC1B,SAAA,CAAAF,EAAAA,IAACsC,EAAA,CAAI,MAAOF,EAAI,QAAU,QAAU,MAAQA,EAAI,QAAU,OAAS,SAAW,OAC3E,SAAAA,EAAI,MAAM,cACb,EACApC,EAAAA,IAAC/B,EAAA,CAAK,KAAK,YAAY,MAAO,CAAE,SAAU,EAAA,EACvC,WAAMmE,EAAI,SAAS,EAAE,OAAO,UAAU,CAAA,CACzC,CAAA,EACF,EACApC,MAAC,OAAI,MAAO,CAAE,UAAW,aACtB,WAAI,OAAA,CACP,CAAA,CAAA,EAbKqC,CAAA,CAeR,CAAA,CACH,CAAA,CAEJ,EACF,EAGIE,EAAgB,IACpBvC,EAAAA,IAAC,MAAA,CACE,SAAAF,GAAA,MAAAA,EAAa,KACZI,EAAAA,KAACY,EAAA,CAAI,OAAQ,CAAC,GAAI,EAAE,EACjB,SAAA,CAAAhB,EAAY,KAAK,OAChBE,EAAAA,IAACe,EAAA,CAAI,GAAI,GAAI,GAAI,GACf,SAAAf,EAAAA,IAACC,EAAA,CAAK,MAAO,SAASH,EAAY,KAAK,MAAM,MAAM,IACjD,SAAAE,EAAAA,IAACwC,EAAA,CACC,KAAK,QACL,WAAY1C,EAAY,KAAK,MAC7B,WAAa2C,GACXzC,EAAAA,IAACwC,EAAK,KAAL,CACC,SAAAxC,EAAAA,IAACwC,EAAK,KAAK,KAAV,CACC,MAAOC,EAAK,WACZ,YAAa,OAAOA,EAAK,MAAM,UAAUA,EAAK,SAAW,IAAI,EAAA,CAAA,EAEjE,EAEF,MAAO,CAAE,UAAW,IAAK,SAAU,MAAA,CAAO,CAAA,EAE9C,CAAA,CACF,EAGD3C,EAAY,KAAK,OAChBE,EAAAA,IAACe,EAAA,CAAI,GAAI,GAAI,GAAI,GACf,SAAAf,EAAAA,IAACC,GAAK,MAAO,SAASH,EAAY,KAAK,MAAM,MAAM,IACjD,SAAAE,EAAAA,IAACwC,EAAA,CACC,KAAK,QACL,WAAY1C,EAAY,KAAK,MAC7B,WAAa4C,GACX1C,EAAAA,IAACwC,EAAK,KAAL,CACC,SAAAxC,EAAAA,IAACwC,EAAK,KAAK,KAAV,CACC,MAAO,GAAGE,EAAK,IAAI,IAAIA,EAAK,QAAQ,GACpC,YAAa,OAAOA,EAAK,SAAW,IAAI,UAAUA,EAAK,KAAK,EAAA,CAAA,EAEhE,EAEF,MAAO,CAAE,UAAW,IAAK,SAAU,MAAA,CAAO,CAAA,EAE9C,CAAA,CACF,EAGD5C,EAAY,KAAK,iBAAmBA,EAAY,KAAK,gBAAgB,OAAS,GAC7EE,EAAAA,IAACe,EAAA,CAAI,GAAI,GACP,SAAAf,EAAAA,IAACC,GAAK,MAAO,SAASH,EAAY,KAAK,gBAAgB,MAAM,IAC3D,SAAAE,EAAAA,IAACwC,EAAA,CACC,WAAY1C,EAAY,KAAK,gBAC7B,WAAa6C,UACX3C,OAAAA,EAAAA,IAACwC,EAAK,KAAL,CACC,SAAAxC,EAAAA,IAACwC,EAAK,KAAK,KAAV,CACC,aACG,OAAA,CACC,SAAA,CAAAxC,MAACsC,GAAI,MAAOK,EAAK,WAAa,WAAa,MACjCA,EAAK,WAAa,OAAS,SAC3BA,EAAK,WAAa,SAAW,OAAS,QAC7C,UAAAlB,EAAAkB,EAAK,WAAL,YAAAlB,EAAe,cAClB,EACCkB,EAAK,IAAA,EACR,EAEF,YAAaA,EAAK,WAAA,CAAA,EAEtB,GAEF,MAAO,CAAE,UAAW,IAAK,SAAU,MAAA,CAAO,CAAA,EAE9C,CAAA,CACF,CAAA,CAAA,CAEJ,EAEA3C,EAAAA,IAACC,EAAA,CACC,eAAC,MAAA,CAAI,MAAO,CAAE,UAAW,SAAU,QAAS,QAAA,EAC1C,eAAChC,EAAA,CAAK,KAAK,YAAY,SAAA,QAAA,CAAM,EAC/B,EACF,CAAA,CAEJ,EAGF,cACG,MAAA,CAAI,MAAO,CAAE,QAAS,IACrB,SAAA,CAAAiC,EAAAA,KAAC,MAAA,CAAI,MAAO,CAAE,aAAc,IAC1B,SAAA,CAAAF,EAAAA,IAACK,EAAA,CACC,WAAOuC,GAAA,EAAkB,EACzB,QAAS,IAAMtE,EAAS,QAAQ,EAChC,MAAO,CAAE,YAAa,EAAA,EACvB,SAAA,QAAA,CAAA,EAGD4B,EAAAA,KAAClC,GAAA,CAAM,MAAO,EAAG,MAAO,CAAE,QAAS,eAAgB,OAAQ,CAAA,EAAK,SAAA,CAAA,UACtDsC,EAAY,MAAA,CAAA,CACtB,CAAA,EACF,EAEAN,EAAAA,IAAC6C,GAAA,CACC,UAAWrE,EACX,SAAUC,EACV,MAAO,CACL,CACE,IAAK,WACL,MAAO,KACP,SAAUoC,EAAA,CAAe,EAE3B,CACE,IAAK,OACL,MAAO,OACP,SAAUoB,EAAA,CAAW,EAEvB,CACE,IAAK,UACL,MAAO,OACP,SAAUM,EAAA,CAAc,CAC1B,CACF,CAAA,CACF,EACF,CAEJ"}