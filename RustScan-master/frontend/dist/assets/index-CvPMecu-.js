import{q as ye,v as _e,x as u,j as e}from"./index-BAEl3mLK.js";import{r as o}from"./vendor-o6zXO7vr.js";import{F as c,s as p,ak as E,R as h,C as r,c as l,k as d,ao as be,g as U,m as W,a7 as k,ab as Y,v as K,B as i,ap as G,S as _,aq as ve,a5 as we,q as I,P as C,D as Se,T as ke,U as J,a8 as Q,a9 as b}from"./ui-DUwPBEDa.js";import"./charts-DW2bYSvi.js";const{TabPane:T}=Y,{Text:X}=ke,Be=()=>{var F,P,N,O,V,H,L,q;const[v,Z]=o.useState(null),[j,D]=o.useState([]),[z,M]=o.useState({}),[a,ee]=o.useState(null),[t,se]=o.useState(null),[B,te]=o.useState([]),[m,x]=o.useState(!1),[ae,f]=o.useState(!1),[ne,g]=o.useState(!1),[re]=c.useForm(),{user:w}=ye(),S=(w==null?void 0:w.role)===_e.Admin;o.useEffect(()=>{S&&y()},[S]);const y=async()=>{x(!0);try{await Promise.all([R(),le(),ce(),oe(),ie()])}catch{p.error("获取系统信息失败")}finally{x(!1)}},R=async()=>{try{const s=await u.getDatabaseStats();s.data.success&&Z(s.data.data)}catch(s){console.error("获取数据库统计失败:",s)}},le=async()=>{try{const s=await u.getHealth();s.data.success&&ee(s.data.data)}catch(s){console.error("获取健康状态失败:",s)}},ce=async()=>{try{const s=await u.getPerformanceRecommendations();s.data.success&&D(s.data.data.recommendations||[])}catch(s){console.error("获取性能建议失败:",s)}},oe=async()=>{try{const s=await u.getSystemMetrics();s.data.success&&se(s.data.data)}catch(s){console.error("获取系统指标失败:",s)}},ie=async()=>{try{const s=await u.getSystemAlerts();s.data.success&&te(s.data.data.alerts||[])}catch(s){console.error("获取系统告警失败:",s)}},$=async()=>{x(!0);try{const s=await u.runPerformanceTest();s.data.success&&(M(s.data.data.performance_metrics),p.success("性能测试完成"))}catch{p.error("性能测试失败")}finally{x(!1)}},de=async s=>{x(!0);try{(await u.optimizeDatabase(s)).data.success&&(p.success("数据库优化完成"),f(!1),y())}catch{p.error("数据库优化失败")}finally{x(!1)}},xe=async s=>{x(!0);try{const n=await u.cleanupCompletedTasks(s.retention_days);n.data.success&&(p.success(n.data.data.message),g(!1),y())}catch{p.error("清理任务失败")}finally{x(!1)}},A=s=>{switch(s){case"excellent":return"#52c41a";case"good":return"#1890ff";case"fair":return"#faad14";case"poor":return"#f5222d";default:return"#d9d9d9"}},ue=s=>{switch(s){case"excellent":return"优秀";case"good":return"良好";case"fair":return"一般";case"poor":return"较差";default:return"未知"}};if(!S)return e.jsxs("div",{style:{textAlign:"center",padding:"100px 0"},children:[e.jsx(E,{style:{fontSize:"48px",color:"#faad14",marginBottom:"16px"}}),e.jsx("h2",{children:"权限不足"}),e.jsx("p",{children:"只有管理员可以访问系统管理功能"})]});const pe=[{title:"数据表",dataIndex:"table",key:"table"},{title:"记录数",dataIndex:"count",key:"count",render:s=>s.toLocaleString()},{title:"占比",dataIndex:"percentage",key:"percentage",render:s=>`${s.toFixed(1)}%`}],me=v?Object.entries(v.table_sizes).map(([s,n])=>({key:s,table:s,count:n,percentage:n/Object.values(v.table_sizes).reduce((fe,ge)=>fe+ge,0)*100})).sort((s,n)=>n.count-s.count):[],je=[{title:"测试项目",dataIndex:"test",key:"test"},{title:"响应时间 (ms)",dataIndex:"time",key:"time",render:s=>{const n=s<10?"green":s<50?"orange":"red";return e.jsx(U,{color:n,children:s.toFixed(2)})}}],he=Object.entries(z).map(([s,n])=>({key:s,test:s.replace(/_/g," ").replace(/ms$/,""),time:n}));return e.jsxs("div",{style:{padding:"24px"},children:[e.jsxs(h,{gutter:16,style:{marginBottom:"24px"},children:[e.jsx(r,{span:6,children:e.jsxs(l,{children:[e.jsx(d,{title:"系统健康分数",value:(a==null?void 0:a.health_score)||0,suffix:"/100",prefix:e.jsx(be,{}),valueStyle:{color:A(a==null?void 0:a.health_status)}}),e.jsx("div",{style:{marginTop:"8px"},children:e.jsx(U,{color:A(a==null?void 0:a.health_status),children:ue(a==null?void 0:a.health_status)})})]})}),e.jsx(r,{span:6,children:e.jsx(l,{children:e.jsx(d,{title:"总记录数",value:(a==null?void 0:a.total_records)||0,prefix:e.jsx(W,{}),formatter:s=>`${Number(s).toLocaleString()}`})})}),e.jsx(r,{span:6,children:e.jsx(l,{children:e.jsx(d,{title:"WAL文件大小",value:(a==null?void 0:a.wal_size_mb)||0,suffix:"MB",prefix:e.jsx(W,{}),valueStyle:{color:((a==null?void 0:a.wal_size_mb)||0)>100?"#f5222d":"#52c41a"}})})}),e.jsx(r,{span:6,children:e.jsx(l,{children:e.jsx(d,{title:"性能建议",value:j.length,suffix:"条",prefix:e.jsx(E,{}),valueStyle:{color:j.length>3?"#f5222d":j.length>1?"#faad14":"#52c41a"}})})})]}),j.length>0&&e.jsx(k,{message:"性能优化建议",description:e.jsx("ul",{style:{marginBottom:0},children:j.map((s,n)=>e.jsx("li",{children:s},n))}),type:"warning",showIcon:!0,style:{marginBottom:"24px"}}),e.jsxs(Y,{defaultActiveKey:"overview",children:[e.jsx(T,{tab:"概览",children:e.jsxs(h,{gutter:16,children:[e.jsx(r,{span:12,children:e.jsx(l,{title:"数据表统计",extra:e.jsx(i,{type:"link",icon:e.jsx(G,{}),onClick:R,loading:m,children:"刷新"}),children:e.jsx(K,{dataSource:me,columns:pe,pagination:!1,size:"small"})})}),e.jsx(r,{span:12,children:e.jsx(l,{title:"系统操作",style:{height:"100%"},children:e.jsxs(_,{direction:"vertical",style:{width:"100%"},children:[e.jsx(i,{type:"primary",icon:e.jsx(ve,{}),onClick:()=>f(!0),block:!0,children:"优化数据库"}),e.jsx(i,{icon:e.jsx(we,{}),onClick:()=>g(!0),block:!0,children:"清理历史数据"}),e.jsx(i,{icon:e.jsx(I,{}),onClick:$,loading:m,block:!0,children:"运行性能测试"}),e.jsx(i,{icon:e.jsx(G,{}),onClick:y,loading:m,block:!0,children:"刷新全部数据"})]})})})]})},"overview"),e.jsxs(T,{tab:"系统监控",children:[e.jsxs(h,{gutter:16,style:{marginBottom:"16px"},children:[e.jsx(r,{span:8,children:e.jsxs(l,{children:[e.jsx(d,{title:"CPU使用率",value:(t==null?void 0:t.cpu_usage)||0,suffix:"%",precision:1,valueStyle:{color:((t==null?void 0:t.cpu_usage)||0)>80?"#f5222d":"#52c41a"}}),e.jsx(C,{percent:(t==null?void 0:t.cpu_usage)||0,size:"small",status:((t==null?void 0:t.cpu_usage)||0)>80?"exception":"normal"})]})}),e.jsx(r,{span:8,children:e.jsxs(l,{children:[e.jsx(d,{title:"内存使用率",value:((F=t==null?void 0:t.memory_usage)==null?void 0:F.usage_percent)||0,suffix:"%",precision:1,valueStyle:{color:(((P=t==null?void 0:t.memory_usage)==null?void 0:P.usage_percent)||0)>80?"#f5222d":"#52c41a"}}),e.jsx(C,{percent:((N=t==null?void 0:t.memory_usage)==null?void 0:N.usage_percent)||0,size:"small",status:(((O=t==null?void 0:t.memory_usage)==null?void 0:O.usage_percent)||0)>80?"exception":"normal"})]})}),e.jsx(r,{span:8,children:e.jsx(l,{children:e.jsx(d,{title:"活跃进程",value:((V=t==null?void 0:t.process_stats)==null?void 0:V.total_processes)||0,prefix:e.jsx(Se,{})})})})]}),B.length>0&&e.jsx(l,{title:"系统告警",style:{marginBottom:"16px"},children:e.jsx(_,{direction:"vertical",style:{width:"100%"},children:B.map((s,n)=>e.jsx(k,{message:`${s.category}: ${s.message}`,type:s.level==="critical"?"error":s.level==="warning"?"warning":"info",showIcon:!0,closable:!0},n))})}),e.jsxs(h,{gutter:16,children:[e.jsx(r,{span:12,children:e.jsx(l,{title:"磁盘使用情况",children:(H=t==null?void 0:t.disk_usage)==null?void 0:H.map((s,n)=>e.jsxs("div",{style:{marginBottom:"12px"},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between"},children:[e.jsx(X,{children:s.name}),e.jsxs(X,{children:[s.usage_percent.toFixed(1),"%"]})]}),e.jsx(C,{percent:s.usage_percent,size:"small",status:s.usage_percent>85?"exception":"normal"})]},n))})}),e.jsx(r,{span:12,children:e.jsx(l,{title:"网络统计",children:e.jsxs(h,{gutter:16,children:[e.jsx(r,{span:12,children:e.jsx(d,{title:"接收字节",value:((L=t==null?void 0:t.network_stats)==null?void 0:L.bytes_received)||0,formatter:s=>`${(Number(s)/1024/1024).toFixed(2)} MB`})}),e.jsx(r,{span:12,children:e.jsx(d,{title:"发送字节",value:((q=t==null?void 0:t.network_stats)==null?void 0:q.bytes_transmitted)||0,formatter:s=>`${(Number(s)/1024/1024).toFixed(2)} MB`})})]})})})]})]},"monitoring"),e.jsx(T,{tab:"性能测试",children:e.jsx(l,{title:"查询性能测试结果",extra:e.jsx(i,{icon:e.jsx(I,{}),onClick:$,loading:m,children:"重新测试"}),children:Object.keys(z).length>0?e.jsx(K,{dataSource:he,columns:je,pagination:!1}):e.jsxs("div",{style:{textAlign:"center",padding:"40px 0"},children:[e.jsx(I,{style:{fontSize:"48px",color:"#d9d9d9"}}),e.jsx("p",{style:{color:"#999",marginTop:"16px"},children:'点击"重新测试"按钮开始性能测试'})]})})},"performance")]}),e.jsx(J,{title:"数据库优化",open:ae,onCancel:()=>f(!1),footer:null,children:e.jsxs(c,{form:re,layout:"vertical",onFinish:de,initialValues:{auto_vacuum_enabled:!0,log_retention_days:30,max_wal_size_mb:100,cleanup_completed_tasks:!1,task_retention_days:90},children:[e.jsx(c.Item,{label:"启用自动清理",name:"auto_vacuum_enabled",valuePropName:"checked",children:e.jsx(Q,{})}),e.jsx(c.Item,{label:"日志保留天数",name:"log_retention_days",children:e.jsx(b,{min:1,max:365})}),e.jsx(c.Item,{label:"WAL文件最大大小 (MB)",name:"max_wal_size_mb",children:e.jsx(b,{min:10,max:1e3})}),e.jsx(c.Item,{label:"同时清理已完成任务",name:"cleanup_completed_tasks",valuePropName:"checked",children:e.jsx(Q,{})}),e.jsx(c.Item,{label:"任务保留天数",name:"task_retention_days",dependencies:["cleanup_completed_tasks"],children:e.jsx(b,{min:7,max:365})}),e.jsx(c.Item,{style:{marginBottom:0},children:e.jsxs(_,{style:{width:"100%",justifyContent:"flex-end"},children:[e.jsx(i,{onClick:()=>f(!1),children:"取消"}),e.jsx(i,{type:"primary",htmlType:"submit",loading:m,children:"开始优化"})]})})]})}),e.jsx(J,{title:"清理历史数据",open:ne,onCancel:()=>g(!1),footer:null,children:e.jsxs(c,{layout:"vertical",onFinish:xe,initialValues:{retention_days:90},children:[e.jsx(k,{message:"注意",description:"此操作将永久删除指定天数之前的已完成任务及其相关数据，请谨慎操作！",type:"warning",showIcon:!0,style:{marginBottom:"16px"}}),e.jsx(c.Item,{label:"保留天数",name:"retention_days",help:"将删除这个天数之前的已完成任务",children:e.jsx(b,{min:7,max:365})}),e.jsx(c.Item,{style:{marginBottom:0},children:e.jsxs(_,{style:{width:"100%",justifyContent:"flex-end"},children:[e.jsx(i,{onClick:()=>g(!1),children:"取消"}),e.jsx(i,{type:"primary",danger:!0,htmlType:"submit",loading:m,children:"确认清理"})]})})]})})]})};export{Be as default};
//# sourceMappingURL=index-CvPMecu-.js.map
