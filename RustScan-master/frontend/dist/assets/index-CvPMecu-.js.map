{"version": 3, "file": "index-CvPMecu-.js", "sources": ["../../src/pages/SystemManagement/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  <PERSON>ati<PERSON>,\n  Button,\n  Alert,\n  Table,\n  Progress,\n  Tabs,\n  Modal,\n  Form,\n  InputNumber,\n  Switch,\n  message,\n  Tag,\n  Space,\n  Divider,\n  Typography,\n} from 'antd';\nimport {\n  DatabaseOutlined,\n  <PERSON>boltOutlined,\n  CheckOutlined,\n  WarningOutlined,\n  CheckCircleOutlined,\n  SyncOutlined,\n  DeleteOutlined,\n  Bar<PERSON><PERSON>Outlined,\n  MonitorOutlined,\n} from '@ant-design/icons';\nimport { systemApi } from '@/api';\nimport { useAuthStore } from '@/store/auth';\nimport { UserRole } from '@/types';\n\nconst { TabPane } = Tabs;\nconst { Text } = Typography;\n\ninterface DatabaseStats {\n  table_sizes: Record<string, number>;\n  wal_size: number;\n  health_score?: number;\n  health_status?: string;\n  total_records?: number;\n  wal_size_mb?: number;\n  recommendations?: string[];\n  timestamp?: string;\n}\n\ninterface PerformanceMetrics {\n  [key: string]: number;\n}\n\nconst SystemManagement: React.FC = () => {\n  const [stats, setStats] = useState<DatabaseStats | null>(null);\n  const [recommendations, setRecommendations] = useState<string[]>([]);\n  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics>({});\n  const [healthData, setHealthData] = useState<any>(null);\n  const [systemMetrics, setSystemMetrics] = useState<any>(null);\n  const [alerts, setAlerts] = useState<any[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [optimizeModalVisible, setOptimizeModalVisible] = useState(false);\n  const [cleanupModalVisible, setCleanupModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  \n  const { user } = useAuthStore();\n\n  // 检查权限\n  const canManageSystem = user?.role === UserRole.Admin;\n\n  useEffect(() => {\n    if (canManageSystem) {\n      fetchAllData();\n    }\n  }, [canManageSystem]);\n\n  const fetchAllData = async () => {\n    setLoading(true);\n    try {\n      await Promise.all([\n        fetchDatabaseStats(),\n        fetchHealthData(),\n        fetchRecommendations(),\n        fetchSystemMetrics(),\n        fetchAlerts(),\n      ]);\n    } catch (error) {\n      message.error('获取系统信息失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchDatabaseStats = async () => {\n    try {\n      const response = await systemApi.getDatabaseStats();\n      if (response.data.success) {\n        setStats(response.data.data);\n      }\n    } catch (error) {\n      console.error('获取数据库统计失败:', error);\n    }\n  };\n\n  const fetchHealthData = async () => {\n    try {\n      const response = await systemApi.getHealth();\n      if (response.data.success) {\n        setHealthData(response.data.data);\n      }\n    } catch (error) {\n      console.error('获取健康状态失败:', error);\n    }\n  };\n\n  const fetchRecommendations = async () => {\n    try {\n      const response = await systemApi.getPerformanceRecommendations();\n      if (response.data.success) {\n        setRecommendations(response.data.data.recommendations || []);\n      }\n    } catch (error) {\n      console.error('获取性能建议失败:', error);\n    }\n  };\n\n  const fetchSystemMetrics = async () => {\n    try {\n      const response = await systemApi.getSystemMetrics();\n      if (response.data.success) {\n        setSystemMetrics(response.data.data);\n      }\n    } catch (error) {\n      console.error('获取系统指标失败:', error);\n    }\n  };\n\n  const fetchAlerts = async () => {\n    try {\n      const response = await systemApi.getSystemAlerts();\n      if (response.data.success) {\n        setAlerts(response.data.data.alerts || []);\n      }\n    } catch (error) {\n      console.error('获取系统告警失败:', error);\n    }\n  };\n\n  const runPerformanceTest = async () => {\n    setLoading(true);\n    try {\n      const response = await systemApi.runPerformanceTest();\n      if (response.data.success) {\n        setPerformanceMetrics(response.data.data.performance_metrics);\n        message.success('性能测试完成');\n      }\n    } catch (error) {\n      message.error('性能测试失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleOptimizeDatabase = async (values: any) => {\n    setLoading(true);\n    try {\n      const response = await systemApi.optimizeDatabase(values);\n      if (response.data.success) {\n        message.success('数据库优化完成');\n        setOptimizeModalVisible(false);\n        fetchAllData();\n      }\n    } catch (error) {\n      message.error('数据库优化失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCleanupTasks = async (values: any) => {\n    setLoading(true);\n    try {\n      const response = await systemApi.cleanupCompletedTasks(values.retention_days);\n      if (response.data.success) {\n        message.success(response.data.data.message);\n        setCleanupModalVisible(false);\n        fetchAllData();\n      }\n    } catch (error) {\n      message.error('清理任务失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getHealthColor = (status: string) => {\n    switch (status) {\n      case 'excellent': return '#52c41a';\n      case 'good': return '#1890ff';\n      case 'fair': return '#faad14';\n      case 'poor': return '#f5222d';\n      default: return '#d9d9d9';\n    }\n  };\n\n  const getHealthText = (status: string) => {\n    switch (status) {\n      case 'excellent': return '优秀';\n      case 'good': return '良好';\n      case 'fair': return '一般';\n      case 'poor': return '较差';\n      default: return '未知';\n    }\n  };\n\n  if (!canManageSystem) {\n    return (\n      <div style={{ textAlign: 'center', padding: '100px 0' }}>\n        <WarningOutlined style={{ fontSize: '48px', color: '#faad14', marginBottom: '16px' }} />\n        <h2>权限不足</h2>\n        <p>只有管理员可以访问系统管理功能</p>\n      </div>\n    );\n  }\n\n  // 表格数据\n  const tableColumns = [\n    {\n      title: '数据表',\n      dataIndex: 'table',\n      key: 'table',\n    },\n    {\n      title: '记录数',\n      dataIndex: 'count',\n      key: 'count',\n      render: (count: number) => count.toLocaleString(),\n    },\n    {\n      title: '占比',\n      dataIndex: 'percentage',\n      key: 'percentage',\n      render: (percentage: number) => `${percentage.toFixed(1)}%`,\n    },\n  ];\n\n  const tableData = stats ? Object.entries(stats.table_sizes).map(([table, count]) => ({\n    key: table,\n    table,\n    count,\n    percentage: (count / Object.values(stats.table_sizes).reduce((a, b) => a + b, 0)) * 100,\n  })).sort((a, b) => b.count - a.count) : [];\n\n  const performanceColumns = [\n    {\n      title: '测试项目',\n      dataIndex: 'test',\n      key: 'test',\n    },\n    {\n      title: '响应时间 (ms)',\n      dataIndex: 'time',\n      key: 'time',\n      render: (time: number) => {\n        const color = time < 10 ? 'green' : time < 50 ? 'orange' : 'red';\n        return <Tag color={color}>{time.toFixed(2)}</Tag>;\n      },\n    },\n  ];\n\n  const performanceData = Object.entries(performanceMetrics).map(([test, time]) => ({\n    key: test,\n    test: test.replace(/_/g, ' ').replace(/ms$/, ''),\n    time,\n  }));\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <Row gutter={16} style={{ marginBottom: '24px' }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"系统健康分数\"\n              value={healthData?.health_score || 0}\n              suffix=\"/100\"\n              prefix={<CheckOutlined />}\n              valueStyle={{ color: getHealthColor(healthData?.health_status) }}\n            />\n            <div style={{ marginTop: '8px' }}>\n              <Tag color={getHealthColor(healthData?.health_status)}>\n                {getHealthText(healthData?.health_status)}\n              </Tag>\n            </div>\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总记录数\"\n              value={healthData?.total_records || 0}\n              prefix={<DatabaseOutlined />}\n              formatter={(value) => `${Number(value).toLocaleString()}`}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"WAL文件大小\"\n              value={healthData?.wal_size_mb || 0}\n              suffix=\"MB\"\n              prefix={<DatabaseOutlined />}\n              valueStyle={{ \n                color: (healthData?.wal_size_mb || 0) > 100 ? '#f5222d' : '#52c41a' \n              }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"性能建议\"\n              value={recommendations.length}\n              suffix=\"条\"\n              prefix={<WarningOutlined />}\n              valueStyle={{ \n                color: recommendations.length > 3 ? '#f5222d' : \n                       recommendations.length > 1 ? '#faad14' : '#52c41a' \n              }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {recommendations.length > 0 && (\n        <Alert\n          message=\"性能优化建议\"\n          description={\n            <ul style={{ marginBottom: 0 }}>\n              {recommendations.map((rec, index) => (\n                <li key={index}>{rec}</li>\n              ))}\n            </ul>\n          }\n          type=\"warning\"\n          showIcon\n          style={{ marginBottom: '24px' }}\n        />\n      )}\n\n      <Tabs defaultActiveKey=\"overview\">\n        <TabPane tab=\"概览\" key=\"overview\">\n          <Row gutter={16}>\n            <Col span={12}>\n              <Card title=\"数据表统计\" extra={\n                <Button \n                  type=\"link\" \n                  icon={<SyncOutlined />}\n                  onClick={fetchDatabaseStats}\n                  loading={loading}\n                >\n                  刷新\n                </Button>\n              }>\n                <Table\n                  dataSource={tableData}\n                  columns={tableColumns}\n                  pagination={false}\n                  size=\"small\"\n                />\n              </Card>\n            </Col>\n            <Col span={12}>\n              <Card title=\"系统操作\" style={{ height: '100%' }}>\n                <Space direction=\"vertical\" style={{ width: '100%' }}>\n                  <Button\n                    type=\"primary\"\n                    icon={<ThunderboltOutlined />}\n                    onClick={() => setOptimizeModalVisible(true)}\n                    block\n                  >\n                    优化数据库\n                  </Button>\n                  <Button\n                    icon={<DeleteOutlined />}\n                    onClick={() => setCleanupModalVisible(true)}\n                    block\n                  >\n                    清理历史数据\n                  </Button>\n                  <Button\n                    icon={<BarChartOutlined />}\n                    onClick={runPerformanceTest}\n                    loading={loading}\n                    block\n                  >\n                    运行性能测试\n                  </Button>\n                  <Button\n                    icon={<SyncOutlined />}\n                    onClick={fetchAllData}\n                    loading={loading}\n                    block\n                  >\n                    刷新全部数据\n                  </Button>\n                </Space>\n              </Card>\n            </Col>\n          </Row>\n        </TabPane>\n\n        <TabPane tab=\"系统监控\" key=\"monitoring\">\n          <Row gutter={16} style={{ marginBottom: '16px' }}>\n            <Col span={8}>\n              <Card>\n                <Statistic\n                  title=\"CPU使用率\"\n                  value={systemMetrics?.cpu_usage || 0}\n                  suffix=\"%\"\n                  precision={1}\n                  valueStyle={{ \n                    color: (systemMetrics?.cpu_usage || 0) > 80 ? '#f5222d' : '#52c41a' \n                  }}\n                />\n                <Progress \n                  percent={systemMetrics?.cpu_usage || 0} \n                  size=\"small\" \n                  status={(systemMetrics?.cpu_usage || 0) > 80 ? 'exception' : 'normal'}\n                />\n              </Card>\n            </Col>\n            <Col span={8}>\n              <Card>\n                <Statistic\n                  title=\"内存使用率\"\n                  value={systemMetrics?.memory_usage?.usage_percent || 0}\n                  suffix=\"%\"\n                  precision={1}\n                  valueStyle={{ \n                    color: (systemMetrics?.memory_usage?.usage_percent || 0) > 80 ? '#f5222d' : '#52c41a' \n                  }}\n                />\n                <Progress \n                  percent={systemMetrics?.memory_usage?.usage_percent || 0} \n                  size=\"small\"\n                  status={(systemMetrics?.memory_usage?.usage_percent || 0) > 80 ? 'exception' : 'normal'}\n                />\n              </Card>\n            </Col>\n            <Col span={8}>\n              <Card>\n                <Statistic\n                  title=\"活跃进程\"\n                  value={systemMetrics?.process_stats?.total_processes || 0}\n                  prefix={<MonitorOutlined />}\n                />\n              </Card>\n            </Col>\n          </Row>\n\n          {alerts.length > 0 && (\n            <Card title=\"系统告警\" style={{ marginBottom: '16px' }}>\n              <Space direction=\"vertical\" style={{ width: '100%' }}>\n                {alerts.map((alert, index) => (\n                  <Alert\n                    key={index}\n                    message={`${alert.category}: ${alert.message}`}\n                    type={alert.level === 'critical' ? 'error' : \n                          alert.level === 'warning' ? 'warning' : 'info'}\n                    showIcon\n                    closable\n                  />\n                ))}\n              </Space>\n            </Card>\n          )}\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Card title=\"磁盘使用情况\">\n                {systemMetrics?.disk_usage?.map((disk: any, index: number) => (\n                  <div key={index} style={{ marginBottom: '12px' }}>\n                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                      <Text>{disk.name}</Text>\n                      <Text>{disk.usage_percent.toFixed(1)}%</Text>\n                    </div>\n                    <Progress \n                      percent={disk.usage_percent} \n                      size=\"small\"\n                      status={disk.usage_percent > 85 ? 'exception' : 'normal'}\n                    />\n                  </div>\n                ))}\n              </Card>\n            </Col>\n            <Col span={12}>\n              <Card title=\"网络统计\">\n                <Row gutter={16}>\n                  <Col span={12}>\n                    <Statistic\n                      title=\"接收字节\"\n                      value={systemMetrics?.network_stats?.bytes_received || 0}\n                      formatter={(value) => `${(Number(value) / 1024 / 1024).toFixed(2)} MB`}\n                    />\n                  </Col>\n                  <Col span={12}>\n                    <Statistic\n                      title=\"发送字节\"\n                      value={systemMetrics?.network_stats?.bytes_transmitted || 0}\n                      formatter={(value) => `${(Number(value) / 1024 / 1024).toFixed(2)} MB`}\n                    />\n                  </Col>\n                </Row>\n              </Card>\n            </Col>\n          </Row>\n        </TabPane>\n\n        <TabPane tab=\"性能测试\" key=\"performance\">\n          <Card \n            title=\"查询性能测试结果\" \n            extra={\n              <Button \n                icon={<BarChartOutlined />}\n                onClick={runPerformanceTest}\n                loading={loading}\n              >\n                重新测试\n              </Button>\n            }\n          >\n            {Object.keys(performanceMetrics).length > 0 ? (\n              <Table\n                dataSource={performanceData}\n                columns={performanceColumns}\n                pagination={false}\n              />\n            ) : (\n              <div style={{ textAlign: 'center', padding: '40px 0' }}>\n                <BarChartOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />\n                <p style={{ color: '#999', marginTop: '16px' }}>\n                  点击&quot;重新测试&quot;按钮开始性能测试\n                </p>\n              </div>\n            )}\n          </Card>\n        </TabPane>\n      </Tabs>\n\n      {/* 数据库优化Modal */}\n      <Modal\n        title=\"数据库优化\"\n        open={optimizeModalVisible}\n        onCancel={() => setOptimizeModalVisible(false)}\n        footer={null}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleOptimizeDatabase}\n          initialValues={{\n            auto_vacuum_enabled: true,\n            log_retention_days: 30,\n            max_wal_size_mb: 100,\n            cleanup_completed_tasks: false,\n            task_retention_days: 90,\n          }}\n        >\n          <Form.Item\n            label=\"启用自动清理\"\n            name=\"auto_vacuum_enabled\"\n            valuePropName=\"checked\"\n          >\n            <Switch />\n          </Form.Item>\n\n          <Form.Item\n            label=\"日志保留天数\"\n            name=\"log_retention_days\"\n          >\n            <InputNumber min={1} max={365} />\n          </Form.Item>\n\n          <Form.Item\n            label=\"WAL文件最大大小 (MB)\"\n            name=\"max_wal_size_mb\"\n          >\n            <InputNumber min={10} max={1000} />\n          </Form.Item>\n\n          <Form.Item\n            label=\"同时清理已完成任务\"\n            name=\"cleanup_completed_tasks\"\n            valuePropName=\"checked\"\n          >\n            <Switch />\n          </Form.Item>\n\n          <Form.Item\n            label=\"任务保留天数\"\n            name=\"task_retention_days\"\n            dependencies={['cleanup_completed_tasks']}\n          >\n            <InputNumber min={7} max={365} />\n          </Form.Item>\n\n          <Form.Item style={{ marginBottom: 0 }}>\n            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>\n              <Button onClick={() => setOptimizeModalVisible(false)}>\n                取消\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\" loading={loading}>\n                开始优化\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 清理任务Modal */}\n      <Modal\n        title=\"清理历史数据\"\n        open={cleanupModalVisible}\n        onCancel={() => setCleanupModalVisible(false)}\n        footer={null}\n      >\n        <Form\n          layout=\"vertical\"\n          onFinish={handleCleanupTasks}\n          initialValues={{ retention_days: 90 }}\n        >\n          <Alert\n            message=\"注意\"\n            description=\"此操作将永久删除指定天数之前的已完成任务及其相关数据，请谨慎操作！\"\n            type=\"warning\"\n            showIcon\n            style={{ marginBottom: '16px' }}\n          />\n\n          <Form.Item\n            label=\"保留天数\"\n            name=\"retention_days\"\n            help=\"将删除这个天数之前的已完成任务\"\n          >\n            <InputNumber min={7} max={365} />\n          </Form.Item>\n\n          <Form.Item style={{ marginBottom: 0 }}>\n            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>\n              <Button onClick={() => setCleanupModalVisible(false)}>\n                取消\n              </Button>\n              <Button type=\"primary\" danger htmlType=\"submit\" loading={loading}>\n                确认清理\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default SystemManagement;"], "names": ["TabPane", "Tabs", "Text", "Typography", "SystemManagement", "stats", "setStats", "useState", "recommendations", "setRecommendations", "performanceMetrics", "setPerformanceMetrics", "healthData", "setHealthData", "systemMetrics", "setSystemMetrics", "alerts", "<PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "optimizeModalVisible", "setOptimizeModalVisible", "cleanupModalVisible", "setCleanupModalVisible", "form", "Form", "user", "useAuthStore", "canManageSystem", "UserRole", "useEffect", "fetchAllData", "fetchDatabaseStats", "fetchHealthData", "fetchRecommendations", "fetchSystemMetrics", "fetch<PERSON><PERSON><PERSON>", "message", "response", "systemApi", "error", "runPerformanceTest", "handleOptimizeDatabase", "values", "handleCleanupTasks", "getHealthColor", "status", "getHealthText", "jsxs", "jsx", "WarningOutlined", "tableColumns", "count", "percentage", "tableData", "table", "a", "b", "performanceColumns", "time", "color", "Tag", "performanceData", "test", "Row", "Col", "Card", "Statistic", "CheckOutlined", "DatabaseOutlined", "value", "<PERSON><PERSON>", "rec", "index", "<PERSON><PERSON>", "SyncOutlined", "Table", "Space", "ThunderboltOutlined", "DeleteOutlined", "BarChartOutlined", "Progress", "_a", "_b", "_c", "_d", "_e", "MonitorOutlined", "alert", "_f", "disk", "_g", "_h", "Modal", "Switch", "InputNumber"], "mappings": "gWAoCA,KAAM,CAAE,QAAAA,GAAYC,EACd,CAAE,KAAAC,GAASC,GAiBXC,GAA6B,IAAM,qBACvC,KAAM,CAACC,EAAOC,CAAQ,EAAIC,EAAAA,SAA+B,IAAI,EACvD,CAACC,EAAiBC,CAAkB,EAAIF,EAAAA,SAAmB,CAAA,CAAE,EAC7D,CAACG,EAAoBC,CAAqB,EAAIJ,EAAAA,SAA6B,CAAA,CAAE,EAC7E,CAACK,EAAYC,EAAa,EAAIN,EAAAA,SAAc,IAAI,EAChD,CAACO,EAAeC,EAAgB,EAAIR,EAAAA,SAAc,IAAI,EACtD,CAACS,EAAQC,EAAS,EAAIV,EAAAA,SAAgB,CAAA,CAAE,EACxC,CAACW,EAASC,CAAU,EAAIZ,EAAAA,SAAS,EAAK,EACtC,CAACa,GAAsBC,CAAuB,EAAId,EAAAA,SAAS,EAAK,EAChE,CAACe,GAAqBC,CAAsB,EAAIhB,EAAAA,SAAS,EAAK,EAC9D,CAACiB,EAAI,EAAIC,EAAK,QAAA,EAEd,CAAE,KAAAC,CAAA,EAASC,GAAA,EAGXC,GAAkBF,GAAA,YAAAA,EAAM,QAASG,GAAS,MAEhDC,EAAAA,UAAU,IAAM,CACVF,GACFG,EAAA,CAEJ,EAAG,CAACH,CAAe,CAAC,EAEpB,MAAMG,EAAe,SAAY,CAC/BZ,EAAW,EAAI,EACf,GAAI,CACF,MAAM,QAAQ,IAAI,CAChBa,EAAA,EACAC,GAAA,EACAC,GAAA,EACAC,GAAA,EACAC,GAAA,CAAY,CACb,CACH,MAAgB,CACdC,EAAQ,MAAM,UAAU,CAC1B,QAAA,CACElB,EAAW,EAAK,CAClB,CACF,EAEMa,EAAqB,SAAY,CACrC,GAAI,CACF,MAAMM,EAAW,MAAMC,EAAU,iBAAA,EAC7BD,EAAS,KAAK,SAChBhC,EAASgC,EAAS,KAAK,IAAI,CAE/B,OAASE,EAAO,CACd,QAAQ,MAAM,aAAcA,CAAK,CACnC,CACF,EAEMP,GAAkB,SAAY,CAClC,GAAI,CACF,MAAMK,EAAW,MAAMC,EAAU,UAAA,EAC7BD,EAAS,KAAK,SAChBzB,GAAcyB,EAAS,KAAK,IAAI,CAEpC,OAASE,EAAO,CACd,QAAQ,MAAM,YAAaA,CAAK,CAClC,CACF,EAEMN,GAAuB,SAAY,CACvC,GAAI,CACF,MAAMI,EAAW,MAAMC,EAAU,8BAAA,EAC7BD,EAAS,KAAK,SAChB7B,EAAmB6B,EAAS,KAAK,KAAK,iBAAmB,CAAA,CAAE,CAE/D,OAASE,EAAO,CACd,QAAQ,MAAM,YAAaA,CAAK,CAClC,CACF,EAEML,GAAqB,SAAY,CACrC,GAAI,CACF,MAAMG,EAAW,MAAMC,EAAU,iBAAA,EAC7BD,EAAS,KAAK,SAChBvB,GAAiBuB,EAAS,KAAK,IAAI,CAEvC,OAASE,EAAO,CACd,QAAQ,MAAM,YAAaA,CAAK,CAClC,CACF,EAEMJ,GAAc,SAAY,CAC9B,GAAI,CACF,MAAME,EAAW,MAAMC,EAAU,gBAAA,EAC7BD,EAAS,KAAK,SAChBrB,GAAUqB,EAAS,KAAK,KAAK,QAAU,CAAA,CAAE,CAE7C,OAASE,EAAO,CACd,QAAQ,MAAM,YAAaA,CAAK,CAClC,CACF,EAEMC,EAAqB,SAAY,CACrCtB,EAAW,EAAI,EACf,GAAI,CACF,MAAMmB,EAAW,MAAMC,EAAU,mBAAA,EAC7BD,EAAS,KAAK,UAChB3B,EAAsB2B,EAAS,KAAK,KAAK,mBAAmB,EAC5DD,EAAQ,QAAQ,QAAQ,EAE5B,MAAgB,CACdA,EAAQ,MAAM,QAAQ,CACxB,QAAA,CACElB,EAAW,EAAK,CAClB,CACF,EAEMuB,GAAyB,MAAOC,GAAgB,CACpDxB,EAAW,EAAI,EACf,GAAI,EACe,MAAMoB,EAAU,iBAAiBI,CAAM,GAC3C,KAAK,UAChBN,EAAQ,QAAQ,SAAS,EACzBhB,EAAwB,EAAK,EAC7BU,EAAA,EAEJ,MAAgB,CACdM,EAAQ,MAAM,SAAS,CACzB,QAAA,CACElB,EAAW,EAAK,CAClB,CACF,EAEMyB,GAAqB,MAAOD,GAAgB,CAChDxB,EAAW,EAAI,EACf,GAAI,CACF,MAAMmB,EAAW,MAAMC,EAAU,sBAAsBI,EAAO,cAAc,EACxEL,EAAS,KAAK,UAChBD,EAAQ,QAAQC,EAAS,KAAK,KAAK,OAAO,EAC1Cf,EAAuB,EAAK,EAC5BQ,EAAA,EAEJ,MAAgB,CACdM,EAAQ,MAAM,QAAQ,CACxB,QAAA,CACElB,EAAW,EAAK,CAClB,CACF,EAEM0B,EAAkBC,GAAmB,CACzC,OAAQA,EAAA,CACN,IAAK,YAAa,MAAO,UACzB,IAAK,OAAQ,MAAO,UACpB,IAAK,OAAQ,MAAO,UACpB,IAAK,OAAQ,MAAO,UACpB,QAAS,MAAO,SAAA,CAEpB,EAEMC,GAAiBD,GAAmB,CACxC,OAAQA,EAAA,CACN,IAAK,YAAa,MAAO,KACzB,IAAK,OAAQ,MAAO,KACpB,IAAK,OAAQ,MAAO,KACpB,IAAK,OAAQ,MAAO,KACpB,QAAS,MAAO,IAAA,CAEpB,EAEA,GAAI,CAAClB,EACH,OACEoB,EAAAA,KAAC,OAAI,MAAO,CAAE,UAAW,SAAU,QAAS,WAC1C,SAAA,CAAAC,EAAAA,IAACC,EAAA,CAAgB,MAAO,CAAE,SAAU,OAAQ,MAAO,UAAW,aAAc,MAAA,CAAO,CAAG,EACtFD,EAAAA,IAAC,MAAG,SAAA,MAAA,CAAI,EACRA,EAAAA,IAAC,KAAE,SAAA,iBAAA,CAAe,CAAA,EACpB,EAKJ,MAAME,GAAe,CACnB,CACE,MAAO,MACP,UAAW,QACX,IAAK,OAAA,EAEP,CACE,MAAO,MACP,UAAW,QACX,IAAK,QACL,OAASC,GAAkBA,EAAM,eAAA,CAAe,EAElD,CACE,MAAO,KACP,UAAW,aACX,IAAK,aACL,OAASC,GAAuB,GAAGA,EAAW,QAAQ,CAAC,CAAC,GAAA,CAC1D,EAGIC,GAAYjD,EAAQ,OAAO,QAAQA,EAAM,WAAW,EAAE,IAAI,CAAC,CAACkD,EAAOH,CAAK,KAAO,CACnF,IAAKG,EACL,MAAAA,EACA,MAAAH,EACA,WAAaA,EAAQ,OAAO,OAAO/C,EAAM,WAAW,EAAE,OAAO,CAACmD,GAAGC,KAAMD,GAAIC,GAAG,CAAC,EAAK,GAAA,EACpF,EAAE,KAAK,CAACD,EAAGC,IAAMA,EAAE,MAAQD,EAAE,KAAK,EAAI,CAAA,EAElCE,GAAqB,CACzB,CACE,MAAO,OACP,UAAW,OACX,IAAK,MAAA,EAEP,CACE,MAAO,YACP,UAAW,OACX,IAAK,OACL,OAASC,GAAiB,CACxB,MAAMC,EAAQD,EAAO,GAAK,QAAUA,EAAO,GAAK,SAAW,MAC3D,aAAQE,EAAA,CAAI,MAAAD,EAAe,SAAAD,EAAK,QAAQ,CAAC,EAAE,CAC7C,CAAA,CACF,EAGIG,GAAkB,OAAO,QAAQpD,CAAkB,EAAE,IAAI,CAAC,CAACqD,EAAMJ,CAAI,KAAO,CAChF,IAAKI,EACL,KAAMA,EAAK,QAAQ,KAAM,GAAG,EAAE,QAAQ,MAAO,EAAE,EAC/C,KAAAJ,CAAA,EACA,EAEF,cACG,MAAA,CAAI,MAAO,CAAE,QAAS,QACrB,SAAA,CAAAX,OAACgB,GAAI,OAAQ,GAAI,MAAO,CAAE,aAAc,QACtC,SAAA,CAAAf,MAACgB,EAAA,CAAI,KAAM,EACT,SAAAjB,EAAAA,KAACkB,EAAA,CACC,SAAA,CAAAjB,EAAAA,IAACkB,EAAA,CACC,MAAM,SACN,OAAOvD,GAAA,YAAAA,EAAY,eAAgB,EACnC,OAAO,OACP,aAASwD,GAAA,EAAc,EACvB,WAAY,CAAE,MAAOvB,EAAejC,GAAA,YAAAA,EAAY,aAAa,CAAA,CAAE,CAAA,QAEhE,MAAA,CAAI,MAAO,CAAE,UAAW,KAAA,EACvB,SAAAqC,EAAAA,IAACY,EAAA,CAAI,MAAOhB,EAAejC,GAAA,YAAAA,EAAY,aAAa,EACjD,YAAcA,GAAA,YAAAA,EAAY,aAAa,EAC1C,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CACF,EACAqC,MAACgB,EAAA,CAAI,KAAM,EACT,eAACC,EAAA,CACC,SAAAjB,EAAAA,IAACkB,EAAA,CACC,MAAM,OACN,OAAOvD,GAAA,YAAAA,EAAY,gBAAiB,EACpC,aAASyD,EAAA,EAAiB,EAC1B,UAAYC,GAAU,GAAG,OAAOA,CAAK,EAAE,gBAAgB,EAAA,CAAA,EAE3D,CAAA,CACF,EACArB,MAACgB,EAAA,CAAI,KAAM,EACT,eAACC,EAAA,CACC,SAAAjB,EAAAA,IAACkB,EAAA,CACC,MAAM,UACN,OAAOvD,GAAA,YAAAA,EAAY,cAAe,EAClC,OAAO,KACP,aAASyD,EAAA,EAAiB,EAC1B,WAAY,CACV,QAAQzD,GAAA,YAAAA,EAAY,cAAe,GAAK,IAAM,UAAY,SAAA,CAC5D,CAAA,EAEJ,CAAA,CACF,EACAqC,MAACgB,EAAA,CAAI,KAAM,EACT,eAACC,EAAA,CACC,SAAAjB,EAAAA,IAACkB,EAAA,CACC,MAAM,OACN,MAAO3D,EAAgB,OACvB,OAAO,IACP,aAAS0C,EAAA,EAAgB,EACzB,WAAY,CACV,MAAO1C,EAAgB,OAAS,EAAI,UAC7BA,EAAgB,OAAS,EAAI,UAAY,SAAA,CAClD,CAAA,EAEJ,CAAA,CACF,CAAA,EACF,EAECA,EAAgB,OAAS,GACxByC,EAAAA,IAACsB,EAAA,CACC,QAAQ,SACR,YACEtB,EAAAA,IAAC,KAAA,CAAG,MAAO,CAAE,aAAc,GACxB,SAAAzC,EAAgB,IAAI,CAACgE,EAAKC,IACzBxB,EAAAA,IAAC,MAAgB,SAAAuB,CAAA,EAARC,CAAY,CACtB,EACH,EAEF,KAAK,UACL,SAAQ,GACR,MAAO,CAAE,aAAc,MAAA,CAAO,CAAA,EAIlCzB,EAAAA,KAAC/C,EAAA,CAAK,iBAAiB,WACrB,SAAA,CAAAgD,EAAAA,IAACjD,GAAQ,IAAI,KACX,SAAAgD,EAAAA,KAACgB,EAAA,CAAI,OAAQ,GACX,SAAA,CAAAf,EAAAA,IAACgB,GAAI,KAAM,GACT,eAACC,EAAA,CAAK,MAAM,QAAQ,MAClBjB,EAAAA,IAACyB,EAAA,CACC,KAAK,OACL,WAAOC,EAAA,EAAa,EACpB,QAAS3C,EACT,QAAAd,EACD,SAAA,IAAA,CAAA,EAID,SAAA+B,EAAAA,IAAC2B,EAAA,CACC,WAAYtB,GACZ,QAASH,GACT,WAAY,GACZ,KAAK,OAAA,CAAA,EAET,CAAA,CACF,EACAF,EAAAA,IAACgB,GAAI,KAAM,GACT,eAACC,EAAA,CAAK,MAAM,OAAO,MAAO,CAAE,OAAQ,MAAA,EAClC,gBAACW,EAAA,CAAM,UAAU,WAAW,MAAO,CAAE,MAAO,MAAA,EAC1C,SAAA,CAAA5B,EAAAA,IAACyB,EAAA,CACC,KAAK,UACL,WAAOI,GAAA,EAAoB,EAC3B,QAAS,IAAMzD,EAAwB,EAAI,EAC3C,MAAK,GACN,SAAA,OAAA,CAAA,EAGD4B,EAAAA,IAACyB,EAAA,CACC,WAAOK,GAAA,EAAe,EACtB,QAAS,IAAMxD,EAAuB,EAAI,EAC1C,MAAK,GACN,SAAA,QAAA,CAAA,EAGD0B,EAAAA,IAACyB,EAAA,CACC,WAAOM,EAAA,EAAiB,EACxB,QAASvC,EACT,QAAAvB,EACA,MAAK,GACN,SAAA,QAAA,CAAA,EAGD+B,EAAAA,IAACyB,EAAA,CACC,WAAOC,EAAA,EAAa,EACpB,QAAS5C,EACT,QAAAb,EACA,MAAK,GACN,SAAA,QAAA,CAAA,CAED,CAAA,CACF,EACF,CAAA,CACF,CAAA,CAAA,CACF,GA1DoB,UA2DtB,EAEA8B,EAAAA,KAAChD,EAAA,CAAQ,IAAI,OACX,SAAA,CAAAgD,OAACgB,GAAI,OAAQ,GAAI,MAAO,CAAE,aAAc,QACtC,SAAA,CAAAf,MAACgB,EAAA,CAAI,KAAM,EACT,SAAAjB,EAAAA,KAACkB,EAAA,CACC,SAAA,CAAAjB,EAAAA,IAACkB,EAAA,CACC,MAAM,SACN,OAAOrD,GAAA,YAAAA,EAAe,YAAa,EACnC,OAAO,IACP,UAAW,EACX,WAAY,CACV,QAAQA,GAAA,YAAAA,EAAe,YAAa,GAAK,GAAK,UAAY,SAAA,CAC5D,CAAA,EAEFmC,EAAAA,IAACgC,EAAA,CACC,SAASnE,GAAA,YAAAA,EAAe,YAAa,EACrC,KAAK,QACL,SAASA,GAAA,YAAAA,EAAe,YAAa,GAAK,GAAK,YAAc,QAAA,CAAA,CAC/D,CAAA,CACF,CAAA,CACF,EACAmC,MAACgB,EAAA,CAAI,KAAM,EACT,gBAACC,EAAA,CACC,SAAA,CAAAjB,EAAAA,IAACkB,EAAA,CACC,MAAM,QACN,QAAOe,EAAApE,GAAA,YAAAA,EAAe,eAAf,YAAAoE,EAA6B,gBAAiB,EACrD,OAAO,IACP,UAAW,EACX,WAAY,CACV,SAAQC,EAAArE,GAAA,YAAAA,EAAe,eAAf,YAAAqE,EAA6B,gBAAiB,GAAK,GAAK,UAAY,SAAA,CAC9E,CAAA,EAEFlC,EAAAA,IAACgC,EAAA,CACC,UAASG,EAAAtE,GAAA,YAAAA,EAAe,eAAf,YAAAsE,EAA6B,gBAAiB,EACvD,KAAK,QACL,UAASC,EAAAvE,GAAA,YAAAA,EAAe,eAAf,YAAAuE,EAA6B,gBAAiB,GAAK,GAAK,YAAc,QAAA,CAAA,CACjF,CAAA,CACF,CAAA,CACF,EACApC,MAACgB,EAAA,CAAI,KAAM,EACT,eAACC,EAAA,CACC,SAAAjB,EAAAA,IAACkB,EAAA,CACC,MAAM,OACN,QAAOmB,EAAAxE,GAAA,YAAAA,EAAe,gBAAf,YAAAwE,EAA8B,kBAAmB,EACxD,aAASC,GAAA,CAAA,CAAgB,CAAA,CAAA,EAE7B,CAAA,CACF,CAAA,EACF,EAECvE,EAAO,OAAS,GACfiC,EAAAA,IAACiB,EAAA,CAAK,MAAM,OAAO,MAAO,CAAE,aAAc,MAAA,EACxC,eAACW,EAAA,CAAM,UAAU,WAAW,MAAO,CAAE,MAAO,QACzC,SAAA7D,EAAO,IAAI,CAACwE,EAAOf,IAClBxB,EAAAA,IAACsB,EAAA,CAEC,QAAS,GAAGiB,EAAM,QAAQ,KAAKA,EAAM,OAAO,GAC5C,KAAMA,EAAM,QAAU,WAAa,QAC7BA,EAAM,QAAU,UAAY,UAAY,OAC9C,SAAQ,GACR,SAAQ,EAAA,EALHf,CAAA,CAOR,EACH,CAAA,CACF,EAGFzB,EAAAA,KAACgB,EAAA,CAAI,OAAQ,GACX,SAAA,CAAAf,EAAAA,IAACgB,GAAI,KAAM,GACT,eAACC,EAAA,CAAK,MAAM,SACT,UAAAuB,EAAA3E,GAAA,YAAAA,EAAe,aAAf,YAAA2E,EAA2B,IAAI,CAACC,EAAWjB,IAC1CzB,EAAAA,KAAC,MAAA,CAAgB,MAAO,CAAE,aAAc,QACtC,SAAA,CAAAA,OAAC,OAAI,MAAO,CAAE,QAAS,OAAQ,eAAgB,iBAC7C,SAAA,CAAAC,EAAAA,IAAC/C,EAAA,CAAM,WAAK,IAAA,CAAK,SAChBA,EAAA,CAAM,SAAA,CAAAwF,EAAK,cAAc,QAAQ,CAAC,EAAE,GAAA,CAAA,CAAC,CAAA,EACxC,EACAzC,EAAAA,IAACgC,EAAA,CACC,QAASS,EAAK,cACd,KAAK,QACL,OAAQA,EAAK,cAAgB,GAAK,YAAc,QAAA,CAAA,CAClD,CAAA,EATQjB,CAUV,EACD,CACH,EACF,EACAxB,EAAAA,IAACgB,EAAA,CAAI,KAAM,GACT,SAAAhB,EAAAA,IAACiB,EAAA,CAAK,MAAM,OACV,SAAAlB,OAACgB,EAAA,CAAI,OAAQ,GACX,SAAA,CAAAf,EAAAA,IAACgB,EAAA,CAAI,KAAM,GACT,SAAAhB,EAAAA,IAACkB,EAAA,CACC,MAAM,OACN,QAAOwB,EAAA7E,GAAA,YAAAA,EAAe,gBAAf,YAAA6E,EAA8B,iBAAkB,EACvD,UAAYrB,GAAU,IAAI,OAAOA,CAAK,EAAI,KAAO,MAAM,QAAQ,CAAC,CAAC,KAAA,CAAA,EAErE,EACArB,EAAAA,IAACgB,EAAA,CAAI,KAAM,GACT,SAAAhB,EAAAA,IAACkB,EAAA,CACC,MAAM,OACN,QAAOyB,EAAA9E,GAAA,YAAAA,EAAe,gBAAf,YAAA8E,EAA8B,oBAAqB,EAC1D,UAAYtB,GAAU,IAAI,OAAOA,CAAK,EAAI,KAAO,MAAM,QAAQ,CAAC,CAAC,KAAA,CAAA,CACnE,CACF,CAAA,CAAA,CACF,EACF,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EAxGsB,YAyGxB,EAEArB,EAAAA,IAACjD,EAAA,CAAQ,IAAI,OACX,SAAAiD,EAAAA,IAACiB,EAAA,CACC,MAAM,WACN,MACEjB,EAAAA,IAACyB,EAAA,CACC,WAAOM,EAAA,EAAiB,EACxB,QAASvC,EACT,QAAAvB,EACD,SAAA,MAAA,CAAA,EAKF,SAAA,OAAO,KAAKR,CAAkB,EAAE,OAAS,EACxCuC,EAAAA,IAAC2B,EAAA,CACC,WAAYd,GACZ,QAASJ,GACT,WAAY,EAAA,CAAA,SAGb,MAAA,CAAI,MAAO,CAAE,UAAW,SAAU,QAAS,QAAA,EAC1C,SAAA,CAAAT,MAAC+B,GAAiB,MAAO,CAAE,SAAU,OAAQ,MAAO,WAAa,EACjE/B,EAAAA,IAAC,KAAE,MAAO,CAAE,MAAO,OAAQ,UAAW,MAAA,EAAU,SAAA,kBAAA,CAEhD,CAAA,CAAA,CACF,CAAA,CAAA,GAzBkB,aA4BxB,CAAA,EACF,EAGAA,EAAAA,IAAC4C,EAAA,CACC,MAAM,QACN,KAAMzE,GACN,SAAU,IAAMC,EAAwB,EAAK,EAC7C,OAAQ,KAER,SAAA2B,EAAAA,KAACvB,EAAA,CACC,KAAAD,GACA,OAAO,WACP,SAAUkB,GACV,cAAe,CACb,oBAAqB,GACrB,mBAAoB,GACpB,gBAAiB,IACjB,wBAAyB,GACzB,oBAAqB,EAAA,EAGvB,SAAA,CAAAO,EAAAA,IAACxB,EAAK,KAAL,CACC,MAAM,SACN,KAAK,sBACL,cAAc,UAEd,eAACqE,EAAA,CAAA,CAAO,CAAA,CAAA,EAGV7C,EAAAA,IAACxB,EAAK,KAAL,CACC,MAAM,SACN,KAAK,qBAEL,SAAAwB,EAAAA,IAAC8C,EAAA,CAAY,IAAK,EAAG,IAAK,GAAA,CAAK,CAAA,CAAA,EAGjC9C,EAAAA,IAACxB,EAAK,KAAL,CACC,MAAM,iBACN,KAAK,kBAEL,SAAAwB,EAAAA,IAAC8C,EAAA,CAAY,IAAK,GAAI,IAAK,GAAA,CAAM,CAAA,CAAA,EAGnC9C,EAAAA,IAACxB,EAAK,KAAL,CACC,MAAM,YACN,KAAK,0BACL,cAAc,UAEd,eAACqE,EAAA,CAAA,CAAO,CAAA,CAAA,EAGV7C,EAAAA,IAACxB,EAAK,KAAL,CACC,MAAM,SACN,KAAK,sBACL,aAAc,CAAC,yBAAyB,EAExC,SAAAwB,EAAAA,IAAC8C,EAAA,CAAY,IAAK,EAAG,IAAK,GAAA,CAAK,CAAA,CAAA,QAGhCtE,EAAK,KAAL,CAAU,MAAO,CAAE,aAAc,CAAA,EAChC,SAAAuB,EAAAA,KAAC6B,GAAM,MAAO,CAAE,MAAO,OAAQ,eAAgB,YAC7C,SAAA,CAAA5B,MAACyB,GAAO,QAAS,IAAMrD,EAAwB,EAAK,EAAG,SAAA,KAEvD,QACCqD,EAAA,CAAO,KAAK,UAAU,SAAS,SAAS,QAAAxD,EAAkB,SAAA,MAAA,CAE3D,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAAA,EAIF+B,EAAAA,IAAC4C,EAAA,CACC,MAAM,SACN,KAAMvE,GACN,SAAU,IAAMC,EAAuB,EAAK,EAC5C,OAAQ,KAER,SAAAyB,EAAAA,KAACvB,EAAA,CACC,OAAO,WACP,SAAUmB,GACV,cAAe,CAAE,eAAgB,EAAA,EAEjC,SAAA,CAAAK,EAAAA,IAACsB,EAAA,CACC,QAAQ,KACR,YAAY,oCACZ,KAAK,UACL,SAAQ,GACR,MAAO,CAAE,aAAc,MAAA,CAAO,CAAA,EAGhCtB,EAAAA,IAACxB,EAAK,KAAL,CACC,MAAM,OACN,KAAK,iBACL,KAAK,kBAEL,SAAAwB,EAAAA,IAAC8C,EAAA,CAAY,IAAK,EAAG,IAAK,GAAA,CAAK,CAAA,CAAA,QAGhCtE,EAAK,KAAL,CAAU,MAAO,CAAE,aAAc,CAAA,EAChC,SAAAuB,EAAAA,KAAC6B,GAAM,MAAO,CAAE,MAAO,OAAQ,eAAgB,YAC7C,SAAA,CAAA5B,MAACyB,GAAO,QAAS,IAAMnD,EAAuB,EAAK,EAAG,SAAA,KAEtD,EACA0B,EAAAA,IAACyB,GAAO,KAAK,UAAU,OAAM,GAAC,SAAS,SAAS,QAAAxD,EAAkB,SAAA,MAAA,CAElE,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAAA,CACF,EACF,CAEJ"}