{"version": 3, "file": "TaskList-Bttm5b5J.js", "sources": ["../../src/pages/Tasks/TaskList.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { \n  Table, \n  Button, \n  Space, \n  Tag, \n  Progress, \n  Typography, \n  Input, \n  Select, \n  Card,\n  Popconfirm,\n  message,\n  Tooltip,\n  Badge,\n} from 'antd';\nimport { useNavigate } from 'react-router-dom';\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport {\n  PlusOutlined,\n  ReloadOutlined,\n  PlayCircleOutlined,\n  PauseCircleOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  SearchOutlined,\n  FilterOutlined,\n} from '@ant-design/icons';\nimport dayjs from 'dayjs';\n\nimport { taskApi } from '@/api';\nimport { useTaskStore } from '@/hooks/useStore';\nimport { ScanTask, TaskStatus, ScanType } from '@/types';\n\nconst { Title, Text } = Typography;\nconst { Search } = Input;\nconst { Option } = Select;\n\nconst TaskList: React.FC = () => {\n  const navigate = useNavigate();\n  const queryClient = useQueryClient();\n  const { tasks: _tasks, setTasks: _setTasks } = useTaskStore(); // 暂时未使用，使用下划线前缀\n  \n  const [searchText, setSearchText] = useState('');\n  const [statusFilter, setStatusFilter] = useState<TaskStatus | 'all'>('all');\n  const [typeFilter, setTypeFilter] = useState<ScanType | 'all'>('all');\n\n  // 获取任务列表\n  const { data, isLoading, refetch } = useQuery({\n    queryKey: ['tasks'],\n    queryFn: () => taskApi.getTasks(1, 100),\n    refetchInterval: 10000, // 每10秒刷新\n  });\n\n  // 启动任务\n  const startTaskMutation = useMutation({\n    mutationFn: taskApi.startTask,\n    onSuccess: () => {\n      message.success('任务已启动');\n      queryClient.invalidateQueries({ queryKey: ['tasks'] });\n    },\n    onError: (error: any) => {\n      message.error('启动任务失败: ' + error.message);\n    },\n  });\n\n  // 停止任务\n  const stopTaskMutation = useMutation({\n    mutationFn: taskApi.stopTask,\n    onSuccess: () => {\n      message.success('任务已停止');\n      queryClient.invalidateQueries({ queryKey: ['tasks'] });\n    },\n    onError: (error: any) => {\n      message.error('停止任务失败: ' + error.message);\n    },\n  });\n\n  // 删除任务\n  const deleteTaskMutation = useMutation({\n    mutationFn: taskApi.deleteTask,\n    onSuccess: () => {\n      message.success('任务已删除');\n      queryClient.invalidateQueries({ queryKey: ['tasks'] });\n    },\n    onError: (error: any) => {\n      message.error('删除任务失败: ' + error.message);\n    },\n  });\n\n  // 重启任务\n  const restartTaskMutation = useMutation({\n    mutationFn: taskApi.restartTask,\n    onSuccess: () => {\n      message.success('任务已重启');\n      queryClient.invalidateQueries({ queryKey: ['tasks'] });\n    },\n    onError: (error: any) => {\n      message.error('重启任务失败: ' + error.message);\n    },\n  });\n\n  const taskData = data?.data?.data || [];\n\n  // 过滤任务\n  const filteredTasks = taskData.filter((task: ScanTask) => {\n    const matchesSearch = task.target.toLowerCase().includes(searchText.toLowerCase()) ||\n                         task.id.toLowerCase().includes(searchText.toLowerCase());\n    const matchesStatus = statusFilter === 'all' || task.status === statusFilter;\n    const matchesType = typeFilter === 'all' || task.task_type === typeFilter;\n    \n    return matchesSearch && matchesStatus && matchesType;\n  });\n\n  // 状态标签颜色\n  const getStatusColor = (status: TaskStatus) => {\n    switch (status) {\n      case TaskStatus.Pending:\n        return 'default';\n      case TaskStatus.Running:\n        return 'processing';\n      case TaskStatus.Completed:\n        return 'success';\n      case TaskStatus.Failed:\n        return 'error';\n      case TaskStatus.Stopped:\n        return 'warning';\n      default:\n        return 'default';\n    }\n  };\n\n  // 任务类型标签颜色\n  const getTypeColor = (type: ScanType) => {\n    switch (type) {\n      case ScanType.Quick:\n        return 'green';\n      case ScanType.Standard:\n        return 'blue';\n      case ScanType.Deep:\n        return 'purple';\n      case ScanType.WebFocused:\n        return 'orange';\n      default:\n        return 'default';\n    }\n  };\n\n  // 表格列定义\n  const columns = [\n    {\n      title: '任务ID',\n      dataIndex: 'id',\n      key: 'id',\n      width: 120,\n      render: (id: string) => (\n        <Text code style={{ fontSize: 12 }}>\n          {id.slice(-8)}\n        </Text>\n      ),\n    },\n    {\n      title: '扫描目标',\n      dataIndex: 'target',\n      key: 'target',\n      ellipsis: true,\n      render: (target: string) => (\n        <Tooltip title={target}>\n          <Text>{target}</Text>\n        </Tooltip>\n      ),\n    },\n    {\n      title: '扫描类型',\n      dataIndex: 'task_type',\n      key: 'task_type',\n      width: 100,\n      render: (type: ScanType) => (\n        <Tag color={getTypeColor(type)}>\n          {type === ScanType.Quick && '快速'}\n          {type === ScanType.Standard && '标准'}\n          {type === ScanType.Deep && '深度'}\n          {type === ScanType.WebFocused && 'Web'}\n          {type === ScanType.Custom && '自定义'}\n        </Tag>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: TaskStatus, record: ScanTask) => (\n        <Space>\n          <Badge \n            status={\n              status === TaskStatus.Running ? 'processing' :\n              status === TaskStatus.Completed ? 'success' :\n              status === TaskStatus.Failed ? 'error' : 'default'\n            } \n          />\n          <Tag color={getStatusColor(status)}>\n            {status === TaskStatus.Pending && '等待中'}\n            {status === TaskStatus.Running && '运行中'}\n            {status === TaskStatus.Completed && '已完成'}\n            {status === TaskStatus.Failed && '失败'}\n            {status === TaskStatus.Stopped && '已停止'}\n          </Tag>\n        </Space>\n      ),\n    },\n    {\n      title: '进度',\n      dataIndex: 'progress',\n      key: 'progress',\n      width: 120,\n      render: (progress: number, record: ScanTask) => (\n        <Progress \n          percent={progress} \n          size=\"small\" \n          status={\n            record.status === TaskStatus.Failed ? 'exception' :\n            record.status === TaskStatus.Completed ? 'success' : 'active'\n          }\n        />\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'created_at',\n      key: 'created_at',\n      width: 160,\n      render: (time: string) => (\n        <Text>{dayjs(time).format('YYYY-MM-DD HH:mm')}</Text>\n      ),\n    },\n    {\n      title: '耗时',\n      key: 'duration',\n      width: 100,\n      render: (record: ScanTask) => {\n        if (!record.started_at) return <Text type=\"secondary\">-</Text>;\n        \n        const endTime = record.completed_at || new Date().toISOString();\n        const duration = dayjs(endTime).diff(dayjs(record.started_at), 'second');\n        \n        if (duration < 60) {\n          return <Text>{duration}秒</Text>;\n        } else if (duration < 3600) {\n          return <Text>{Math.floor(duration / 60)}分钟</Text>;\n        } else {\n          return <Text>{Math.floor(duration / 3600)}小时</Text>;\n        }\n      },\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      render: (record: ScanTask) => (\n        <Space size=\"small\">\n          <Tooltip title=\"查看详情\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              size=\"small\"\n              onClick={() => navigate(`/tasks/${record.id}`)}\n            />\n          </Tooltip>\n          \n          {record.status === TaskStatus.Pending && (\n            <Tooltip title=\"启动任务\">\n              <Button\n                type=\"text\"\n                icon={<PlayCircleOutlined />}\n                size=\"small\"\n                loading={startTaskMutation.isPending}\n                onClick={() => startTaskMutation.mutate(record.id)}\n              />\n            </Tooltip>\n          )}\n\n          {record.status === TaskStatus.Running && (\n            <Tooltip title=\"停止任务\">\n              <Button\n                type=\"text\"\n                icon={<PauseCircleOutlined />}\n                size=\"small\"\n                loading={stopTaskMutation.isPending}\n                onClick={() => stopTaskMutation.mutate(record.id)}\n              />\n            </Tooltip>\n          )}\n\n          {(record.status === TaskStatus.Failed || record.status === TaskStatus.Stopped) && (\n            <Tooltip title=\"重启任务\">\n              <Button\n                type=\"text\"\n                icon={<PlayCircleOutlined />}\n                size=\"small\"\n                loading={restartTaskMutation.isPending}\n                onClick={() => restartTaskMutation.mutate(record.id)}\n              />\n            </Tooltip>\n          )}\n          \n          <Popconfirm\n            title=\"确定删除此任务？\"\n            description=\"删除后将无法恢复任务数据\"\n            onConfirm={() => deleteTaskMutation.mutate(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Tooltip title=\"删除任务\">\n              <Button\n                type=\"text\"\n                icon={<DeleteOutlined />}\n                size=\"small\"\n                danger\n                loading={deleteTaskMutation.isPending}\n              />\n            </Tooltip>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div style={{ padding: 24 }}>\n      <Card>\n        {/* 页面标题和操作 */}\n        <div style={{ \n          display: 'flex', \n          justifyContent: 'space-between', \n          alignItems: 'center',\n          marginBottom: 24 \n        }}>\n          <div>\n            <Title level={3} style={{ margin: 0 }}>\n              扫描任务\n            </Title>\n            <Text type=\"secondary\">\n              管理和监控所有扫描任务\n            </Text>\n          </div>\n          \n          <Space>\n            <Button\n              icon={<ReloadOutlined />}\n              onClick={() => refetch()}\n              loading={isLoading}\n            >\n              刷新\n            </Button>\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={() => navigate('/tasks/create')}\n            >\n              创建任务\n            </Button>\n          </Space>\n        </div>\n\n        {/* 过滤器 */}\n        <div style={{ \n          display: 'flex', \n          gap: 16, \n          marginBottom: 16,\n          flexWrap: 'wrap' \n        }}>\n          <Search\n            placeholder=\"搜索任务ID或目标\"\n            allowClear\n            style={{ width: 300 }}\n            value={searchText}\n            onChange={(e) => setSearchText(e.target.value)}\n            prefix={<SearchOutlined />}\n          />\n          \n          <Select\n            placeholder=\"任务状态\"\n            style={{ width: 120 }}\n            value={statusFilter}\n            onChange={setStatusFilter}\n          >\n            <Option value=\"all\">全部状态</Option>\n            <Option value={TaskStatus.Pending}>等待中</Option>\n            <Option value={TaskStatus.Running}>运行中</Option>\n            <Option value={TaskStatus.Completed}>已完成</Option>\n            <Option value={TaskStatus.Failed}>失败</Option>\n            <Option value={TaskStatus.Stopped}>已停止</Option>\n          </Select>\n          \n          <Select\n            placeholder=\"扫描类型\"\n            style={{ width: 120 }}\n            value={typeFilter}\n            onChange={setTypeFilter}\n          >\n            <Option value=\"all\">全部类型</Option>\n            <Option value={ScanType.Quick}>快速扫描</Option>\n            <Option value={ScanType.Standard}>标准扫描</Option>\n            <Option value={ScanType.Deep}>深度扫描</Option>\n            <Option value={ScanType.WebFocused}>Web扫描</Option>\n          </Select>\n        </div>\n\n        {/* 任务表格 */}\n        <Table\n          columns={columns}\n          dataSource={filteredTasks}\n          rowKey=\"id\"\n          loading={isLoading}\n          pagination={{\n            total: filteredTasks.length,\n            pageSize: 20,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 条记录`,\n          }}\n          scroll={{ x: 1200 }}\n        />\n      </Card>\n    </div>\n  );\n};\n\nexport default TaskList;"], "names": ["Title", "Text", "Typography", "Search", "Input", "Option", "Select", "TaskList", "navigate", "useNavigate", "queryClient", "useQueryClient", "_tasks", "_setTasks", "useTaskStore", "searchText", "setSearchText", "useState", "statusFilter", "setStatus<PERSON>ilter", "typeFilter", "setTypeFilter", "data", "isLoading", "refetch", "useQuery", "taskApi", "startTaskMutation", "useMutation", "message", "error", "stopTaskMutation", "deleteTaskMutation", "restartTaskMutation", "filteredTasks", "_a", "task", "matchesSearch", "matchesStatus", "matchesType", "getStatusColor", "status", "TaskStatus", "getTypeColor", "type", "ScanType", "columns", "id", "jsx", "target", "<PERSON><PERSON><PERSON>", "jsxs", "Tag", "record", "Space", "Badge", "progress", "Progress", "time", "endTime", "duration", "dayjs", "<PERSON><PERSON>", "EyeOutlined", "PlayCircleOutlined", "PauseCircleOutlined", "Popconfirm", "DeleteOutlined", "Card", "ReloadOutlined", "PlusOutlined", "SearchOutlined", "Table", "total"], "mappings": "kYAkCA,KAAM,CAAE,MAAAA,GAAO,KAAAC,CAAA,EAASC,EAClB,CAAE,OAAAC,IAAWC,EACb,CAAE,OAAAC,GAAWC,EAEbC,GAAqB,IAAM,OAC/B,MAAMC,EAAWC,EAAA,EACXC,EAAcC,EAAA,EACd,CAAE,MAAOC,GAAQ,SAAUC,EAAA,EAAcC,EAAA,EAEzC,CAACC,EAAYC,CAAa,EAAIC,EAAAA,SAAS,EAAE,EACzC,CAACC,EAAcC,CAAe,EAAIF,EAAAA,SAA6B,KAAK,EACpE,CAACG,EAAYC,CAAa,EAAIJ,EAAAA,SAA2B,KAAK,EAG9D,CAAE,KAAAK,EAAM,UAAAC,EAAW,QAAAC,CAAA,EAAYC,EAAS,CAC5C,SAAU,CAAC,OAAO,EAClB,QAAS,IAAMC,EAAQ,SAAS,EAAG,GAAG,EACtC,gBAAiB,GAAA,CAClB,EAGKC,EAAoBC,EAAY,CACpC,WAAYF,EAAQ,UACpB,UAAW,IAAM,CACfG,EAAQ,QAAQ,OAAO,EACvBnB,EAAY,kBAAkB,CAAE,SAAU,CAAC,OAAO,EAAG,CACvD,EACA,QAAUoB,GAAe,CACvBD,EAAQ,MAAM,WAAaC,EAAM,OAAO,CAC1C,CAAA,CACD,EAGKC,EAAmBH,EAAY,CACnC,WAAYF,EAAQ,SACpB,UAAW,IAAM,CACfG,EAAQ,QAAQ,OAAO,EACvBnB,EAAY,kBAAkB,CAAE,SAAU,CAAC,OAAO,EAAG,CACvD,EACA,QAAUoB,GAAe,CACvBD,EAAQ,MAAM,WAAaC,EAAM,OAAO,CAC1C,CAAA,CACD,EAGKE,EAAqBJ,EAAY,CACrC,WAAYF,EAAQ,WACpB,UAAW,IAAM,CACfG,EAAQ,QAAQ,OAAO,EACvBnB,EAAY,kBAAkB,CAAE,SAAU,CAAC,OAAO,EAAG,CACvD,EACA,QAAUoB,GAAe,CACvBD,EAAQ,MAAM,WAAaC,EAAM,OAAO,CAC1C,CAAA,CACD,EAGKG,EAAsBL,EAAY,CACtC,WAAYF,EAAQ,YACpB,UAAW,IAAM,CACfG,EAAQ,QAAQ,OAAO,EACvBnB,EAAY,kBAAkB,CAAE,SAAU,CAAC,OAAO,EAAG,CACvD,EACA,QAAUoB,GAAe,CACvBD,EAAQ,MAAM,WAAaC,EAAM,OAAO,CAC1C,CAAA,CACD,EAKKI,KAHWC,EAAAb,GAAA,YAAAA,EAAM,OAAN,YAAAa,EAAY,OAAQ,CAAA,GAGN,OAAQC,GAAmB,CACxD,MAAMC,EAAgBD,EAAK,OAAO,YAAA,EAAc,SAASrB,EAAW,YAAA,CAAa,GAC5DqB,EAAK,GAAG,YAAA,EAAc,SAASrB,EAAW,aAAa,EACtEuB,EAAgBpB,IAAiB,OAASkB,EAAK,SAAWlB,EAC1DqB,EAAcnB,IAAe,OAASgB,EAAK,YAAchB,EAE/D,OAAOiB,GAAiBC,GAAiBC,CAC3C,CAAC,EAGKC,EAAkBC,GAAuB,CAC7C,OAAQA,EAAA,CACN,KAAKC,EAAW,QACd,MAAO,UACT,KAAKA,EAAW,QACd,MAAO,aACT,KAAKA,EAAW,UACd,MAAO,UACT,KAAKA,EAAW,OACd,MAAO,QACT,KAAKA,EAAW,QACd,MAAO,UACT,QACE,MAAO,SAAA,CAEb,EAGMC,EAAgBC,GAAmB,CACvC,OAAQA,EAAA,CACN,KAAKC,EAAS,MACZ,MAAO,QACT,KAAKA,EAAS,SACZ,MAAO,OACT,KAAKA,EAAS,KACZ,MAAO,SACT,KAAKA,EAAS,WACZ,MAAO,SACT,QACE,MAAO,SAAA,CAEb,EAGMC,EAAU,CACd,CACE,MAAO,OACP,UAAW,KACX,IAAK,KACL,MAAO,IACP,OAASC,GACPC,EAAAA,IAAC/C,GAAK,KAAI,GAAC,MAAO,CAAE,SAAU,EAAA,EAC3B,SAAA8C,EAAG,MAAM,EAAE,CAAA,CACd,CAAA,EAGJ,CACE,MAAO,OACP,UAAW,SACX,IAAK,SACL,SAAU,GACV,OAASE,GACPD,EAAAA,IAACE,EAAA,CAAQ,MAAOD,EACd,SAAAD,EAAAA,IAAC/C,EAAA,CAAM,SAAAgD,CAAA,CAAO,CAAA,CAChB,CAAA,EAGJ,CACE,MAAO,OACP,UAAW,YACX,IAAK,YACL,MAAO,IACP,OAASL,GACPO,EAAAA,KAACC,GAAI,MAAOT,EAAaC,CAAI,EAC1B,SAAA,CAAAA,IAASC,EAAS,OAAS,KAC3BD,IAASC,EAAS,UAAY,KAC9BD,IAASC,EAAS,MAAQ,KAC1BD,IAASC,EAAS,YAAc,MAChCD,IAASC,EAAS,QAAU,KAAA,CAAA,CAC/B,CAAA,EAGJ,CACE,MAAO,KACP,UAAW,SACX,IAAK,SACL,MAAO,IACP,OAAQ,CAACJ,EAAoBY,WAC1BC,EAAA,CACC,SAAA,CAAAN,EAAAA,IAACO,EAAA,CACC,OACEd,IAAWC,EAAW,QAAU,aAChCD,IAAWC,EAAW,UAAY,UAClCD,IAAWC,EAAW,OAAS,QAAU,SAAA,CAAA,EAG7CS,EAAAA,KAACC,EAAA,CAAI,MAAOZ,EAAeC,CAAM,EAC9B,SAAA,CAAAA,IAAWC,EAAW,SAAW,MACjCD,IAAWC,EAAW,SAAW,MACjCD,IAAWC,EAAW,WAAa,MACnCD,IAAWC,EAAW,QAAU,KAChCD,IAAWC,EAAW,SAAW,KAAA,CAAA,CACpC,CAAA,CAAA,CACF,CAAA,EAGJ,CACE,MAAO,KACP,UAAW,WACX,IAAK,WACL,MAAO,IACP,OAAQ,CAACc,EAAkBH,IACzBL,EAAAA,IAACS,EAAA,CACC,QAASD,EACT,KAAK,QACL,OACEH,EAAO,SAAWX,EAAW,OAAS,YACtCW,EAAO,SAAWX,EAAW,UAAY,UAAY,QAAA,CAAA,CAEzD,EAGJ,CACE,MAAO,OACP,UAAW,aACX,IAAK,aACL,MAAO,IACP,OAASgB,GACPV,EAAAA,IAAC/C,EAAA,CAAM,WAAMyD,CAAI,EAAE,OAAO,kBAAkB,CAAA,CAAE,CAAA,EAGlD,CACE,MAAO,KACP,IAAK,WACL,MAAO,IACP,OAASL,GAAqB,CAC5B,GAAI,CAACA,EAAO,WAAY,aAAQpD,EAAA,CAAK,KAAK,YAAY,SAAA,IAAC,EAEvD,MAAM0D,EAAUN,EAAO,cAAgB,IAAI,KAAA,EAAO,YAAA,EAC5CO,EAAWC,EAAMF,CAAO,EAAE,KAAKE,EAAMR,EAAO,UAAU,EAAG,QAAQ,EAEvE,OAAIO,EAAW,UACL3D,EAAA,CAAM,SAAA,CAAA2D,EAAS,GAAA,EAAC,EACfA,EAAW,YACZ3D,EAAA,CAAM,SAAA,CAAA,KAAK,MAAM2D,EAAW,EAAE,EAAE,IAAA,EAAE,SAElC3D,EAAA,CAAM,SAAA,CAAA,KAAK,MAAM2D,EAAW,IAAI,EAAE,IAAA,EAAE,CAEhD,CAAA,EAEF,CACE,MAAO,KACP,IAAK,SACL,MAAO,IACP,OAASP,GACPF,EAAAA,KAACG,EAAA,CAAM,KAAK,QACV,SAAA,CAAAN,EAAAA,IAACE,EAAA,CAAQ,MAAM,OACb,SAAAF,EAAAA,IAACc,EAAA,CACC,KAAK,OACL,WAAOC,EAAA,EAAY,EACnB,KAAK,QACL,QAAS,IAAMvD,EAAS,UAAU6C,EAAO,EAAE,EAAE,CAAA,CAAA,EAEjD,EAECA,EAAO,SAAWX,EAAW,SAC5BM,EAAAA,IAACE,EAAA,CAAQ,MAAM,OACb,SAAAF,EAAAA,IAACc,EAAA,CACC,KAAK,OACL,WAAOE,EAAA,EAAmB,EAC1B,KAAK,QACL,QAASrC,EAAkB,UAC3B,QAAS,IAAMA,EAAkB,OAAO0B,EAAO,EAAE,CAAA,CAAA,EAErD,EAGDA,EAAO,SAAWX,EAAW,SAC5BM,EAAAA,IAACE,EAAA,CAAQ,MAAM,OACb,SAAAF,EAAAA,IAACc,EAAA,CACC,KAAK,OACL,WAAOG,GAAA,EAAoB,EAC3B,KAAK,QACL,QAASlC,EAAiB,UAC1B,QAAS,IAAMA,EAAiB,OAAOsB,EAAO,EAAE,CAAA,CAAA,EAEpD,GAGAA,EAAO,SAAWX,EAAW,QAAUW,EAAO,SAAWX,EAAW,UACpEM,EAAAA,IAACE,EAAA,CAAQ,MAAM,OACb,SAAAF,EAAAA,IAACc,EAAA,CACC,KAAK,OACL,WAAOE,EAAA,EAAmB,EAC1B,KAAK,QACL,QAAS/B,EAAoB,UAC7B,QAAS,IAAMA,EAAoB,OAAOoB,EAAO,EAAE,CAAA,CAAA,EAEvD,EAGFL,EAAAA,IAACkB,GAAA,CACC,MAAM,WACN,YAAY,eACZ,UAAW,IAAMlC,EAAmB,OAAOqB,EAAO,EAAE,EACpD,OAAO,KACP,WAAW,KAEX,SAAAL,EAAAA,IAACE,EAAA,CAAQ,MAAM,OACb,SAAAF,EAAAA,IAACc,EAAA,CACC,KAAK,OACL,WAAOK,GAAA,EAAe,EACtB,KAAK,QACL,OAAM,GACN,QAASnC,EAAmB,SAAA,CAAA,CAC9B,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CAEJ,EAGF,OACEgB,MAAC,OAAI,MAAO,CAAE,QAAS,EAAA,EACrB,gBAACoB,EAAA,CAEC,SAAA,CAAAjB,OAAC,OAAI,MAAO,CACV,QAAS,OACT,eAAgB,gBAChB,WAAY,SACZ,aAAc,EAAA,EAEd,SAAA,CAAAA,OAAC,MAAA,CACC,SAAA,CAAAH,EAAAA,IAAChD,GAAA,CAAM,MAAO,EAAG,MAAO,CAAE,OAAQ,CAAA,EAAK,SAAA,MAAA,CAEvC,EACAgD,EAAAA,IAAC/C,EAAA,CAAK,KAAK,YAAY,SAAA,aAAA,CAEvB,CAAA,EACF,SAECqD,EAAA,CACC,SAAA,CAAAN,EAAAA,IAACc,EAAA,CACC,WAAOO,EAAA,EAAe,EACtB,QAAS,IAAM7C,EAAA,EACf,QAASD,EACV,SAAA,IAAA,CAAA,EAGDyB,EAAAA,IAACc,EAAA,CACC,KAAK,UACL,WAAOQ,EAAA,EAAa,EACpB,QAAS,IAAM9D,EAAS,eAAe,EACxC,SAAA,MAAA,CAAA,CAED,CAAA,CACF,CAAA,EACF,EAGA2C,OAAC,OAAI,MAAO,CACV,QAAS,OACT,IAAK,GACL,aAAc,GACd,SAAU,MAAA,EAEV,SAAA,CAAAH,EAAAA,IAAC7C,GAAA,CACC,YAAY,YACZ,WAAU,GACV,MAAO,CAAE,MAAO,GAAA,EAChB,MAAOY,EACP,SAAW,GAAMC,EAAc,EAAE,OAAO,KAAK,EAC7C,aAASuD,EAAA,CAAA,CAAe,CAAA,CAAA,EAG1BpB,EAAAA,KAAC7C,EAAA,CACC,YAAY,OACZ,MAAO,CAAE,MAAO,GAAA,EAChB,MAAOY,EACP,SAAUC,EAEV,SAAA,CAAA6B,EAAAA,IAAC3C,EAAA,CAAO,MAAM,MAAM,SAAA,OAAI,EACxB2C,EAAAA,IAAC3C,EAAA,CAAO,MAAOqC,EAAW,QAAS,SAAA,MAAG,EACtCM,EAAAA,IAAC3C,EAAA,CAAO,MAAOqC,EAAW,QAAS,SAAA,MAAG,EACtCM,EAAAA,IAAC3C,EAAA,CAAO,MAAOqC,EAAW,UAAW,SAAA,MAAG,EACxCM,EAAAA,IAAC3C,EAAA,CAAO,MAAOqC,EAAW,OAAQ,SAAA,KAAE,EACpCM,EAAAA,IAAC3C,EAAA,CAAO,MAAOqC,EAAW,QAAS,SAAA,KAAA,CAAG,CAAA,CAAA,CAAA,EAGxCS,EAAAA,KAAC7C,EAAA,CACC,YAAY,OACZ,MAAO,CAAE,MAAO,GAAA,EAChB,MAAOc,EACP,SAAUC,EAEV,SAAA,CAAA2B,EAAAA,IAAC3C,EAAA,CAAO,MAAM,MAAM,SAAA,OAAI,EACxB2C,EAAAA,IAAC3C,EAAA,CAAO,MAAOwC,EAAS,MAAO,SAAA,OAAI,EACnCG,EAAAA,IAAC3C,EAAA,CAAO,MAAOwC,EAAS,SAAU,SAAA,OAAI,EACtCG,EAAAA,IAAC3C,EAAA,CAAO,MAAOwC,EAAS,KAAM,SAAA,OAAI,EAClCG,EAAAA,IAAC3C,EAAA,CAAO,MAAOwC,EAAS,WAAY,SAAA,OAAA,CAAK,CAAA,CAAA,CAAA,CAC3C,EACF,EAGAG,EAAAA,IAACwB,EAAA,CACC,QAAA1B,EACA,WAAYZ,EACZ,OAAO,KACP,QAASX,EACT,WAAY,CACV,MAAOW,EAAc,OACrB,SAAU,GACV,gBAAiB,GACjB,gBAAiB,GACjB,UAAYuC,GAAU,KAAKA,CAAK,MAAA,EAElC,OAAQ,CAAE,EAAG,IAAA,CAAK,CAAA,CACpB,CAAA,CACF,CAAA,CACF,CAEJ"}