import axios, { AxiosResponse } from 'axios';
import type {
  ApiResponse,
  PaginatedResponse,
  Task,
  CreateTaskRequest,
  Host,
  Port,
  Subdomain,
  WebAsset,
  CrawledPage,
  Vulnerability,
  ScanStatistics,
  SystemConfig,
  LoginRequest,
  LoginResponse,
  CreateUserRequest,
  UpdateUserRequest,
  ChangePasswordRequest,
  UserInfo,
} from '@/types';

// 创建 axios 实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 从localStorage获取token并添加到请求头
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Error:', error);
    // 如果是401错误，清除token并重定向到登录页
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// 任务管理 API
export const taskApi = {
  // 获取任务列表
  getTasks: (page = 1, size = 20): Promise<AxiosResponse<ApiResponse<Task[]>>> =>
    api.get('/tasks', { params: { page, size } }),

  // 获取单个任务
  getTask: (taskId: string): Promise<AxiosResponse<ApiResponse<Task>>> =>
    api.get(`/tasks/${taskId}`),

  // 创建新任务
  createTask: (task: CreateTaskRequest): Promise<AxiosResponse<ApiResponse<Task>>> =>
    api.post('/tasks', task),

  // 启动任务
  startTask: (taskId: string): Promise<AxiosResponse<ApiResponse<void>>> =>
    api.post(`/tasks/${taskId}/start`),

  // 停止任务
  stopTask: (taskId: string): Promise<AxiosResponse<ApiResponse<void>>> =>
    api.post(`/tasks/${taskId}/stop`),

  // 删除任务
  deleteTask: (taskId: string): Promise<AxiosResponse<ApiResponse<void>>> =>
    api.delete(`/tasks/${taskId}`),

  // 重启任务
  restartTask: (taskId: string): Promise<AxiosResponse<ApiResponse<Task>>> =>
    api.post(`/tasks/${taskId}/restart`),
};

// 结果查询 API
export const resultsApi = {
  // 获取主机列表
  getHosts: (params?: string): Promise<AxiosResponse<PaginatedResponse<Host>>> =>
    api.get('/results/hosts' + (params ? `?${params}` : '')),

  // 获取端口列表
  getPorts: (params?: string): Promise<AxiosResponse<PaginatedResponse<Port>>> =>
    api.get('/results/ports' + (params ? `?${params}` : '')),

  // 获取子域名列表
  getSubdomains: (params?: string): Promise<AxiosResponse<PaginatedResponse<Subdomain>>> =>
    api.get('/results/subdomains' + (params ? `?${params}` : '')),

  // 获取Web资产列表
  getWebAssets: (params?: string): Promise<AxiosResponse<PaginatedResponse<WebAsset>>> =>
    api.get('/results/web-assets' + (params ? `?${params}` : '')),

  // 获取爬取页面列表
  getCrawledPages: (params?: string): Promise<AxiosResponse<PaginatedResponse<CrawledPage>>> =>
    api.get('/results/crawled-pages' + (params ? `?${params}` : '')),

  // 获取漏洞列表
  getVulnerabilities: (params?: string): Promise<AxiosResponse<PaginatedResponse<Vulnerability>>> =>
    api.get('/results/vulnerabilities' + (params ? `?${params}` : '')),

  // 获取统计信息
  getStatistics: (taskId?: string): Promise<AxiosResponse<ApiResponse<ScanStatistics>>> =>
    api.get('/results/statistics', { params: { task_id: taskId } }),

  // 导出结果
  exportResults: (taskId: string, format: string): Promise<AxiosResponse<Blob>> =>
    api.get(`/results/${taskId}/export`, {
      params: { format },
      responseType: 'blob',
    }),
};

// 配置管理 API
export const configApi = {
  // 获取系统配置
  getConfig: (): Promise<AxiosResponse<ApiResponse<SystemConfig>>> => api.get('/config'),

  // 更新系统配置
  updateConfig: (config: Partial<SystemConfig>): Promise<AxiosResponse<ApiResponse<SystemConfig>>> =>
    api.put('/config', config),

  // 获取默认扫描配置
  getDefaultScanConfig: (scanMode: string): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get(`/config/scan-defaults/${scanMode}`),
};

// 系统信息 API
export const systemApi = {
  // 获取系统状态
  getStatus: (): Promise<AxiosResponse<ApiResponse<any>>> => api.get('/system/status'),

  // 获取系统信息
  getInfo: (): Promise<AxiosResponse<ApiResponse<any>>> => api.get('/system/info'),

  // 检查工具状态
  checkTools: (): Promise<AxiosResponse<ApiResponse<any>>> => api.get('/system/tools'),

  // 获取日志
  getLogs: (level?: string, limit = 100): Promise<AxiosResponse<ApiResponse<any[]>>> =>
    api.get('/system/logs', { params: { level, limit } }),

  // 获取系统健康状态
  getHealth: (): Promise<AxiosResponse<ApiResponse<any>>> => api.get('/system/health'),

  // 获取数据库统计信息
  getDatabaseStats: (): Promise<AxiosResponse<ApiResponse<any>>> => 
    api.get('/system/database/stats'),

  // 优化数据库
  optimizeDatabase: (config: any): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.post('/system/database/optimize', config),

  // 获取性能建议
  getPerformanceRecommendations: (): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get('/system/database/recommendations'),

  // 运行性能测试
  runPerformanceTest: (): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get('/system/database/performance-test'),

  // 清理已完成任务
  cleanupCompletedTasks: (retentionDays: number): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.post('/system/database/cleanup-tasks', { retention_days: retentionDays }),

  // 获取系统指标
  getSystemMetrics: (): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get('/system/metrics'),

  // 获取系统告警
  getSystemAlerts: (): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get('/system/alerts'),

  // 获取监控仪表板
  getMonitoringDashboard: (): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get('/system/monitoring/dashboard'),
};

// 认证 API
export const authApi = {
  // 登录
  login: (credentials: LoginRequest): Promise<AxiosResponse<ApiResponse<LoginResponse>>> =>
    api.post('/auth/login', credentials),

  // 注册
  register: (userData: CreateUserRequest): Promise<AxiosResponse<ApiResponse<UserInfo>>> =>
    api.post('/auth/register', userData),

  // 获取用户信息
  getProfile: (): Promise<AxiosResponse<ApiResponse<UserInfo>>> =>
    api.get('/user/profile'),

  // 修改密码
  changePassword: (data: ChangePasswordRequest): Promise<AxiosResponse<ApiResponse<void>>> =>
    api.post('/user/change-password', data),

  // 获取用户列表 (仅管理员)
  getUsers: (page = 1, size = 20): Promise<AxiosResponse<ApiResponse<UserInfo[]>>> =>
    api.get('/user/users', { params: { page, size } }),

  // 更新用户信息
  updateUser: (userId: number, data: UpdateUserRequest): Promise<AxiosResponse<ApiResponse<UserInfo>>> =>
    api.put(`/user/users/${userId}`, data),

  // 删除用户 (仅管理员)
  deleteUser: (userId: number): Promise<AxiosResponse<ApiResponse<void>>> =>
    api.delete(`/user/users/${userId}`),
};

export { api };
export default api;