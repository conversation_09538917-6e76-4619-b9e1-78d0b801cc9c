{"version": 3, "file": "useMutation-i6kbUsZB.js", "sources": ["../../node_modules/.pnpm/@tanstack+query-core@5.83.0/node_modules/@tanstack/query-core/build/modern/mutationObserver.js", "../../node_modules/.pnpm/@tanstack+react-query@5.83.0_react@18.3.1/node_modules/@tanstack/react-query/build/modern/useMutation.js"], "sourcesContent": ["// src/mutationObserver.ts\nimport { getDefaultState } from \"./mutation.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { Subscribable } from \"./subscribable.js\";\nimport { hashKey, shallowEqualObjects } from \"./utils.js\";\nvar MutationObserver = class extends Subscribable {\n  #client;\n  #currentResult = void 0;\n  #currentMutation;\n  #mutateOptions;\n  constructor(client, options) {\n    super();\n    this.#client = client;\n    this.setOptions(options);\n    this.bindMethods();\n    this.#updateResult();\n  }\n  bindMethods() {\n    this.mutate = this.mutate.bind(this);\n    this.reset = this.reset.bind(this);\n  }\n  setOptions(options) {\n    const prevOptions = this.options;\n    this.options = this.#client.defaultMutationOptions(options);\n    if (!shallowEqualObjects(this.options, prevOptions)) {\n      this.#client.getMutationCache().notify({\n        type: \"observerOptionsUpdated\",\n        mutation: this.#currentMutation,\n        observer: this\n      });\n    }\n    if (prevOptions?.mutationKey && this.options.mutationKey && hashKey(prevOptions.mutationKey) !== hashKey(this.options.mutationKey)) {\n      this.reset();\n    } else if (this.#currentMutation?.state.status === \"pending\") {\n      this.#currentMutation.setOptions(this.options);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#currentMutation?.removeObserver(this);\n    }\n  }\n  onMutationUpdate(action) {\n    this.#updateResult();\n    this.#notify(action);\n  }\n  getCurrentResult() {\n    return this.#currentResult;\n  }\n  reset() {\n    this.#currentMutation?.removeObserver(this);\n    this.#currentMutation = void 0;\n    this.#updateResult();\n    this.#notify();\n  }\n  mutate(variables, options) {\n    this.#mutateOptions = options;\n    this.#currentMutation?.removeObserver(this);\n    this.#currentMutation = this.#client.getMutationCache().build(this.#client, this.options);\n    this.#currentMutation.addObserver(this);\n    return this.#currentMutation.execute(variables);\n  }\n  #updateResult() {\n    const state = this.#currentMutation?.state ?? getDefaultState();\n    this.#currentResult = {\n      ...state,\n      isPending: state.status === \"pending\",\n      isSuccess: state.status === \"success\",\n      isError: state.status === \"error\",\n      isIdle: state.status === \"idle\",\n      mutate: this.mutate,\n      reset: this.reset\n    };\n  }\n  #notify(action) {\n    notifyManager.batch(() => {\n      if (this.#mutateOptions && this.hasListeners()) {\n        const variables = this.#currentResult.variables;\n        const context = this.#currentResult.context;\n        if (action?.type === \"success\") {\n          this.#mutateOptions.onSuccess?.(action.data, variables, context);\n          this.#mutateOptions.onSettled?.(action.data, null, variables, context);\n        } else if (action?.type === \"error\") {\n          this.#mutateOptions.onError?.(action.error, variables, context);\n          this.#mutateOptions.onSettled?.(\n            void 0,\n            action.error,\n            variables,\n            context\n          );\n        }\n      }\n      this.listeners.forEach((listener) => {\n        listener(this.#currentResult);\n      });\n    });\n  }\n};\nexport {\n  MutationObserver\n};\n//# sourceMappingURL=mutationObserver.js.map", "\"use client\";\n\n// src/useMutation.ts\nimport * as React from \"react\";\nimport {\n  MutationObserver,\n  noop,\n  notify<PERSON><PERSON><PERSON>,\n  shouldThrowError\n} from \"@tanstack/query-core\";\nimport { useQueryClient } from \"./QueryClientProvider.js\";\nfunction useMutation(options, queryClient) {\n  const client = useQueryClient(queryClient);\n  const [observer] = React.useState(\n    () => new MutationObserver(\n      client,\n      options\n    )\n  );\n  React.useEffect(() => {\n    observer.setOptions(options);\n  }, [observer, options]);\n  const result = React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) => observer.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [observer]\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult()\n  );\n  const mutate = React.useCallback(\n    (variables, mutateOptions) => {\n      observer.mutate(variables, mutateOptions).catch(noop);\n    },\n    [observer]\n  );\n  if (result.error && shouldThrowError(observer.options.throwOnError, [result.error])) {\n    throw result.error;\n  }\n  return { ...result, mutate, mutateAsync: result.mutate };\n}\nexport {\n  useMutation\n};\n//# sourceMappingURL=useMutation.js.map"], "names": ["MutationObserver", "_a", "Subscribable", "client", "options", "__privateAdd", "_MutationObserver_instances", "_client", "_currentResult", "_currentMutation", "_mutateOptions", "__privateSet", "__privateMethod", "updateResult_fn", "prevOptions", "__privateGet", "shallowEqualObjects", "hash<PERSON><PERSON>", "action", "notify_fn", "variables", "state", "getDefaultState", "notify<PERSON><PERSON>ger", "context", "_b", "_d", "_c", "_f", "_e", "_h", "_g", "listener", "useMutation", "queryClient", "useQueryClient", "observer", "React.useState", "React.useEffect", "result", "React.useSyncExternalStore", "React.useCallback", "onStoreChange", "mutate", "mutateOptions", "noop", "shouldThrowError"], "mappings": "+gBAKIA,GAAmBC,EAAA,cAAcC,CAAa,CAKhD,YAAYC,EAAQC,EAAS,CAC3B,MAAK,EANcC,EAAA,KAAAC,GACrBD,EAAA,KAAAE,GACAF,EAAA,KAAAG,GACAH,EAAA,KAAAI,GACAJ,EAAA,KAAAK,GAGEC,EAAA,KAAKJ,EAAUJ,GACf,KAAK,WAAWC,CAAO,EACvB,KAAK,YAAW,EAChBQ,EAAA,KAAKN,EAAAO,GAAL,UACF,CACA,aAAc,CACZ,KAAK,OAAS,KAAK,OAAO,KAAK,IAAI,EACnC,KAAK,MAAQ,KAAK,MAAM,KAAK,IAAI,CACnC,CACA,WAAWT,EAAS,OAClB,MAAMU,EAAc,KAAK,QACzB,KAAK,QAAUC,EAAA,KAAKR,GAAQ,uBAAuBH,CAAO,EACrDY,EAAoB,KAAK,QAASF,CAAW,GAChDC,EAAA,KAAKR,GAAQ,iBAAgB,EAAG,OAAO,CACrC,KAAM,yBACN,SAAUQ,EAAA,KAAKN,GACf,SAAU,IAClB,CAAO,EAECK,GAAA,MAAAA,EAAa,aAAe,KAAK,QAAQ,aAAeG,EAAQH,EAAY,WAAW,IAAMG,EAAQ,KAAK,QAAQ,WAAW,EAC/H,KAAK,MAAK,IACDhB,EAAAc,EAAA,KAAKN,KAAL,YAAAR,EAAuB,MAAM,UAAW,WACjDc,EAAA,KAAKN,GAAiB,WAAW,KAAK,OAAO,CAEjD,CACA,eAAgB,OACT,KAAK,iBACRR,EAAAc,EAAA,KAAKN,KAAL,MAAAR,EAAuB,eAAe,KAE1C,CACA,iBAAiBiB,EAAQ,CACvBN,EAAA,KAAKN,EAAAO,GAAL,WACAD,EAAA,KAAKN,EAAAa,GAAL,UAAaD,EACf,CACA,kBAAmB,CACjB,OAAOH,EAAA,KAAKP,EACd,CACA,OAAQ,QACNP,EAAAc,EAAA,KAAKN,KAAL,MAAAR,EAAuB,eAAe,MACtCU,EAAA,KAAKF,EAAmB,QACxBG,EAAA,KAAKN,EAAAO,GAAL,WACAD,EAAA,KAAKN,EAAAa,GAAL,UACF,CACA,OAAOC,EAAWhB,EAAS,OACzB,OAAAO,EAAA,KAAKD,EAAiBN,IACtBH,EAAAc,EAAA,KAAKN,KAAL,MAAAR,EAAuB,eAAe,MACtCU,EAAA,KAAKF,EAAmBM,EAAA,KAAKR,GAAQ,iBAAgB,EAAG,MAAMQ,EAAA,KAAKR,GAAS,KAAK,OAAO,GACxFQ,EAAA,KAAKN,GAAiB,YAAY,IAAI,EAC/BM,EAAA,KAAKN,GAAiB,QAAQW,CAAS,CAChD,CAoCF,EA3FEb,EAAA,YACAC,EAAA,YACAC,EAAA,YACAC,EAAA,YAJqBJ,EAAA,YAyDrBO,EAAa,UAAG,OACd,MAAMQ,IAAQpB,EAAAc,EAAA,KAAKN,KAAL,YAAAR,EAAuB,QAASqB,EAAe,EAC7DX,EAAA,KAAKH,EAAiB,CACpB,GAAGa,EACH,UAAWA,EAAM,SAAW,UAC5B,UAAWA,EAAM,SAAW,UAC5B,QAASA,EAAM,SAAW,QAC1B,OAAQA,EAAM,SAAW,OACzB,OAAQ,KAAK,OACb,MAAO,KAAK,KAClB,EACE,EACAF,EAAO,SAACD,EAAQ,CACdK,EAAc,MAAM,IAAM,qBACxB,GAAIR,EAAA,KAAKL,IAAkB,KAAK,aAAY,EAAI,CAC9C,MAAMU,EAAYL,EAAA,KAAKP,GAAe,UAChCgB,EAAUT,EAAA,KAAKP,GAAe,SAChCU,GAAA,YAAAA,EAAQ,QAAS,YACnBO,GAAAxB,EAAAc,EAAA,KAAKL,IAAe,YAApB,MAAAe,EAAA,KAAAxB,EAAgCiB,EAAO,KAAME,EAAWI,IACxDE,GAAAC,EAAAZ,EAAA,KAAKL,IAAe,YAApB,MAAAgB,EAAA,KAAAC,EAAgCT,EAAO,KAAM,KAAME,EAAWI,KACrDN,GAAA,YAAAA,EAAQ,QAAS,WAC1BU,GAAAC,EAAAd,EAAA,KAAKL,IAAe,UAApB,MAAAkB,EAAA,KAAAC,EAA8BX,EAAO,MAAOE,EAAWI,IACvDM,GAAAC,EAAAhB,EAAA,KAAKL,IAAe,YAApB,MAAAoB,EAAA,KAAAC,EACE,OACAb,EAAO,MACPE,EACAI,GAGN,CACA,KAAK,UAAU,QAASQ,GAAa,CACnCA,EAASjB,EAAA,KAAKP,EAAc,CAC9B,CAAC,CACH,CAAC,CACH,EA3FqBP,GCMvB,SAASgC,EAAY7B,EAAS8B,EAAa,CACzC,MAAM/B,EAASgC,EAA0B,EACnC,CAACC,CAAQ,EAAIC,EAAAA,SACjB,IAAM,IAAIrC,EACRG,EACAC,CACN,CACA,EACEkC,EAAAA,UAAgB,IAAM,CACpBF,EAAS,WAAWhC,CAAO,CAC7B,EAAG,CAACgC,EAAUhC,CAAO,CAAC,EACtB,MAAMmC,EAASC,EAAAA,qBACbC,EAAAA,YACGC,GAAkBN,EAAS,UAAUb,EAAc,WAAWmB,CAAa,CAAC,EAC7E,CAACN,CAAQ,CACf,EACI,IAAMA,EAAS,iBAAgB,EAC/B,IAAMA,EAAS,iBAAgB,CACnC,EACQO,EAASF,EAAAA,YACb,CAACrB,EAAWwB,IAAkB,CAC5BR,EAAS,OAAOhB,EAAWwB,CAAa,EAAE,MAAMC,CAAI,CACtD,EACA,CAACT,CAAQ,CACb,EACE,GAAIG,EAAO,OAASO,EAAiBV,EAAS,QAAQ,aAAc,CAACG,EAAO,KAAK,CAAC,EAChF,MAAMA,EAAO,MAEf,MAAO,CAAE,GAAGA,EAAQ,OAAAI,EAAQ,YAAaJ,EAAO,MAAM,CACxD", "x_google_ignoreList": [0, 1]}