import{l as X,u as G,m as J,b as V,o as Z,c as B,j as e,p as y}from"./index-z9zCfJ11.js";import{r as C}from"./vendor-o6zXO7vr.js";import{b as a,X as ee,a7 as v,B as h,a6 as se,T as te,ab as re,R as f,C as n,ac as o,y as R,f as u,P as ae,S as _,a3 as ie,j as ne,ad as le,k as p,ae as z,n as de,o as ce,u as oe,t as H,g as A,af as l}from"./ui-Smnqh0BA.js";import"./charts-CfM58meh.js";const{Title:xe,Text:x}=te,me=()=>{const{taskId:i}=X(),M=G(),[W,q]=C.useState("overview"),{subscribeToTask:b,unsubscribeFromTask:k,connected:I}=J(),{tasks:he,getTaskById:L}=V(),{logs:Y,connected:j}=Z(),d=L(i||""),w=Y[i||""]||[],{data:g,isLoading:D,error:E,refetch:m}=B({queryKey:["task",i],queryFn:()=>y.get(`/tasks/${i}`),enabled:!!i,refetchInterval:(d==null?void 0:d.status)==="running"?5e3:!1}),{data:t}=B({queryKey:["task-results",i],queryFn:()=>y.get(`/results?task_id=${i}`),enabled:!!i&&(d==null?void 0:d.status)==="completed"});if(C.useEffect(()=>{if(i&&I)return b(i),()=>{k(i)}},[i,I,b,k]),D)return e.jsx("div",{style:{padding:24},children:e.jsx(a,{children:e.jsxs("div",{style:{textAlign:"center",padding:"40px 0"},children:[e.jsx(ee,{size:"large"}),e.jsx("div",{style:{marginTop:16},children:"正在加载任务详情..."})]})})});if(E)return e.jsx("div",{style:{padding:24},children:e.jsx(a,{children:e.jsx(v,{message:"加载失败",description:"无法加载任务详情，请检查任务ID是否正确。",type:"error",showIcon:!0,action:e.jsx(h,{size:"small",danger:!0,onClick:()=>m(),children:"重试"})})})});const r=(g==null?void 0:g.data)||d;if(!r)return e.jsx("div",{style:{padding:24},children:e.jsx(a,{children:e.jsx(v,{message:"任务不存在",description:"未找到指定的任务。",type:"warning",showIcon:!0})})});const F=s=>{switch(s){case"pending":return"default";case"running":return"processing";case"completed":return"success";case"failed":return"error";case"stopped":return"warning";default:return"default"}},K=s=>{switch(s){case"pending":return"等待中";case"running":return"运行中";case"completed":return"已完成";case"failed":return"失败";case"stopped":return"已停止";default:return s}},P=async()=>{try{await y.delete(`/tasks/${i}`),m()}catch(s){console.error("停止任务失败:",s)}},U=()=>{console.log("下载结果")},N=()=>{var s,c,T,S,$;return e.jsxs(f,{gutter:[16,16],children:[e.jsxs(n,{xs:24,lg:16,children:[e.jsx(a,{title:"任务信息",style:{marginBottom:16},children:e.jsxs(o,{column:2,children:[e.jsx(o.Item,{label:"任务ID",children:e.jsx(x,{copyable:!0,children:r.id})}),e.jsx(o.Item,{label:"目标",children:e.jsx(x,{strong:!0,children:r.target})}),e.jsx(o.Item,{label:"状态",children:e.jsx(R,{status:F(r.status),text:K(r.status)})}),e.jsx(o.Item,{label:"扫描类型",children:r.scan_type||"标准扫描"}),e.jsx(o.Item,{label:"创建时间",children:u(r.created_at).format("YYYY-MM-DD HH:mm:ss")}),e.jsx(o.Item,{label:"持续时间",children:r.updated_at?u(r.updated_at).diff(u(r.created_at),"second")+"秒":"-"})]})}),e.jsxs(a,{title:"扫描进度",style:{marginBottom:16},children:[e.jsx(ae,{percent:r.progress||0,status:r.status==="failed"?"exception":r.status==="completed"?"success":"active",strokeColor:{"0%":"#108ee9","100%":"#87d068"}}),e.jsx("div",{style:{marginTop:8},children:e.jsx(x,{type:"secondary",children:r.status==="running"?"正在执行扫描...":r.status==="completed"?"扫描已完成":r.status==="failed"?"扫描失败":"等待开始"})})]}),r.error&&e.jsx(a,{title:"错误信息",style:{marginBottom:16},children:e.jsx(v,{message:"任务执行出错",description:r.error,type:"error",showIcon:!0})})]}),e.jsxs(n,{xs:24,lg:8,children:[e.jsx(a,{title:"连接状态",style:{marginBottom:16},children:e.jsxs(_,{direction:"vertical",style:{width:"100%"},children:[e.jsx("div",{children:e.jsx(R,{status:j?"success":"error",text:j?"WebSocket已连接":"WebSocket断开"})}),e.jsx("div",{children:e.jsxs(x,{type:"secondary",children:["实时日志: ",j?"启用":"禁用"]})})]})}),e.jsx(a,{title:"操作",style:{marginBottom:16},children:e.jsxs(_,{direction:"vertical",style:{width:"100%"},children:[r.status==="running"&&e.jsx(h,{type:"primary",danger:!0,icon:e.jsx(ie,{}),onClick:P,block:!0,children:"停止任务"}),e.jsx(h,{icon:e.jsx(ne,{}),onClick:()=>m(),block:!0,children:"刷新状态"}),r.status==="completed"&&e.jsx(h,{type:"primary",icon:e.jsx(le,{}),onClick:U,block:!0,children:"下载结果"})]})}),(t==null?void 0:t.data)&&e.jsx(a,{title:"扫描统计",style:{marginBottom:16},children:e.jsxs(f,{gutter:16,children:[e.jsx(n,{span:12,children:e.jsx(p,{title:"发现主机",value:((s=t.data.hosts)==null?void 0:s.length)||0,prefix:e.jsx(z,{})})}),e.jsx(n,{span:12,children:e.jsx(p,{title:"开放端口",value:((c=t.data.ports)==null?void 0:c.length)||0,prefix:e.jsx(de,{})})}),e.jsx(n,{span:12,style:{marginTop:16},children:e.jsx(p,{title:"发现漏洞",value:((T=t.data.vulnerabilities)==null?void 0:T.length)||0,prefix:e.jsx(ce,{}),valueStyle:{color:((S=t.data.vulnerabilities)==null?void 0:S.length)>0?"#cf1322":"#3f8600"}})}),e.jsx(n,{span:12,style:{marginTop:16},children:e.jsx(p,{title:"子域名",value:(($=t.data.subdomains)==null?void 0:$.length)||0,prefix:e.jsx(z,{})})})]})})]})]})},O=()=>e.jsx(a,{title:"实时日志",style:{height:"600px",overflow:"hidden"},children:e.jsx("div",{style:{height:"520px",overflow:"auto",padding:"8px 0"},children:w.length===0?e.jsxs("div",{style:{textAlign:"center",padding:"40px 0",color:"#999"},children:[e.jsx(oe,{style:{fontSize:24,marginBottom:8}}),e.jsx("div",{children:"暂无日志数据"}),e.jsx(x,{type:"secondary",style:{fontSize:12},children:j?"等待任务产生日志...":"请检查WebSocket连接"})]}):e.jsx(H,{mode:"left",style:{paddingTop:16},children:w.map((s,c)=>e.jsxs(H.Item,{color:s.level==="error"?"red":s.level==="warn"?"orange":"blue",children:[e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(A,{color:s.level==="error"?"red":s.level==="warn"?"orange":"blue",children:s.level.toUpperCase()}),e.jsx(x,{type:"secondary",style:{fontSize:12},children:u(s.timestamp).format("HH:mm:ss")})]}),e.jsx("div",{style:{wordBreak:"break-all"},children:s.message})]},c))})})}),Q=()=>e.jsx("div",{children:t!=null&&t.data?e.jsxs(f,{gutter:[16,16],children:[t.data.hosts&&e.jsx(n,{xs:24,lg:12,children:e.jsx(a,{title:`发现主机 (${t.data.hosts.length})`,children:e.jsx(l,{size:"small",dataSource:t.data.hosts,renderItem:s=>e.jsx(l.Item,{children:e.jsx(l.Item.Meta,{title:s.ip_address,description:`状态: ${s.status} | 系统: ${s.os_name||"未知"}`})}),style:{maxHeight:400,overflow:"auto"}})})}),t.data.ports&&e.jsx(n,{xs:24,lg:12,children:e.jsx(a,{title:`开放端口 (${t.data.ports.length})`,children:e.jsx(l,{size:"small",dataSource:t.data.ports,renderItem:s=>e.jsx(l.Item,{children:e.jsx(l.Item.Meta,{title:`${s.port}/${s.protocol}`,description:`服务: ${s.service||"未知"} | 状态: ${s.state}`})}),style:{maxHeight:400,overflow:"auto"}})})}),t.data.vulnerabilities&&t.data.vulnerabilities.length>0&&e.jsx(n,{xs:24,children:e.jsx(a,{title:`发现漏洞 (${t.data.vulnerabilities.length})`,children:e.jsx(l,{dataSource:t.data.vulnerabilities,renderItem:s=>{var c;return e.jsx(l.Item,{children:e.jsx(l.Item.Meta,{title:e.jsxs("span",{children:[e.jsx(A,{color:s.severity==="critical"?"red":s.severity==="high"?"orange":s.severity==="medium"?"gold":"green",children:(c=s.severity)==null?void 0:c.toUpperCase()}),s.name]}),description:s.description})})},style:{maxHeight:400,overflow:"auto"}})})})]}):e.jsx(a,{children:e.jsx("div",{style:{textAlign:"center",padding:"40px 0"},children:e.jsx(x,{type:"secondary",children:"暂无扫描结果"})})})});return e.jsxs("div",{style:{padding:24},children:[e.jsxs("div",{style:{marginBottom:16},children:[e.jsx(h,{icon:e.jsx(se,{}),onClick:()=>M("/tasks"),style:{marginRight:16},children:"返回任务列表"}),e.jsxs(xe,{level:3,style:{display:"inline-block",margin:0},children:["任务详情 - ",r.target]})]}),e.jsx(re,{activeKey:W,onChange:q,items:[{key:"overview",label:"概览",children:N()},{key:"logs",label:"实时日志",children:O()},{key:"results",label:"扫描结果",children:Q()}]})]})};export{me as default};
//# sourceMappingURL=TaskDetail-Bfmfpbt2.js.map
