import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  <PERSON>ati<PERSON>,
  Button,
  Alert,
  Table,
  Progress,
  Tabs,
  Modal,
  Form,
  InputNumber,
  Switch,
  message,
  Tag,
  Space,
  Divider,
  Typography,
} from 'antd';
import {
  DatabaseOutlined,
  <PERSON>boltOutlined,
  CheckOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  SyncOutlined,
  DeleteOutlined,
  Bar<PERSON><PERSON>Outlined,
  MonitorOutlined,
} from '@ant-design/icons';
import { systemApi } from '@/api';
import { useAuthStore } from '@/store/auth';
import { UserRole } from '@/types';

const { TabPane } = Tabs;
const { Text } = Typography;

interface DatabaseStats {
  table_sizes: Record<string, number>;
  wal_size: number;
  health_score?: number;
  health_status?: string;
  total_records?: number;
  wal_size_mb?: number;
  recommendations?: string[];
  timestamp?: string;
}

interface PerformanceMetrics {
  [key: string]: number;
}

const SystemManagement: React.FC = () => {
  const [stats, setStats] = useState<DatabaseStats | null>(null);
  const [recommendations, setRecommendations] = useState<string[]>([]);
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics>({});
  const [healthData, setHealthData] = useState<any>(null);
  const [systemMetrics, setSystemMetrics] = useState<any>(null);
  const [alerts, setAlerts] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [optimizeModalVisible, setOptimizeModalVisible] = useState(false);
  const [cleanupModalVisible, setCleanupModalVisible] = useState(false);
  const [form] = Form.useForm();
  
  const { user } = useAuthStore();

  // 检查权限
  const canManageSystem = user?.role === UserRole.Admin;

  useEffect(() => {
    if (canManageSystem) {
      fetchAllData();
    }
  }, [canManageSystem]);

  const fetchAllData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchDatabaseStats(),
        fetchHealthData(),
        fetchRecommendations(),
        fetchSystemMetrics(),
        fetchAlerts(),
      ]);
    } catch (error) {
      message.error('获取系统信息失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchDatabaseStats = async () => {
    try {
      const response = await systemApi.getDatabaseStats();
      if (response.data.success) {
        setStats(response.data.data);
      }
    } catch (error) {
      console.error('获取数据库统计失败:', error);
    }
  };

  const fetchHealthData = async () => {
    try {
      const response = await systemApi.getHealth();
      if (response.data.success) {
        setHealthData(response.data.data);
      }
    } catch (error) {
      console.error('获取健康状态失败:', error);
    }
  };

  const fetchRecommendations = async () => {
    try {
      const response = await systemApi.getPerformanceRecommendations();
      if (response.data.success) {
        setRecommendations(response.data.data.recommendations || []);
      }
    } catch (error) {
      console.error('获取性能建议失败:', error);
    }
  };

  const fetchSystemMetrics = async () => {
    try {
      const response = await systemApi.getSystemMetrics();
      if (response.data.success) {
        setSystemMetrics(response.data.data);
      }
    } catch (error) {
      console.error('获取系统指标失败:', error);
    }
  };

  const fetchAlerts = async () => {
    try {
      const response = await systemApi.getSystemAlerts();
      if (response.data.success) {
        setAlerts(response.data.data.alerts || []);
      }
    } catch (error) {
      console.error('获取系统告警失败:', error);
    }
  };

  const runPerformanceTest = async () => {
    setLoading(true);
    try {
      const response = await systemApi.runPerformanceTest();
      if (response.data.success) {
        setPerformanceMetrics(response.data.data.performance_metrics);
        message.success('性能测试完成');
      }
    } catch (error) {
      message.error('性能测试失败');
    } finally {
      setLoading(false);
    }
  };

  const handleOptimizeDatabase = async (values: any) => {
    setLoading(true);
    try {
      const response = await systemApi.optimizeDatabase(values);
      if (response.data.success) {
        message.success('数据库优化完成');
        setOptimizeModalVisible(false);
        fetchAllData();
      }
    } catch (error) {
      message.error('数据库优化失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCleanupTasks = async (values: any) => {
    setLoading(true);
    try {
      const response = await systemApi.cleanupCompletedTasks(values.retention_days);
      if (response.data.success) {
        message.success(response.data.data.message);
        setCleanupModalVisible(false);
        fetchAllData();
      }
    } catch (error) {
      message.error('清理任务失败');
    } finally {
      setLoading(false);
    }
  };

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'excellent': return '#52c41a';
      case 'good': return '#1890ff';
      case 'fair': return '#faad14';
      case 'poor': return '#f5222d';
      default: return '#d9d9d9';
    }
  };

  const getHealthText = (status: string) => {
    switch (status) {
      case 'excellent': return '优秀';
      case 'good': return '良好';
      case 'fair': return '一般';
      case 'poor': return '较差';
      default: return '未知';
    }
  };

  if (!canManageSystem) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <WarningOutlined style={{ fontSize: '48px', color: '#faad14', marginBottom: '16px' }} />
        <h2>权限不足</h2>
        <p>只有管理员可以访问系统管理功能</p>
      </div>
    );
  }

  // 表格数据
  const tableColumns = [
    {
      title: '数据表',
      dataIndex: 'table',
      key: 'table',
    },
    {
      title: '记录数',
      dataIndex: 'count',
      key: 'count',
      render: (count: number) => count.toLocaleString(),
    },
    {
      title: '占比',
      dataIndex: 'percentage',
      key: 'percentage',
      render: (percentage: number) => `${percentage.toFixed(1)}%`,
    },
  ];

  const tableData = stats ? Object.entries(stats.table_sizes).map(([table, count]) => ({
    key: table,
    table,
    count,
    percentage: (count / Object.values(stats.table_sizes).reduce((a, b) => a + b, 0)) * 100,
  })).sort((a, b) => b.count - a.count) : [];

  const performanceColumns = [
    {
      title: '测试项目',
      dataIndex: 'test',
      key: 'test',
    },
    {
      title: '响应时间 (ms)',
      dataIndex: 'time',
      key: 'time',
      render: (time: number) => {
        const color = time < 10 ? 'green' : time < 50 ? 'orange' : 'red';
        return <Tag color={color}>{time.toFixed(2)}</Tag>;
      },
    },
  ];

  const performanceData = Object.entries(performanceMetrics).map(([test, time]) => ({
    key: test,
    test: test.replace(/_/g, ' ').replace(/ms$/, ''),
    time,
  }));

  return (
    <div style={{ padding: '24px' }}>
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="系统健康分数"
              value={healthData?.health_score || 0}
              suffix="/100"
              prefix={<CheckOutlined />}
              valueStyle={{ color: getHealthColor(healthData?.health_status) }}
            />
            <div style={{ marginTop: '8px' }}>
              <Tag color={getHealthColor(healthData?.health_status)}>
                {getHealthText(healthData?.health_status)}
              </Tag>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总记录数"
              value={healthData?.total_records || 0}
              prefix={<DatabaseOutlined />}
              formatter={(value) => `${Number(value).toLocaleString()}`}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="WAL文件大小"
              value={healthData?.wal_size_mb || 0}
              suffix="MB"
              prefix={<DatabaseOutlined />}
              valueStyle={{ 
                color: (healthData?.wal_size_mb || 0) > 100 ? '#f5222d' : '#52c41a' 
              }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="性能建议"
              value={recommendations.length}
              suffix="条"
              prefix={<WarningOutlined />}
              valueStyle={{ 
                color: recommendations.length > 3 ? '#f5222d' : 
                       recommendations.length > 1 ? '#faad14' : '#52c41a' 
              }}
            />
          </Card>
        </Col>
      </Row>

      {recommendations.length > 0 && (
        <Alert
          message="性能优化建议"
          description={
            <ul style={{ marginBottom: 0 }}>
              {recommendations.map((rec, index) => (
                <li key={index}>{rec}</li>
              ))}
            </ul>
          }
          type="warning"
          showIcon
          style={{ marginBottom: '24px' }}
        />
      )}

      <Tabs defaultActiveKey="overview">
        <TabPane tab="概览" key="overview">
          <Row gutter={16}>
            <Col span={12}>
              <Card title="数据表统计" extra={
                <Button 
                  type="link" 
                  icon={<SyncOutlined />}
                  onClick={fetchDatabaseStats}
                  loading={loading}
                >
                  刷新
                </Button>
              }>
                <Table
                  dataSource={tableData}
                  columns={tableColumns}
                  pagination={false}
                  size="small"
                />
              </Card>
            </Col>
            <Col span={12}>
              <Card title="系统操作" style={{ height: '100%' }}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Button
                    type="primary"
                    icon={<ThunderboltOutlined />}
                    onClick={() => setOptimizeModalVisible(true)}
                    block
                  >
                    优化数据库
                  </Button>
                  <Button
                    icon={<DeleteOutlined />}
                    onClick={() => setCleanupModalVisible(true)}
                    block
                  >
                    清理历史数据
                  </Button>
                  <Button
                    icon={<BarChartOutlined />}
                    onClick={runPerformanceTest}
                    loading={loading}
                    block
                  >
                    运行性能测试
                  </Button>
                  <Button
                    icon={<SyncOutlined />}
                    onClick={fetchAllData}
                    loading={loading}
                    block
                  >
                    刷新全部数据
                  </Button>
                </Space>
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="系统监控" key="monitoring">
          <Row gutter={16} style={{ marginBottom: '16px' }}>
            <Col span={8}>
              <Card>
                <Statistic
                  title="CPU使用率"
                  value={systemMetrics?.cpu_usage || 0}
                  suffix="%"
                  precision={1}
                  valueStyle={{ 
                    color: (systemMetrics?.cpu_usage || 0) > 80 ? '#f5222d' : '#52c41a' 
                  }}
                />
                <Progress 
                  percent={systemMetrics?.cpu_usage || 0} 
                  size="small" 
                  status={(systemMetrics?.cpu_usage || 0) > 80 ? 'exception' : 'normal'}
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card>
                <Statistic
                  title="内存使用率"
                  value={systemMetrics?.memory_usage?.usage_percent || 0}
                  suffix="%"
                  precision={1}
                  valueStyle={{ 
                    color: (systemMetrics?.memory_usage?.usage_percent || 0) > 80 ? '#f5222d' : '#52c41a' 
                  }}
                />
                <Progress 
                  percent={systemMetrics?.memory_usage?.usage_percent || 0} 
                  size="small"
                  status={(systemMetrics?.memory_usage?.usage_percent || 0) > 80 ? 'exception' : 'normal'}
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card>
                <Statistic
                  title="活跃进程"
                  value={systemMetrics?.process_stats?.total_processes || 0}
                  prefix={<MonitorOutlined />}
                />
              </Card>
            </Col>
          </Row>

          {alerts.length > 0 && (
            <Card title="系统告警" style={{ marginBottom: '16px' }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                {alerts.map((alert, index) => (
                  <Alert
                    key={index}
                    message={`${alert.category}: ${alert.message}`}
                    type={alert.level === 'critical' ? 'error' : 
                          alert.level === 'warning' ? 'warning' : 'info'}
                    showIcon
                    closable
                  />
                ))}
              </Space>
            </Card>
          )}

          <Row gutter={16}>
            <Col span={12}>
              <Card title="磁盘使用情况">
                {systemMetrics?.disk_usage?.map((disk: any, index: number) => (
                  <div key={index} style={{ marginBottom: '12px' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text>{disk.name}</Text>
                      <Text>{disk.usage_percent.toFixed(1)}%</Text>
                    </div>
                    <Progress 
                      percent={disk.usage_percent} 
                      size="small"
                      status={disk.usage_percent > 85 ? 'exception' : 'normal'}
                    />
                  </div>
                ))}
              </Card>
            </Col>
            <Col span={12}>
              <Card title="网络统计">
                <Row gutter={16}>
                  <Col span={12}>
                    <Statistic
                      title="接收字节"
                      value={systemMetrics?.network_stats?.bytes_received || 0}
                      formatter={(value) => `${(Number(value) / 1024 / 1024).toFixed(2)} MB`}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="发送字节"
                      value={systemMetrics?.network_stats?.bytes_transmitted || 0}
                      formatter={(value) => `${(Number(value) / 1024 / 1024).toFixed(2)} MB`}
                    />
                  </Col>
                </Row>
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="性能测试" key="performance">
          <Card 
            title="查询性能测试结果" 
            extra={
              <Button 
                icon={<BarChartOutlined />}
                onClick={runPerformanceTest}
                loading={loading}
              >
                重新测试
              </Button>
            }
          >
            {Object.keys(performanceMetrics).length > 0 ? (
              <Table
                dataSource={performanceData}
                columns={performanceColumns}
                pagination={false}
              />
            ) : (
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <BarChartOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
                <p style={{ color: '#999', marginTop: '16px' }}>
                  点击&quot;重新测试&quot;按钮开始性能测试
                </p>
              </div>
            )}
          </Card>
        </TabPane>
      </Tabs>

      {/* 数据库优化Modal */}
      <Modal
        title="数据库优化"
        open={optimizeModalVisible}
        onCancel={() => setOptimizeModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleOptimizeDatabase}
          initialValues={{
            auto_vacuum_enabled: true,
            log_retention_days: 30,
            max_wal_size_mb: 100,
            cleanup_completed_tasks: false,
            task_retention_days: 90,
          }}
        >
          <Form.Item
            label="启用自动清理"
            name="auto_vacuum_enabled"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            label="日志保留天数"
            name="log_retention_days"
          >
            <InputNumber min={1} max={365} />
          </Form.Item>

          <Form.Item
            label="WAL文件最大大小 (MB)"
            name="max_wal_size_mb"
          >
            <InputNumber min={10} max={1000} />
          </Form.Item>

          <Form.Item
            label="同时清理已完成任务"
            name="cleanup_completed_tasks"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            label="任务保留天数"
            name="task_retention_days"
            dependencies={['cleanup_completed_tasks']}
          >
            <InputNumber min={7} max={365} />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0 }}>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button onClick={() => setOptimizeModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                开始优化
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 清理任务Modal */}
      <Modal
        title="清理历史数据"
        open={cleanupModalVisible}
        onCancel={() => setCleanupModalVisible(false)}
        footer={null}
      >
        <Form
          layout="vertical"
          onFinish={handleCleanupTasks}
          initialValues={{ retention_days: 90 }}
        >
          <Alert
            message="注意"
            description="此操作将永久删除指定天数之前的已完成任务及其相关数据，请谨慎操作！"
            type="warning"
            showIcon
            style={{ marginBottom: '16px' }}
          />

          <Form.Item
            label="保留天数"
            name="retention_days"
            help="将删除这个天数之前的已完成任务"
          >
            <InputNumber min={7} max={365} />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0 }}>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button onClick={() => setCleanupModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" danger htmlType="submit" loading={loading}>
                确认清理
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default SystemManagement;