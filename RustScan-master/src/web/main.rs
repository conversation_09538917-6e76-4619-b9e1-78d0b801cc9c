use actix_cors::Cors;
use actix_web::{middleware::Logger, web, App, HttpServer};
use actix_web_httpauth::middleware::HttpAuthentication;
use sqlx::SqlitePool;
use std::env;

mod common;
mod handlers;
mod models;
mod services;
mod tools;
mod utils;

use handlers::websocket_handlers::{websocket_handler, WEBSOCKET_MANAGER};
use handlers::*;
use services::database::Database;
use services::database_optimizer::OptimizationConfig;
use services::scheduled_tasks;
use utils::auth::jwt_validator;

async fn create_tables(pool: &SqlitePool) -> Result<(), sqlx::Error> {
    // 执行初始化迁移
    let sql = include_str!("../../migrations/20240101000000_initial.sql");
    sqlx::raw_sql(sql).execute(pool).await?;

    // 执行性能优化迁移
    let optimization_sql =
        include_str!("../../migrations/20240102000000_performance_optimization.sql");
    sqlx::raw_sql(optimization_sql).execute(pool).await?;

    Ok(())
}

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    // 初始化日志系统
    env_logger::init();

    // 获取数据库 URL
    let database_url =
        env::var("DATABASE_URL").unwrap_or_else(|_| "sqlite://./data/rustscan.db".to_string());

    // 创建数据库连接池
    let pool = SqlitePool::connect(&database_url)
        .await
        .expect("Failed to create database pool");

    // TODO: 实现数据库迁移逻辑
    // 暂时手动创建表
    create_tables(&pool)
        .await
        .expect("Failed to create database tables");

    let db = Database::new(pool);

    // 初始化定时任务管理器
    let optimization_config = OptimizationConfig {
        auto_vacuum_enabled: true,
        log_retention_days: 30,
        max_wal_size_mb: 100,
        analyze_frequency_hours: 24,
        cleanup_frequency_hours: 6,
    };

    scheduled_tasks::initialize_task_manager(db.clone(), optimization_config);

    // 启动后台定时任务
    tokio::spawn(async {
        scheduled_tasks::start_background_tasks().await;
    });

    log::info!("Starting RustScan Web Server on 0.0.0.0:8080");

    // 初始化 WebSocket 管理器
    let ws_manager = WEBSOCKET_MANAGER.clone();

    HttpServer::new(move || {
        let cors = Cors::default()
            .allow_any_origin()
            .allow_any_method()
            .allow_any_header()
            .max_age(3600);

        // 创建JWT认证中间件
        let auth = HttpAuthentication::bearer(jwt_validator);

        App::new()
            .app_data(web::Data::new(db.clone()))
            .app_data(web::Data::new(ws_manager.clone()))
            .wrap(Logger::default())
            .wrap(cors)
            .service(
                web::scope("/api")
                    // 公开的认证相关路由
                    .service(
                        web::scope("/auth")
                            .service(auth_handlers::register_user)
                            .service(auth_handlers::login_user),
                    )
                    // 需要认证的路由
                    .service(
                        web::scope("/user")
                            .wrap(auth.clone())
                            .service(auth_handlers::get_profile)
                            .service(auth_handlers::change_password)
                            .service(auth_handlers::get_users)
                            .service(auth_handlers::update_user)
                            .service(auth_handlers::delete_user),
                    )
                    .service(
                        web::scope("/tasks")
                            .wrap(auth.clone())
                            .service(task_handlers::create_task)
                            .service(task_handlers::get_tasks)
                            .service(task_handlers::get_task)
                            .service(task_handlers::delete_task)
                            .service(task_handlers::start_task)
                            .service(task_handlers::stop_task),
                    )
                    .service(
                        web::scope("/results")
                            .wrap(auth.clone())
                            .service(result_handlers::get_hosts)
                            .service(result_handlers::get_ports)
                            .service(result_handlers::get_vulnerabilities)
                            .service(result_handlers::get_statistics)
                            .service(result_handlers::get_all_vulnerabilities)
                            .service(result_handlers::get_all_hosts)
                            .service(result_handlers::get_all_ports)
                            .service(result_handlers::get_all_web_assets),
                    )
                    .service(
                        web::scope("/config")
                            .wrap(auth.clone())
                            .service(config_handlers::get_config)
                            .service(config_handlers::update_config),
                    )
                    .service(
                        web::scope("/system")
                            .wrap(auth.clone())
                            .service(system_handlers::get_database_stats)
                            .service(system_handlers::optimize_database)
                            .service(system_handlers::get_performance_recommendations)
                            .service(system_handlers::run_performance_test)
                            .service(system_handlers::cleanup_completed_tasks)
                            .service(system_handlers::get_system_health)
                            .service(system_handlers::get_system_metrics)
                            .service(system_handlers::get_system_alerts)
                            .service(system_handlers::get_monitoring_dashboard),
                    ),
            )
            .route("/ws", web::get().to(websocket_handler))
            .service(actix_files::Files::new("/", "./frontend/dist").index_file("index.html"))
    })
    .bind("0.0.0.0:8080")?
    .run()
    .await
}
