{"version": 3, "file": "CreateTask-DGfnfyYJ.js", "sources": ["../../src/pages/Tasks/CreateTask.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Card,\n  Form,\n  Input,\n  Select,\n  Button,\n  Typography,\n  Space,\n  Row,\n  Col,\n  Switch,\n  InputNumber,\n  Divider,\n  Alert,\n  App,\n} from 'antd';\nimport { useMutation } from '@tanstack/react-query';\nimport { ArrowLeftOutlined, PlayCircleOutlined } from '@ant-design/icons';\n\nimport { taskApi } from '@/api';\nimport { CreateTaskRequest, ScanType, ScanMode, TargetType, UserAgentType } from '@/types';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\nconst CreateTask: React.FC = () => {\n  const navigate = useNavigate();\n  const { message } = App.useApp();\n  const [form] = Form.useForm();\n  const [selectedType, setSelectedType] = useState<ScanType>(ScanType.Standard);\n  const [showAdvanced, setShowAdvanced] = useState(false);\n\n  // 创建任务\n  const createTaskMutation = useMutation({\n    mutationFn: taskApi.createTask,\n    onSuccess: (response) => {\n      message.success('任务创建成功');\n      navigate(`/tasks/${response.data.data?.id}`);\n    },\n    onError: (error: any) => {\n      message.error('创建任务失败: ' + error.message);\n    },\n  });\n\n  const onFinish = (values: any) => {\n    // 构建配置对象\n    const config = showAdvanced ? {\n      scan_mode: values.scan_mode,\n      target_type: values.target_type,\n      enable_port_scan: values.enable_port_scan,\n      enable_service_detection: values.enable_service_detection,\n      enable_dns_resolution: values.enable_dns_resolution,\n      enable_subdomain_enum: values.enable_subdomain_enum,\n      enable_web_crawling: values.enable_web_crawling,\n      enable_vulnerability_scan: values.enable_vulnerability_scan,\n      max_parallel_tasks: values.max_parallel_tasks,\n      timeout_per_step: values.timeout_per_step,\n      max_retries: values.max_retries,\n      continue_on_error: values.continue_on_error,\n      nmap_config: {\n        scan_type: values.nmap_scan_type,\n        timing: values.nmap_timing,\n        threads: values.nmap_threads,\n        host_timeout: values.nmap_host_timeout,\n        enable_service_detection: values.nmap_enable_service_detection,\n        enable_os_detection: values.nmap_enable_os_detection,\n        stealth_mode: values.nmap_stealth_mode,\n        aggressive_scan: values.nmap_aggressive_scan,\n      },\n      httpx_config: {\n        threads: values.httpx_threads,\n        timeout: values.httpx_timeout,\n        retries: values.httpx_retries,\n        user_agent_type: values.httpx_user_agent_type,\n        follow_redirects: values.httpx_follow_redirects,\n        tech_detect: values.httpx_tech_detect,\n        screenshot: values.httpx_screenshot,\n      },\n      nuclei_config: {\n        threads: values.nuclei_threads,\n        rate_limit: values.nuclei_rate_limit,\n        timeout: values.nuclei_timeout,\n        severity: values.nuclei_severity || ['medium', 'high', 'critical'],\n        templates: values.nuclei_templates || ['cves/', 'vulnerabilities/'],\n        user_agent_type: values.nuclei_user_agent_type,\n        update_templates: values.nuclei_update_templates,\n        passive_scan: values.nuclei_passive_scan,\n      },\n    } : undefined;\n\n    const taskRequest: CreateTaskRequest = {\n      name: `${values.task_type}扫描 - ${values.target.trim()}`, // 生成任务名称\n      target: values.target.trim(),\n      task_type: values.task_type, // 直接使用字符串值\n      config: config ? JSON.stringify(config) : undefined, // 转换为JSON字符串\n      total_steps: 7, // 默认步骤数\n    };\n\n    createTaskMutation.mutate(taskRequest);\n  };\n\n  const scanTypeDescriptions = {\n    [ScanType.Quick]: '快速扫描常见端口和高危漏洞，适合快速评估',\n    [ScanType.Standard]: '标准扫描，平衡速度和覆盖率',\n    [ScanType.Deep]: '深度扫描所有端口和漏洞，时间较长但最全面',\n    [ScanType.WebFocused]: '专注于Web应用扫描，包含爬虫和Web漏洞检测',\n    [ScanType.Custom]: '自定义扫描配置，可精确控制扫描参数',\n  };\n\n  return (\n    <div style={{ padding: 24 }}>\n      <Card>\n        {/* 页面标题 */}\n        <div style={{ marginBottom: 24 }}>\n          <Space>\n            <Button \n              icon={<ArrowLeftOutlined />} \n              onClick={() => navigate('/tasks')}\n            >\n              返回\n            </Button>\n            <div>\n              <Title level={3} style={{ margin: 0 }}>\n                创建扫描任务\n              </Title>\n              <Text type=\"secondary\">\n                配置扫描目标和参数，开始安全扫描\n              </Text>\n            </div>\n          </Space>\n        </div>\n\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={onFinish}\n          initialValues={{\n            task_type: ScanType.Standard,\n            scan_mode: ScanMode.Standard,\n            target_type: TargetType.Mixed,\n            enable_port_scan: true,\n            enable_service_detection: true,\n            enable_dns_resolution: true,\n            enable_subdomain_enum: true,\n            enable_web_crawling: true,\n            enable_vulnerability_scan: true,\n            max_parallel_tasks: 3,\n            timeout_per_step: 1800,\n            max_retries: 2,\n            continue_on_error: true,\n            nmap_scan_type: 'SYN',\n            nmap_timing: 4,\n            nmap_threads: 50,\n            nmap_host_timeout: 300,\n            nmap_enable_service_detection: true,\n            nmap_enable_os_detection: false,\n            nmap_stealth_mode: false,\n            nmap_aggressive_scan: false,\n            httpx_threads: 50,\n            httpx_timeout: 10,\n            httpx_retries: 2,\n            httpx_user_agent_type: UserAgentType.Desktop,\n            httpx_follow_redirects: true,\n            httpx_tech_detect: true,\n            httpx_screenshot: false,\n            nuclei_threads: 25,\n            nuclei_rate_limit: 150,\n            nuclei_timeout: 10,\n            nuclei_user_agent_type: UserAgentType.Desktop,\n            nuclei_update_templates: false,\n            nuclei_passive_scan: false,\n          }}\n        >\n          <Row gutter={24}>\n            <Col xs={24} lg={12}>\n              {/* 基础配置 */}\n              <Card title=\"基础配置\" size=\"small\" style={{ marginBottom: 16 }}>\n                <Form.Item\n                  label=\"扫描目标\"\n                  name=\"target\"\n                  rules={[\n                    { required: true, message: '请输入扫描目标' },\n                    {\n                      pattern: /^(https?:\\/\\/|[\\w.-]+|[\\d.]+|[\\da-fA-F:]+).*$/,\n                      message: '请输入有效的URL、域名或IP地址',\n                    },\n                  ]}\n                >\n                  <Input\n                    placeholder=\"例如: example.com, ***********, https://example.com\"\n                    size=\"large\"\n                  />\n                </Form.Item>\n\n                <Form.Item\n                  label=\"扫描类型\"\n                  name=\"task_type\"\n                  rules={[{ required: true, message: '请选择扫描类型' }]}\n                >\n                  <Select\n                    size=\"large\"\n                    onChange={(value) => setSelectedType(value)}\n                  >\n                    <Option value={ScanType.Quick}>快速扫描</Option>\n                    <Option value={ScanType.Standard}>标准扫描</Option>\n                    <Option value={ScanType.Deep}>深度扫描</Option>\n                    <Option value={ScanType.WebFocused}>Web专项扫描</Option>\n                    <Option value={ScanType.Custom}>自定义扫描</Option>\n                  </Select>\n                </Form.Item>\n\n                <Alert\n                  message={scanTypeDescriptions[selectedType]}\n                  type=\"info\"\n                  showIcon\n                  style={{ marginBottom: 16 }}\n                />\n              </Card>\n\n              {/* 高级配置开关 */}\n              <Card title=\"配置选项\" size=\"small\">\n                <Form.Item>\n                  <Space>\n                    <Switch\n                      checked={showAdvanced}\n                      onChange={setShowAdvanced}\n                    />\n                    <Text>显示高级配置</Text>\n                  </Space>\n                </Form.Item>\n              </Card>\n            </Col>\n\n            <Col xs={24} lg={12}>\n              {/* 扫描模块 */}\n              {showAdvanced && (\n                <Card title=\"扫描模块\" size=\"small\" style={{ marginBottom: 16 }}>\n                  <Row gutter={16}>\n                    <Col span={12}>\n                      <Form.Item name=\"enable_port_scan\" valuePropName=\"checked\">\n                        <Space>\n                          <Switch size=\"small\" />\n                          <Text>端口扫描</Text>\n                        </Space>\n                      </Form.Item>\n                    </Col>\n                    <Col span={12}>\n                      <Form.Item name=\"enable_service_detection\" valuePropName=\"checked\">\n                        <Space>\n                          <Switch size=\"small\" />\n                          <Text>服务检测</Text>\n                        </Space>\n                      </Form.Item>\n                    </Col>\n                    <Col span={12}>\n                      <Form.Item name=\"enable_dns_resolution\" valuePropName=\"checked\">\n                        <Space>\n                          <Switch size=\"small\" />\n                          <Text>DNS解析</Text>\n                        </Space>\n                      </Form.Item>\n                    </Col>\n                    <Col span={12}>\n                      <Form.Item name=\"enable_subdomain_enum\" valuePropName=\"checked\">\n                        <Space>\n                          <Switch size=\"small\" />\n                          <Text>子域名枚举</Text>\n                        </Space>\n                      </Form.Item>\n                    </Col>\n                    <Col span={12}>\n                      <Form.Item name=\"enable_web_crawling\" valuePropName=\"checked\">\n                        <Space>\n                          <Switch size=\"small\" />\n                          <Text>Web爬虫</Text>\n                        </Space>\n                      </Form.Item>\n                    </Col>\n                    <Col span={12}>\n                      <Form.Item name=\"enable_vulnerability_scan\" valuePropName=\"checked\">\n                        <Space>\n                          <Switch size=\"small\" />\n                          <Text>漏洞扫描</Text>\n                        </Space>\n                      </Form.Item>\n                    </Col>\n                  </Row>\n                </Card>\n              )}\n\n              {/* 全局参数 */}\n              {showAdvanced && (\n                <Card title=\"全局参数\" size=\"small\">\n                  <Row gutter={16}>\n                    <Col span={12}>\n                      <Form.Item label=\"并行任务数\" name=\"max_parallel_tasks\">\n                        <InputNumber min={1} max={10} style={{ width: '100%' }} />\n                      </Form.Item>\n                    </Col>\n                    <Col span={12}>\n                      <Form.Item label=\"步骤超时(秒)\" name=\"timeout_per_step\">\n                        <InputNumber min={300} max={7200} style={{ width: '100%' }} />\n                      </Form.Item>\n                    </Col>\n                    <Col span={12}>\n                      <Form.Item label=\"最大重试次数\" name=\"max_retries\">\n                        <InputNumber min={0} max={5} style={{ width: '100%' }} />\n                      </Form.Item>\n                    </Col>\n                    <Col span={12}>\n                      <Form.Item name=\"continue_on_error\" valuePropName=\"checked\">\n                        <Space>\n                          <Switch size=\"small\" />\n                          <Text>出错时继续</Text>\n                        </Space>\n                      </Form.Item>\n                    </Col>\n                  </Row>\n                </Card>\n              )}\n            </Col>\n          </Row>\n\n          {/* 工具配置 */}\n          {showAdvanced && (\n            <>\n              <Divider>工具配置</Divider>\n              \n              <Row gutter={24}>\n                {/* Nmap配置 */}\n                <Col xs={24} lg={8}>\n                  <Card title=\"Nmap配置\" size=\"small\">\n                    <Form.Item label=\"扫描类型\" name=\"nmap_scan_type\">\n                      <Select size=\"small\">\n                        <Option value=\"SYN\">SYN扫描</Option>\n                        <Option value=\"TCP\">TCP连接扫描</Option>\n                        <Option value=\"UDP\">UDP扫描</Option>\n                      </Select>\n                    </Form.Item>\n                    \n                    <Form.Item label=\"时序模板\" name=\"nmap_timing\">\n                      <Select size=\"small\">\n                        <Option value={3}>正常 (T3)</Option>\n                        <Option value={4}>快速 (T4)</Option>\n                        <Option value={5}>急速 (T5)</Option>\n                      </Select>\n                    </Form.Item>\n                    \n                    <Form.Item label=\"线程数\" name=\"nmap_threads\">\n                      <InputNumber min={1} max={100} size=\"small\" style={{ width: '100%' }} />\n                    </Form.Item>\n                    \n                    <Form.Item name=\"nmap_stealth_mode\" valuePropName=\"checked\">\n                      <Space>\n                        <Switch size=\"small\" />\n                        <Text>隐蔽模式</Text>\n                      </Space>\n                    </Form.Item>\n                  </Card>\n                </Col>\n\n                {/* HTTPx配置 */}\n                <Col xs={24} lg={8}>\n                  <Card title=\"HTTPx配置\" size=\"small\">\n                    <Form.Item label=\"线程数\" name=\"httpx_threads\">\n                      <InputNumber min={1} max={200} size=\"small\" style={{ width: '100%' }} />\n                    </Form.Item>\n                    \n                    <Form.Item label=\"超时时间\" name=\"httpx_timeout\">\n                      <InputNumber min={5} max={60} size=\"small\" style={{ width: '100%' }} />\n                    </Form.Item>\n                    \n                    <Form.Item label=\"User-Agent类型\" name=\"httpx_user_agent_type\">\n                      <Select size=\"small\">\n                        <Option value={UserAgentType.Desktop}>桌面浏览器</Option>\n                        <Option value={UserAgentType.Mobile}>移动浏览器</Option>\n                        <Option value={UserAgentType.Random}>随机</Option>\n                      </Select>\n                    </Form.Item>\n                    \n                    <Form.Item name=\"httpx_tech_detect\" valuePropName=\"checked\">\n                      <Space>\n                        <Switch size=\"small\" />\n                        <Text>技术检测</Text>\n                      </Space>\n                    </Form.Item>\n                  </Card>\n                </Col>\n\n                {/* Nuclei配置 */}\n                <Col xs={24} lg={8}>\n                  <Card title=\"Nuclei配置\" size=\"small\">\n                    <Form.Item label=\"线程数\" name=\"nuclei_threads\">\n                      <InputNumber min={1} max={100} size=\"small\" style={{ width: '100%' }} />\n                    </Form.Item>\n                    \n                    <Form.Item label=\"速率限制\" name=\"nuclei_rate_limit\">\n                      <InputNumber min={50} max={500} size=\"small\" style={{ width: '100%' }} />\n                    </Form.Item>\n                    \n                    <Form.Item label=\"严重性\" name=\"nuclei_severity\">\n                      <Select mode=\"multiple\" size=\"small\" placeholder=\"选择严重性级别\">\n                        <Option value=\"info\">信息</Option>\n                        <Option value=\"low\">低危</Option>\n                        <Option value=\"medium\">中危</Option>\n                        <Option value=\"high\">高危</Option>\n                        <Option value=\"critical\">严重</Option>\n                      </Select>\n                    </Form.Item>\n                    \n                    <Form.Item name=\"nuclei_update_templates\" valuePropName=\"checked\">\n                      <Space>\n                        <Switch size=\"small\" />\n                        <Text>更新模板</Text>\n                      </Space>\n                    </Form.Item>\n                  </Card>\n                </Col>\n              </Row>\n            </>\n          )}\n\n          {/* 操作按钮 */}\n          <div style={{ marginTop: 32, textAlign: 'right' }}>\n            <Space>\n              <Button onClick={() => navigate('/tasks')}>\n                取消\n              </Button>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                icon={<PlayCircleOutlined />}\n                loading={createTaskMutation.isPending}\n                size=\"large\"\n              >\n                开始扫描\n              </Button>\n            </Space>\n          </div>\n        </Form>\n      </Card>\n    </div>\n  );\n};\n\nexport default CreateTask;"], "names": ["Title", "Text", "Typography", "Option", "Select", "TextArea", "Input", "CreateTask", "navigate", "useNavigate", "message", "App", "form", "Form", "selectedType", "setSelectedType", "useState", "ScanType", "showAdvanced", "setShowAdvanced", "createTaskMutation", "useMutation", "taskApi", "response", "_a", "error", "onFinish", "values", "config", "taskRequest", "scanTypeDescriptions", "jsx", "Card", "Space", "<PERSON><PERSON>", "ArrowLeftOutlined", "jsxs", "ScanMode", "TargetType", "UserAgentType", "Row", "Col", "value", "<PERSON><PERSON>", "Switch", "InputNumber", "Fragment", "Divider", "PlayCircleOutlined"], "mappings": "2VAwBA,KAAM,CAAE,MAAAA,EAAO,KAAAC,CAAA,EAASC,EAClB,CAAE,OAAAC,GAAWC,EACb,CAAE,SAAAC,GAAaC,EAEfC,EAAuB,IAAM,CACjC,MAAMC,EAAWC,EAAA,EACX,CAAE,QAAAC,CAAA,EAAYC,EAAI,OAAA,EAClB,CAACC,CAAI,EAAIC,EAAK,QAAA,EACd,CAACC,EAAcC,CAAe,EAAIC,EAAAA,SAAmBC,EAAS,QAAQ,EACtE,CAACC,EAAcC,CAAe,EAAIH,EAAAA,SAAS,EAAK,EAGhDI,EAAqBC,EAAY,CACrC,WAAYC,EAAQ,WACpB,UAAYC,GAAa,OACvBb,EAAQ,QAAQ,QAAQ,EACxBF,EAAS,WAAUgB,EAAAD,EAAS,KAAK,OAAd,YAAAC,EAAoB,EAAE,EAAE,CAC7C,EACA,QAAUC,GAAe,CACvBf,EAAQ,MAAM,WAAae,EAAM,OAAO,CAC1C,CAAA,CACD,EAEKC,EAAYC,GAAgB,CAEhC,MAAMC,EAASV,EAAe,CAC5B,UAAWS,EAAO,UAClB,YAAaA,EAAO,YACpB,iBAAkBA,EAAO,iBACzB,yBAA0BA,EAAO,yBACjC,sBAAuBA,EAAO,sBAC9B,sBAAuBA,EAAO,sBAC9B,oBAAqBA,EAAO,oBAC5B,0BAA2BA,EAAO,0BAClC,mBAAoBA,EAAO,mBAC3B,iBAAkBA,EAAO,iBACzB,YAAaA,EAAO,YACpB,kBAAmBA,EAAO,kBAC1B,YAAa,CACX,UAAWA,EAAO,eAClB,OAAQA,EAAO,YACf,QAASA,EAAO,aAChB,aAAcA,EAAO,kBACrB,yBAA0BA,EAAO,8BACjC,oBAAqBA,EAAO,yBAC5B,aAAcA,EAAO,kBACrB,gBAAiBA,EAAO,oBAAA,EAE1B,aAAc,CACZ,QAASA,EAAO,cAChB,QAASA,EAAO,cAChB,QAASA,EAAO,cAChB,gBAAiBA,EAAO,sBACxB,iBAAkBA,EAAO,uBACzB,YAAaA,EAAO,kBACpB,WAAYA,EAAO,gBAAA,EAErB,cAAe,CACb,QAASA,EAAO,eAChB,WAAYA,EAAO,kBACnB,QAASA,EAAO,eAChB,SAAUA,EAAO,iBAAmB,CAAC,SAAU,OAAQ,UAAU,EACjE,UAAWA,EAAO,kBAAoB,CAAC,QAAS,kBAAkB,EAClE,gBAAiBA,EAAO,uBACxB,iBAAkBA,EAAO,wBACzB,aAAcA,EAAO,mBAAA,CACvB,EACE,OAEEE,EAAiC,CACrC,KAAM,GAAGF,EAAO,SAAS,QAAQA,EAAO,OAAO,MAAM,GACrD,OAAQA,EAAO,OAAO,KAAA,EACtB,UAAWA,EAAO,UAClB,OAAQC,EAAS,KAAK,UAAUA,CAAM,EAAI,OAC1C,YAAa,CAAA,EAGfR,EAAmB,OAAOS,CAAW,CACvC,EAEMC,EAAuB,CAC3B,CAACb,EAAS,KAAK,EAAG,uBAClB,CAACA,EAAS,QAAQ,EAAG,gBACrB,CAACA,EAAS,IAAI,EAAG,uBACjB,CAACA,EAAS,UAAU,EAAG,0BACvB,CAACA,EAAS,MAAM,EAAG,mBAAA,EAGrB,OACEc,MAAC,OAAI,MAAO,CAAE,QAAS,EAAA,EACrB,gBAACC,EAAA,CAEC,SAAA,CAAAD,EAAAA,IAAC,OAAI,MAAO,CAAE,aAAc,EAAA,EAC1B,gBAACE,EAAA,CACC,SAAA,CAAAF,EAAAA,IAACG,EAAA,CACC,WAAOC,EAAA,EAAkB,EACzB,QAAS,IAAM3B,EAAS,QAAQ,EACjC,SAAA,IAAA,CAAA,SAGA,MAAA,CACC,SAAA,CAAAuB,EAAAA,IAAC/B,EAAA,CAAM,MAAO,EAAG,MAAO,CAAE,OAAQ,CAAA,EAAK,SAAA,QAAA,CAEvC,EACA+B,EAAAA,IAAC9B,EAAA,CAAK,KAAK,YAAY,SAAA,kBAAA,CAEvB,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CACF,EAEAmC,EAAAA,KAACvB,EAAA,CACC,KAAAD,EACA,OAAO,WACP,SAAAc,EACA,cAAe,CACb,UAAWT,EAAS,SACpB,UAAWoB,EAAS,SACpB,YAAaC,EAAW,MACxB,iBAAkB,GAClB,yBAA0B,GAC1B,sBAAuB,GACvB,sBAAuB,GACvB,oBAAqB,GACrB,0BAA2B,GAC3B,mBAAoB,EACpB,iBAAkB,KAClB,YAAa,EACb,kBAAmB,GACnB,eAAgB,MAChB,YAAa,EACb,aAAc,GACd,kBAAmB,IACnB,8BAA+B,GAC/B,yBAA0B,GAC1B,kBAAmB,GACnB,qBAAsB,GACtB,cAAe,GACf,cAAe,GACf,cAAe,EACf,sBAAuBC,EAAc,QACrC,uBAAwB,GACxB,kBAAmB,GACnB,iBAAkB,GAClB,eAAgB,GAChB,kBAAmB,IACnB,eAAgB,GAChB,uBAAwBA,EAAc,QACtC,wBAAyB,GACzB,oBAAqB,EAAA,EAGvB,SAAA,CAAAH,EAAAA,KAACI,EAAA,CAAI,OAAQ,GACX,SAAA,CAAAJ,EAAAA,KAACK,EAAA,CAAI,GAAI,GAAI,GAAI,GAEf,SAAA,CAAAL,EAAAA,KAACJ,EAAA,CAAK,MAAM,OAAO,KAAK,QAAQ,MAAO,CAAE,aAAc,EAAA,EACrD,SAAA,CAAAD,EAAAA,IAAClB,EAAK,KAAL,CACC,MAAM,OACN,KAAK,SACL,MAAO,CACL,CAAE,SAAU,GAAM,QAAS,SAAA,EAC3B,CACE,QAAS,gDACT,QAAS,mBAAA,CACX,EAGF,SAAAkB,EAAAA,IAACzB,EAAA,CACC,YAAY,oDACZ,KAAK,OAAA,CAAA,CACP,CAAA,EAGFyB,EAAAA,IAAClB,EAAK,KAAL,CACC,MAAM,OACN,KAAK,YACL,MAAO,CAAC,CAAE,SAAU,GAAM,QAAS,UAAW,EAE9C,SAAAuB,EAAAA,KAAChC,EAAA,CACC,KAAK,QACL,SAAWsC,GAAU3B,EAAgB2B,CAAK,EAE1C,SAAA,CAAAX,EAAAA,IAAC5B,EAAA,CAAO,MAAOc,EAAS,MAAO,SAAA,OAAI,EACnCc,EAAAA,IAAC5B,EAAA,CAAO,MAAOc,EAAS,SAAU,SAAA,OAAI,EACtCc,EAAAA,IAAC5B,EAAA,CAAO,MAAOc,EAAS,KAAM,SAAA,OAAI,EAClCc,EAAAA,IAAC5B,EAAA,CAAO,MAAOc,EAAS,WAAY,SAAA,UAAO,EAC3Cc,EAAAA,IAAC5B,EAAA,CAAO,MAAOc,EAAS,OAAQ,SAAA,OAAA,CAAK,CAAA,CAAA,CAAA,CACvC,CAAA,EAGFc,EAAAA,IAACY,EAAA,CACC,QAASb,EAAqBhB,CAAY,EAC1C,KAAK,OACL,SAAQ,GACR,MAAO,CAAE,aAAc,EAAA,CAAG,CAAA,CAC5B,EACF,EAGAiB,EAAAA,IAACC,EAAA,CAAK,MAAM,OAAO,KAAK,QACtB,SAAAD,EAAAA,IAAClB,EAAK,KAAL,CACC,SAAAuB,EAAAA,KAACH,EAAA,CACC,SAAA,CAAAF,EAAAA,IAACa,EAAA,CACC,QAAS1B,EACT,SAAUC,CAAA,CAAA,EAEZY,EAAAA,IAAC9B,GAAK,SAAA,QAAA,CAAM,CAAA,CAAA,CACd,EACF,CAAA,CACF,CAAA,EACF,EAEAmC,EAAAA,KAACK,EAAA,CAAI,GAAI,GAAI,GAAI,GAEd,SAAA,CAAAvB,GACCa,EAAAA,IAACC,EAAA,CAAK,MAAM,OAAO,KAAK,QAAQ,MAAO,CAAE,aAAc,EAAA,EACrD,SAAAI,EAAAA,KAACI,EAAA,CAAI,OAAQ,GACX,SAAA,CAAAT,EAAAA,IAACU,EAAA,CAAI,KAAM,GACT,SAAAV,EAAAA,IAAClB,EAAK,KAAL,CAAU,KAAK,mBAAmB,cAAc,UAC/C,SAAAuB,OAACH,EAAA,CACC,SAAA,CAAAF,EAAAA,IAACa,EAAA,CAAO,KAAK,OAAA,CAAQ,EACrBb,EAAAA,IAAC9B,GAAK,SAAA,MAAA,CAAI,CAAA,CAAA,CACZ,EACF,EACF,EACA8B,EAAAA,IAACU,EAAA,CAAI,KAAM,GACT,SAAAV,EAAAA,IAAClB,EAAK,KAAL,CAAU,KAAK,2BAA2B,cAAc,UACvD,gBAACoB,EAAA,CACC,SAAA,CAAAF,EAAAA,IAACa,EAAA,CAAO,KAAK,OAAA,CAAQ,EACrBb,EAAAA,IAAC9B,GAAK,SAAA,MAAA,CAAI,CAAA,CAAA,CACZ,EACF,EACF,EACA8B,EAAAA,IAACU,EAAA,CAAI,KAAM,GACT,SAAAV,EAAAA,IAAClB,EAAK,KAAL,CAAU,KAAK,wBAAwB,cAAc,UACpD,gBAACoB,EAAA,CACC,SAAA,CAAAF,EAAAA,IAACa,EAAA,CAAO,KAAK,OAAA,CAAQ,EACrBb,EAAAA,IAAC9B,GAAK,SAAA,OAAA,CAAK,CAAA,CAAA,CACb,EACF,EACF,EACA8B,EAAAA,IAACU,EAAA,CAAI,KAAM,GACT,SAAAV,EAAAA,IAAClB,EAAK,KAAL,CAAU,KAAK,wBAAwB,cAAc,UACpD,gBAACoB,EAAA,CACC,SAAA,CAAAF,EAAAA,IAACa,EAAA,CAAO,KAAK,OAAA,CAAQ,EACrBb,EAAAA,IAAC9B,GAAK,SAAA,OAAA,CAAK,CAAA,CAAA,CACb,EACF,EACF,EACA8B,EAAAA,IAACU,EAAA,CAAI,KAAM,GACT,SAAAV,EAAAA,IAAClB,EAAK,KAAL,CAAU,KAAK,sBAAsB,cAAc,UAClD,gBAACoB,EAAA,CACC,SAAA,CAAAF,EAAAA,IAACa,EAAA,CAAO,KAAK,OAAA,CAAQ,EACrBb,EAAAA,IAAC9B,GAAK,SAAA,OAAA,CAAK,CAAA,CAAA,CACb,EACF,EACF,EACA8B,EAAAA,IAACU,EAAA,CAAI,KAAM,GACT,SAAAV,EAAAA,IAAClB,EAAK,KAAL,CAAU,KAAK,4BAA4B,cAAc,UACxD,gBAACoB,EAAA,CACC,SAAA,CAAAF,EAAAA,IAACa,EAAA,CAAO,KAAK,OAAA,CAAQ,EACrBb,EAAAA,IAAC9B,GAAK,SAAA,MAAA,CAAI,CAAA,CAAA,CACZ,EACF,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CACF,EAIDiB,GACCa,EAAAA,IAACC,EAAA,CAAK,MAAM,OAAO,KAAK,QACtB,SAAAI,EAAAA,KAACI,EAAA,CAAI,OAAQ,GACX,SAAA,CAAAT,EAAAA,IAACU,EAAA,CAAI,KAAM,GACT,SAAAV,EAAAA,IAAClB,EAAK,KAAL,CAAU,MAAM,QAAQ,KAAK,qBAC5B,eAACgC,EAAA,CAAY,IAAK,EAAG,IAAK,GAAI,MAAO,CAAE,MAAO,MAAA,CAAO,CAAG,CAAA,CAC1D,CAAA,CACF,EACAd,EAAAA,IAACU,EAAA,CAAI,KAAM,GACT,SAAAV,EAAAA,IAAClB,EAAK,KAAL,CAAU,MAAM,UAAU,KAAK,mBAC9B,eAACgC,EAAA,CAAY,IAAK,IAAK,IAAK,KAAM,MAAO,CAAE,MAAO,MAAA,CAAO,CAAG,CAAA,CAC9D,CAAA,CACF,EACAd,EAAAA,IAACU,EAAA,CAAI,KAAM,GACT,SAAAV,EAAAA,IAAClB,EAAK,KAAL,CAAU,MAAM,SAAS,KAAK,cAC7B,eAACgC,EAAA,CAAY,IAAK,EAAG,IAAK,EAAG,MAAO,CAAE,MAAO,MAAA,CAAO,CAAG,CAAA,CACzD,CAAA,CACF,EACAd,EAAAA,IAACU,EAAA,CAAI,KAAM,GACT,SAAAV,EAAAA,IAAClB,EAAK,KAAL,CAAU,KAAK,oBAAoB,cAAc,UAChD,gBAACoB,EAAA,CACC,SAAA,CAAAF,EAAAA,IAACa,EAAA,CAAO,KAAK,OAAA,CAAQ,EACrBb,EAAAA,IAAC9B,GAAK,SAAA,OAAA,CAAK,CAAA,CAAA,CACb,EACF,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CAEJ,CAAA,EACF,EAGCiB,GACCkB,EAAAA,KAAAU,WAAA,CACE,SAAA,CAAAf,EAAAA,IAACgB,GAAQ,SAAA,MAAA,CAAI,EAEbX,EAAAA,KAACI,EAAA,CAAI,OAAQ,GAEX,SAAA,CAAAT,EAAAA,IAACU,EAAA,CAAI,GAAI,GAAI,GAAI,EACf,gBAACT,EAAA,CAAK,MAAM,SAAS,KAAK,QACxB,SAAA,CAAAD,EAAAA,IAAClB,EAAK,KAAL,CAAU,MAAM,OAAO,KAAK,iBAC3B,SAAAuB,EAAAA,KAAChC,EAAA,CAAO,KAAK,QACX,SAAA,CAAA2B,EAAAA,IAAC5B,EAAA,CAAO,MAAM,MAAM,SAAA,QAAK,EACzB4B,EAAAA,IAAC5B,EAAA,CAAO,MAAM,MAAM,SAAA,UAAO,EAC3B4B,EAAAA,IAAC5B,EAAA,CAAO,MAAM,MAAM,SAAA,OAAA,CAAK,CAAA,CAAA,CAC3B,CAAA,CACF,EAEA4B,EAAAA,IAAClB,EAAK,KAAL,CAAU,MAAM,OAAO,KAAK,cAC3B,SAAAuB,EAAAA,KAAChC,EAAA,CAAO,KAAK,QACX,SAAA,CAAA2B,EAAAA,IAAC5B,EAAA,CAAO,MAAO,EAAG,SAAA,UAAO,EACzB4B,EAAAA,IAAC5B,EAAA,CAAO,MAAO,EAAG,SAAA,UAAO,EACzB4B,EAAAA,IAAC5B,EAAA,CAAO,MAAO,EAAG,SAAA,SAAA,CAAO,CAAA,CAAA,CAC3B,CAAA,CACF,EAEA4B,EAAAA,IAAClB,EAAK,KAAL,CAAU,MAAM,MAAM,KAAK,eAC1B,SAAAkB,EAAAA,IAACc,EAAA,CAAY,IAAK,EAAG,IAAK,IAAK,KAAK,QAAQ,MAAO,CAAE,MAAO,OAAO,CAAG,EACxE,EAEAd,EAAAA,IAAClB,EAAK,KAAL,CAAU,KAAK,oBAAoB,cAAc,UAChD,SAAAuB,EAAAA,KAACH,EAAA,CACC,SAAA,CAAAF,EAAAA,IAACa,EAAA,CAAO,KAAK,OAAA,CAAQ,EACrBb,EAAAA,IAAC9B,GAAK,SAAA,MAAA,CAAI,CAAA,CAAA,CACZ,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CACF,EAGA8B,EAAAA,IAACU,EAAA,CAAI,GAAI,GAAI,GAAI,EACf,SAAAL,EAAAA,KAACJ,EAAA,CAAK,MAAM,UAAU,KAAK,QACzB,SAAA,CAAAD,EAAAA,IAAClB,EAAK,KAAL,CAAU,MAAM,MAAM,KAAK,gBAC1B,eAACgC,EAAA,CAAY,IAAK,EAAG,IAAK,IAAK,KAAK,QAAQ,MAAO,CAAE,MAAO,QAAU,EACxE,EAEAd,EAAAA,IAAClB,EAAK,KAAL,CAAU,MAAM,OAAO,KAAK,gBAC3B,SAAAkB,EAAAA,IAACc,EAAA,CAAY,IAAK,EAAG,IAAK,GAAI,KAAK,QAAQ,MAAO,CAAE,MAAO,OAAO,CAAG,EACvE,EAEAd,EAAAA,IAAClB,EAAK,KAAL,CAAU,MAAM,eAAe,KAAK,wBACnC,SAAAuB,EAAAA,KAAChC,EAAA,CAAO,KAAK,QACX,SAAA,CAAA2B,EAAAA,IAAC5B,EAAA,CAAO,MAAOoC,EAAc,QAAS,SAAA,QAAK,EAC3CR,EAAAA,IAAC5B,EAAA,CAAO,MAAOoC,EAAc,OAAQ,SAAA,QAAK,EAC1CR,EAAAA,IAAC5B,EAAA,CAAO,MAAOoC,EAAc,OAAQ,SAAA,IAAA,CAAE,CAAA,CAAA,CACzC,CAAA,CACF,EAEAR,EAAAA,IAAClB,EAAK,KAAL,CAAU,KAAK,oBAAoB,cAAc,UAChD,SAAAuB,EAAAA,KAACH,EAAA,CACC,SAAA,CAAAF,EAAAA,IAACa,EAAA,CAAO,KAAK,OAAA,CAAQ,EACrBb,EAAAA,IAAC9B,GAAK,SAAA,MAAA,CAAI,CAAA,CAAA,CACZ,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CACF,EAGA8B,EAAAA,IAACU,EAAA,CAAI,GAAI,GAAI,GAAI,EACf,SAAAL,EAAAA,KAACJ,EAAA,CAAK,MAAM,WAAW,KAAK,QAC1B,SAAA,CAAAD,EAAAA,IAAClB,EAAK,KAAL,CAAU,MAAM,MAAM,KAAK,iBAC1B,eAACgC,EAAA,CAAY,IAAK,EAAG,IAAK,IAAK,KAAK,QAAQ,MAAO,CAAE,MAAO,QAAU,EACxE,EAEAd,EAAAA,IAAClB,EAAK,KAAL,CAAU,MAAM,OAAO,KAAK,oBAC3B,SAAAkB,EAAAA,IAACc,EAAA,CAAY,IAAK,GAAI,IAAK,IAAK,KAAK,QAAQ,MAAO,CAAE,MAAO,OAAO,CAAG,EACzE,EAEAd,EAAAA,IAAClB,EAAK,KAAL,CAAU,MAAM,MAAM,KAAK,kBAC1B,SAAAuB,EAAAA,KAAChC,GAAO,KAAK,WAAW,KAAK,QAAQ,YAAY,UAC/C,SAAA,CAAA2B,EAAAA,IAAC5B,EAAA,CAAO,MAAM,OAAO,SAAA,KAAE,EACvB4B,EAAAA,IAAC5B,EAAA,CAAO,MAAM,MAAM,SAAA,KAAE,EACtB4B,EAAAA,IAAC5B,EAAA,CAAO,MAAM,SAAS,SAAA,KAAE,EACzB4B,EAAAA,IAAC5B,EAAA,CAAO,MAAM,OAAO,SAAA,KAAE,EACvB4B,EAAAA,IAAC5B,EAAA,CAAO,MAAM,WAAW,SAAA,IAAA,CAAE,CAAA,CAAA,CAC7B,CAAA,CACF,EAEA4B,EAAAA,IAAClB,EAAK,KAAL,CAAU,KAAK,0BAA0B,cAAc,UACtD,SAAAuB,EAAAA,KAACH,EAAA,CACC,SAAA,CAAAF,EAAAA,IAACa,EAAA,CAAO,KAAK,OAAA,CAAQ,EACrBb,EAAAA,IAAC9B,GAAK,SAAA,MAAA,CAAI,CAAA,CAAA,CACZ,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CACF,CAAA,EACF,EAIF8B,EAAAA,IAAC,MAAA,CAAI,MAAO,CAAE,UAAW,GAAI,UAAW,OAAA,EACtC,SAAAK,EAAAA,KAACH,EAAA,CACC,SAAA,CAAAF,MAACG,GAAO,QAAS,IAAM1B,EAAS,QAAQ,EAAG,SAAA,KAE3C,EACAuB,EAAAA,IAACG,EAAA,CACC,KAAK,UACL,SAAS,SACT,WAAOc,EAAA,EAAmB,EAC1B,QAAS5B,EAAmB,UAC5B,KAAK,QACN,SAAA,MAAA,CAAA,CAED,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CACF,CAEJ"}