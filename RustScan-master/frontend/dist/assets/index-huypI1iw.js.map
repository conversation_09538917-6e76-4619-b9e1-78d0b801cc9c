{"version": 3, "file": "index-huypI1iw.js", "sources": ["../../src/pages/Settings/index.tsx"], "sourcesContent": ["import React from 'react';\nimport { Card, Typography, Empty } from 'antd';\nimport { SettingOutlined } from '@ant-design/icons';\n\nconst { Title } = Typography;\n\nconst Settings: React.FC = () => {\n  return (\n    <div style={{ padding: 24 }}>\n      <Card>\n        <Title level={3}>系统设置</Title>\n        <Empty\n          image={<SettingOutlined style={{ fontSize: 64, color: '#ccc' }} />}\n          description={\n            <div>\n              <div>系统设置功能正在开发中</div>\n              <div style={{ color: '#999', fontSize: 12 }}>\n                将提供系统配置、用户管理、安全设置等功能\n              </div>\n            </div>\n          }\n        />\n      </Card>\n    </div>\n  );\n};\n\nexport default Settings;"], "names": ["Title", "Typography", "Settings", "jsx", "Card", "Empty", "SettingOutlined"], "mappings": "6JAIA,KAAM,CAAE,MAAAA,GAAUC,EAEZC,EAAqB,IAEvBC,MAAC,OAAI,MAAO,CAAE,QAAS,EAAA,EACrB,gBAACC,EAAA,CACC,SAAA,CAAAD,EAAAA,IAACH,EAAA,CAAM,MAAO,EAAG,SAAA,OAAI,EACrBG,EAAAA,IAACE,EAAA,CACC,YAAQC,EAAA,CAAgB,MAAO,CAAE,SAAU,GAAI,MAAO,MAAA,EAAU,EAChE,mBACG,MAAA,CACC,SAAA,CAAAH,EAAAA,IAAC,OAAI,SAAA,aAAA,CAAW,EAChBA,EAAAA,IAAC,OAAI,MAAO,CAAE,MAAO,OAAQ,SAAU,EAAA,EAAM,SAAA,sBAAA,CAE7C,CAAA,CAAA,CACF,CAAA,CAAA,CAEJ,CAAA,CACF,CAAA,CACF"}